{"permissions": {"allow": ["Bash(npm run typecheck:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(npm run build:*)", "Bash(npx prisma db push:*)", "Bash(npx prisma db seed:*)", "Bash(pnpm start:dev:*)", "Bash(pnpm build:*)", "Bash(timeout 30 pnpm start:dev)", "<PERSON><PERSON>(gtimeout:*)", "Bash(pnpm install:*)", "Bash(npx prisma generate:*)", "Bash(node fix-schema-fields.js)", "Bash(rm:*)", "Bash(npm run lint:*)", "Bash(npm run start:*)", "<PERSON><PERSON>(pkill:*)", "Bash(kill:*)", "<PERSON><PERSON>(curl:*)", "Bash(lsof:*)", "Bash(node:*)", "Bash(rg:*)", "Bash(npx nest start:*)", "Bash(pnpm frontend:dev:*)", "Bash(pnpm dev:*)", "Bash(pnpm backend:dev:*)", "<PERSON><PERSON>(touch:*)", "Bash(npx tsc:*)", "Bash(pnpm run:*)", "Bash(NODE_OPTIONS=\"--max-old-space-size=8192\" pnpm build)", "Bash(NODE_OPTIONS=\"--max-old-space-size=16384\" npx tsc --noEmit --skipLibCheck src/modules/ai/models/agents/ai-agents.service.ts)", "Bash(NODE_OPTIONS=\"--max-old-space-size=12288\" timeout 60s pnpm build 2 >& 1)", "Bash(NODE_OPTIONS=\"--max-old-space-size=12288\" pnpm build 2 >& 1)", "<PERSON><PERSON>(mv:*)", "Bash(pnpm prisma generate:*)", "Bash(ls:*)"], "deny": []}}