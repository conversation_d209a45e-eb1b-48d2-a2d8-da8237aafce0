---
description: 
globs: 
alwaysApply: true
---
# AI 系統統一指南

本文件概述了 HorizAI SaaS 專案中所有 AI 功能的統一架構和操作指南。它強調供應商無關性 (Provider-Agnosticism)、多機器人 (Multi-Bot) 能力、多範疇 (Multi-Scope) 設計、清晰的金鑰管理 (Key Management)、健全的組態設定 (Configuration)、整合的成本控制 (Cost Control) 以及 AI 模型互動的最佳實踐。所有 AI 相關的開發工作都必須遵守這些原則。

---

## 1. 核心原則

1.  **供應商無關性 (Provider-Agnostic)**：系統必須支援多個 AI 供應商（OpenAI、Anthropic/Claude、Google Gemini、其他與 OpenAI 相容的服務），而無需為新增供應商修改核心邏輯。
2.  **多機器人 (Multi-Bot)**：系統允許建立和管理多個不同的 AI 機器人實例，每個實例都能滿足特定的任務或組態需求。
3.  **多範疇 (Multi-Scope)**：機器人和組態設定根據其操作範疇明確劃分：
    *   `SYSTEM` (系統級)：用於後端/管理性的 AI 功能。不與特定租戶或工作空間綁定。這些機器人通常連結到 `AiSystemFeatureDefinition` 中定義的系統級功能。
    *   `TENANT_TEMPLATE` (租戶範本)：可由租戶管理員設定的機器人範本，可被其下的工作空間繼承。
    *   `WORKSPACE` (工作空間級)：特定於使用者工作空間內設定和使用的機器人。
4.  **集中式與機器人特定的金鑰管理 (Centralized and Bot-Specific Key Management)**：API 金鑰集中管理，但**每個機器人實例都必須明確且強制性地連結到一個 `AiKey` 記錄**以進行操作。
5.  **分層組態設定 (Layered Configuration)**：
    *   **全域設定 (Global Settings)**：系統級的 AI 操作參數（例如：AI 功能總開關、全域配額）。
    *   **系統功能定義 (System Feature Definition)**：集中定義系統中可用的 AI 功能及其基本屬性。
    *   **功能組態 (Feature Configuration)**：個別已定義功能的啟用狀態與機器人指派。
    *   **機器人組態 (Bot Configuration)**：機器人的詳細行為、模型選擇及其指定的 API 金鑰。
    *   **模型組態 (Model Configuration)**：支援的 AI 模型集中列表，包含其用於成本計算的定價。
6.  **整合的成本控制 (Integrated Cost Control)**：用於追蹤 AI 使用量（Token 數、呼叫次數）和估算成本的機制，並結合配額管理。
7.  **遵循最佳實踐 (Adherence to Best Practices)**：與 AI 模型的互動應遵循提示工程 (prompt engineering)、模型選擇和迭代優化的既定最佳實踐，以最大化品質和效率。

---

## 2. 資料模型 (Prisma Schema 定義)

> **重要事項：** 所有列舉型別 (enum)、資料傳輸物件 (DTO)、服務/控制器參數、型別及欄位名稱，皆必須嚴格對應 Prisma schema。所有欄位名稱必須使用蛇形命名法 (snake_case)。所有列舉型別/型別必須對應 @prisma/client。任何對 schema、列舉型別、DTO 或服務/控制器參數的變更，都必須反映在此規則檔案中，以防止規格與實作之間產生偏差，並確保兩者完全一致。

以下為核心的 Prisma 模型 schema。所有外鍵關聯應在適用情況下，實作適當的 `onDelete` 與 `onUpdate` 層疊行為。

### 2.1. `AiKey` - API 金鑰管理

```prisma
model AiKey {
  id          String   @id @default(cuid())
  name        String   // 金鑰的易讀名稱 (例如："OpenAI 金鑰 - 主要")
  api_key     String   // 實際的 API 金鑰，加密儲存
  provider    String   // AI 供應商識別碼 (例如："openai", "anthropic", "google-gemini", "openai-compatible")
  api_url     String?  // 選用：API 供應商的基礎 URL，用於代理或 OpenAI 相容服務
  is_enabled  Boolean  @default(true)
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // 關聯
  ai_bots     AiBot[] // 使用此金鑰的機器人
  usage_logs AiUsageLog[]

  @@map("ai_keys")
}
```

### 2.2. `AiModel` - AI 模型定義與定價

```prisma
model AiModel {
  id                             String   @id @default(cuid())
  provider                       String   // AI 供應商識別碼 (例如："openai", "anthropic")
  model_name                     String   // API 特定的模型識別碼 (例如："gpt-4-turbo", "claude-3-opus-20240229")
  display_name                   String   // 易讀的顯示名稱 (例如："GPT-4 Turbo", "Claude 3 Opus")
  is_enabled                     Boolean  @default(true) // 此模型是否可在系統中選擇
  input_price_per_1k_tokens      Decimal  @default(0)// 每 1000 個輸入 token 的成本
  output_price_per_1k_tokens     Decimal  @default(0)// 每 1000 個輸出 token 的成本
  currency                       String   @default("USD")// 貨幣代碼 (例如："USD")
  price_last_updated_at          DateTime @updatedAt // 定價資訊的最後更新時間
  context_window_tokens          Int?     // 此模型的最大上下文視窗大小 (token 數)
  notes                          String?  // 關於模型或其定價的額外備註
  created_at                     DateTime @default(now())
  updated_at                     DateTime @updatedAt

  // 關聯
  ai_bots                        AiBot[] // 設定使用此模型的機器人

  @@unique([provider, model_name])
  @@map("ai_models")
}
```

### 2.3. `AiSystemFeatureDefinition` - AI 系統功能定義 (取代前端 AI_FEATURES 常數)

```prisma
model AiSystemFeatureDefinition {
  id                String   @id @default(cuid())
  key               String   @unique // 例如："promptOptimization", "writingAssistant"。這是此功能的標準識別碼。
  name              String   // 易讀的名稱，例如："AI 提示詞優化"
  description       String?  // 功能的詳細描述
  is_system_level   Boolean  @default(false) // 若為 true，表示此功能由 SYSTEM 範疇的機器人提供。
                                          // 若為 false，表示此功能可由租戶/工作空間設定，且在 AiFeatureConfig 中總是需要明確的 bot_id。
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt

  // 關聯：一個系統功能定義可以有一個對應的組態實例
  feature_config    AiFeatureConfig? @relation("FeatureDefinitionToConfig")

  @@map("ai_system_feature_definitions")
}
```

### 2.4. `AiBot` - AI 助理 (機器人) 組態設定

```prisma
enum AiBotScope {
  SYSTEM          // 系統級
  TENANT_TEMPLATE // 租戶範本
  WORKSPACE       // 工作空間級
}

enum AiBotProviderType { // 用於指導 UI 和工廠模式，實際 API 呼叫時的供應商字串來自 AiModel
  OPENAI
  CLAUDE
  GEMINI
  OPENAI_COMPATIBLE // OpenAI 相容
}

enum AiBotResponseFormat {
  TEXT        // 文字
  JSON_OBJECT // JSON 物件
}

model AiBot {
  id                          String             @id @default(cuid())
  name                        String             // 對於 SYSTEM 級機器人，此名稱應理想地與其服務的 AiSystemFeatureDefinition.name 相符
  description                 String?            // 描述
  scope                       AiBotScope         // 範疇
  provider_type               AiBotProviderType  // 指導供應商選擇邏輯，實際供應商透過 AiModel 識別
  model_id                    String             // 外鍵，指向 AiModel.id
  key_id                      String             // 外鍵，指向 AiKey.id，不可為空 (NOT NULL)
  provider_config_override    Json?              // JSON 格式，用於特定供應商的非金鑰覆寫設定 (例如：OpenAI 相容服務的 baseURL)
  system_prompt               String?            @db.Text // 系統提示詞
  temperature                 Float?             @default(0.7) // 溫度參數
  max_tokens                  Int?               // 此機器人請求的最大輸出 token 數 (依供應商而定)
  response_format             AiBotResponseFormat @default(TEXT) // 回應格式
  is_enabled                  Boolean            @default(true)  // 是否啟用
  is_template                 Boolean            @default(false) // 此機器人是否為範本 (用於 TENANT_TEMPLATE 範疇)
  scene                       String?            // 對於 SYSTEM 級機器人，此欄位必須與其服務的 AiSystemFeatureDefinition.key 相符以建立連結。對於其他範疇，則為一般分類。

  tenant_id                   String?            // TENANT_TEMPLATE 和 WORKSPACE 範疇下為必填
  workspace_id                String?            // WORKSPACE 範疇下為必填

  created_at                  DateTime           @default(now())
  updated_at                  DateTime           @updatedAt
  created_by                  String // User.id
  updated_by                  String? // User.id

  // 關聯
  model                       AiModel            @relation(fields: [model_id], references: [id])
  key                         AiKey              @relation(fields: [key_id], references: [id])
  tenant                      Tenant?            @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  workspace                   Workspace?         @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
  feature_configs             AiFeatureConfig[]  // 此機器人被指派執行的功能 (主要用於非 SYSTEM 級機器人)
  creator                     User               @relation("CreatedBots", fields: [created_by], references: [id])
  updater                     User?              @relation("UpdatedBots", fields: [updated_by], references: [id])
  usage_logs                  AiUsageLog[]

  @@index([scope, tenant_id, workspace_id])
  @@map("ai_bots")
}
```

### 2.5. `AiFeatureConfig` - AI 功能啟用與機器人綁定

```prisma
model AiFeatureConfig {
  id                         String    @id @default(cuid())
  // feature_key             String    @unique // 此欄位已移除，改用 feature_definition_id

  feature_definition_id      String    @unique // 外鍵，指向 AiSystemFeatureDefinition.id
  feature_definition         AiSystemFeatureDefinition @relation("FeatureDefinitionToConfig", fields: [feature_definition_id], references: [id])

  is_enabled                 Boolean   @default(false) // 是否啟用
  bot_id                     String?   // 外鍵，指向 AiBot.id。
                                      // 對於 AiSystemFeatureDefinition.is_system_level = false 的功能，此欄位由管理員設定。
                                      // 對於 AiSystemFeatureDefinition.is_system_level = true 的功能，此欄位通常為 NULL 或被邏輯忽略，
                                      // 因為系統會透過比對 AiBot.scope='SYSTEM' 且 AiBot.scene = AiSystemFeatureDefinition.key 來隱含決定 SYSTEM Bot。
  created_at                 DateTime  @default(now())
  updated_at                 DateTime  @updatedAt

  // 關聯
  bot                        AiBot?    @relation(fields: [bot_id], references: [id], onDelete: SetNull) // 若機器人被刪除，功能可能被禁用或解除指派

  @@map("ai_feature_configs") // 維持原表名
}
```

### 2.6. `AiGlobalSetting` - AI 系統全域設定

```prisma
model AiGlobalSetting {
  id                             String   @id @default(cuid()) // 理想情況下應為單一記錄；應用程式邏輯確保此點。
  is_ai_globally_enabled         Boolean  @default(true) // 所有 AI 功能的總開關
  global_monthly_quota_tokens    BigInt?  // 選用：整個系統每月的總 token 配額
  global_monthly_quota_calls     Int?     // 選用：整個系統每月的總 API 呼叫配額
  // 在此處新增其他真正的全域設定 (例如：全域內容過濾等級)
  created_at                     DateTime @default(now())
  updated_at                     DateTime @updatedAt

  @@map("ai_global_settings")
}
```

### 2.7. `AiUsageLog` - AI 使用日誌 (用於成本控制與分析)

```prisma
model AiUsageLog {
  id                  String   @id @default(cuid())
  user_id             String?  // 發起導致 AI 呼叫的使用者 ID
  tenant_id           String?  // 與呼叫相關的租戶 ID
  bot_id              String   // 使用的 AiBot 實例 ID
  feature_key         String?  // 叫用的 AiSystemFeatureDefinition.key
  api_key_id          String   // 呼叫使用的 AiKey ID
  provider            String   // 使用的供應商 (例如："openai", "anthropic")
  model_name          String   // 使用的特定模型名稱 (例如："gpt-4-turbo")
  input_tokens        Int      // 輸入 token 數
  output_tokens       Int      // 輸出 token 數
  call_count          Int      @default(1) // 呼叫次數
  estimated_cost      Decimal  // 根據 AiModel 定價和使用的 token 計算的估計成本
  request_timestamp   DateTime @default(now()) // 請求時間戳
  response_timestamp  DateTime? // 回應時間戳
  is_success          Boolean  // 是否成功
  error_message       String?  @db.Text // 錯誤訊息
  created_at          DateTime @default(now())

  // 關聯
  bot                 AiBot    @relation(fields: [bot_id], references: [id])
  key                 AiKey    @relation(fields: [api_key_id], references: [id])
  // user             User?    @relation(fields: [user_id], references: [id])
  // tenant           Tenant?  @relation(fields: [tenant_id], references: [id])

  @@index([user_id])
  @@index([tenant_id])
  @@index([bot_id])
  @@index([feature_key]) // 這裡的 feature_key 指的是 AiSystemFeatureDefinition.key
  @@index([api_key_id])
  @@index([request_timestamp])
  @@map("ai_usage_logs")
}
```

---

## 3. AI 功能定義的來源 (Source of AI Feature Definitions)

系統中「已知」的 AI 功能定義，先前由前端常數檔案 (`apps/frontend/src/constants/ai-features.constants.ts`) 管理。**此做法已被取代。**

現在，所有 AI 功能的定義統一由後端資料庫中的 `AiSystemFeatureDefinition` 資料表管理。前端應用程式應透過 API (例如 `/api/ai/feature-definitions`) 從後端獲取功能列表及其屬性。

管理員可透過後台管理介面，對 `AiSystemFeatureDefinition` 資料表進行 CRUD 操作，以定義或修改系統可用的 AI 功能。

---

## 4. 後端架構 (供應商無關性設計)

-   **`AiProviderFactory`** (AI 供應商工廠)：位於 `apps/backend/src/modules/ai/providers/factory.ts` 的中央工廠，根據 `AiBot.provider_type` (對應到 `AiModel.provider`) 負責實例化適當的 AI 供應商客戶端。
-   **`BaseAiProvider`** (基礎 AI 供應商)：位於 `apps/backend/src/modules/ai/providers/base/base-ai-provider.ts` 的抽象基礎類別或介面，定義通用方法，例如 `execute(payload, apiKey, modelName, apiUrlOverride, specificConfig)` 和 `getAvailableModels(apiKey, apiUrlOverride)`。
-   **特定供應商實作 (Specific Provider Implementations)**：位於 `apps/backend/src/modules/ai/providers/implementations/` 中，為每個支援的 AI 供應商提供具體類別 (例如：`OpenAiProvider.ts`, `ClaudeProvider.ts`)。這些類別處理特定 API 的請求/回應格式化、錯誤處理及互動細微差異。
-   **服務層 (Service Layer)**：服務 (例如：`AIBotExecutionService`) 將使用 `AiProviderFactory` 來取得供應商實例。然後呼叫其 `execute` 方法，傳遞從 `AiBot` 衍生的必要資訊 (例如：透過 `model_id` 取得 `AiModel.model_name`，透過 `key_id` 取得 API 金鑰字串、`system_prompt` 及 `provider_config_override`)。

---

## 5. API 設計

### 管理 API (管理員範疇)：
-   `/api/admin/ai/keys`: `AiKey` 的 CRUD 操作。
-   `/api/admin/ai/models`: `AiModel` 的 CRUD 操作 (包含定價欄位)。
-   `/api/admin/ai/system-feature-definitions`: `AiSystemFeatureDefinition` 的 CRUD 操作。
-   `/api/admin/ai/feature-configs`: `AiFeatureConfig` 的 CRUD 操作 (關聯到 `AiSystemFeatureDefinition`)。
-   `/api/admin/ai/bots`: `AiBot` 的 CRUD 操作 (所有範疇)。
-   `/api/admin/ai/global-settings`: 單一 `AiGlobalSetting` 記錄的 GET/PUT 操作。

### 執行 API (使用者/工作空間範疇)：
1.  **取得所有已定義的 AI 功能及其組態 (供 UI 列表或選擇)**：
    *   `GET /api/ai/feature-definitions`
    *   回傳：一個包含 `AiSystemFeatureDefinition` 及其關聯 `AiFeatureConfig` (若存在) 的列表。可用於前端展示所有可用功能及其啟用狀態。
2.  **取得特定功能的組態 (用於執行前檢查)**：
    *   `GET /api/ai/features/{featureDefinitionKey}/config`
    *   `{featureDefinitionKey}` 對應 `AiSystemFeatureDefinition.key`。
    *   回傳：`{ feature: AiSystemFeatureDefinition, config: AiFeatureConfig | null, effectiveBotId: string | null, isEnabled: boolean }`。
        *   `effectiveBotId`：對於系統級功能，由後端根據 `AiSystemFeatureDefinition.key` (匹配 `AiBot.scene`) 決定；對於非系統級功能，來自 `AiFeatureConfig.bot_id`。
        *   `isEnabled`: 綜合 `AiSystemFeatureDefinition` 的存在性及 `AiFeatureConfig.is_enabled` (如果存在) 的狀態。
3.  **執行機器人**：
    *   `POST /api/ai/bots/{botId}/execute`
    *   請求主體 (Request Body)：`{ \"input\": \"使用者的實際輸入或結構化資料\", \"sessionId\": \"選用的會話 ID，用於上下文\", \"featureKey\": \"AiSystemFeatureDefinition.key\" }` (Payload 應為通用格式，`featureKey` 可用於日誌記錄和上下文)。
    *   回應：AI 生成的輸出，根據 `AiBot.response_format` 結構化。

---

## 6. 組態設定與執行流程摘要

1.  **管理員設定**：
    *   **金鑰 (Keys)**：將 API 金鑰新增至 `ai_keys`。
    *   **模型 (Models)**：在 `ai_models` 中新增/更新支援的 AI 模型及其定價。
    *   **全域設定 (Global Settings)**：在 `ai_global_settings` 中設定 `is_ai_globally_enabled` 和 `global_monthly_quota`。
    *   **系統功能定義 (System Feature Definitions)**：在 `ai_system_feature_definitions` 中定義系統可用的 AI 功能 (key, name, description, is_system_level)。
    *   **機器人 (Bots)**：
        *   建立 `AiBot` 實例，指派必要的 `key_id`、`model_id`、`scope`、`system_prompt` 等。
        *   對於 SYSTEM 級機器人，`scope` 為 `SYSTEM`，`name` 應與其服務的 `AiSystemFeatureDefinition.name` 對齊，且 `scene` 必須符合對應的 `AiSystemFeatureDefinition.key`。
    *   **功能組態 (Feature Configurations)**：為每個 `AiSystemFeatureDefinition` 建立或更新 `AiFeatureConfig` 記錄，設定 `is_enabled`。對於 `is_system_level = false` 的功能，必須指派一個 `bot_id`。
2.  **前端應用程式請求**：
    *   (首次或需刷新時) 呼叫 `GET /api/ai/feature-definitions` 獲取所有可用功能及其基本配置，用於 UI 展示。
    *   當使用者選擇某功能時，識別其 `AiSystemFeatureDefinition.key`。
    *   (選用，或由前端 state 管理) 呼叫 `GET /api/ai/features/{featureDefinitionKey}/config` 確認該功能的詳細配置、`isEnabled` 狀態及執行所需的 `effectiveBotId`。
    *   呼叫 `POST /api/ai/bots/{effectiveBotId}/execute` 並附帶使用者輸入及 `featureKey`。
3.  **後端機器人執行**：
    *   檢查 `AiGlobalSetting.is_ai_globally_enabled`。
    *   根據請求中的 `botId` (即 `effectiveBotId`) 載入目標 `AiBot`。
    *   (可選增強驗證) 根據傳入的 `featureKey` (即 `AiSystemFeatureDefinition.key`):
        *   查詢 `AiSystemFeatureDefinition`。
        *   查詢關聯的 `AiFeatureConfig`。
        *   驗證 `AiFeatureConfig.is_enabled`。
        *   如果 `AiSystemFeatureDefinition.is_system_level` 為 `true`，驗證目標 `AiBot.scope` 是否為 `SYSTEM` 且 `AiBot.scene` 是否等於 `AiSystemFeatureDefinition.key`。
        *   如果 `AiSystemFeatureDefinition.is_system_level` 為 `false`，驗證 `AiFeatureConfig.bot_id` 是否與目標 `AiBot.id` 一致。
    *   檢查所選 `AiBot.is_enabled`。
    *   執行配額檢查。
    *   載入 `AiBot` 組態、`AiModel` 詳細資訊、`AiKey` 字串。
    *   使用 `AiProviderFactory`。
    *   呼叫供應商的 `execute` 方法。
    *   記錄 `AiUsageLog`，其中 `feature_key` 欄位記錄 `AiSystemFeatureDefinition.key`。
    *   回傳回應。

---

## 7. AI 模型互動與開發實踐

與 AI 模型的有效互動對於品質和效率至關重要。開發人員應遵循以下原則：

1.  **模型選擇 (Model Selection)**：
    *   了解可用模型的優缺點（例如：GPT-4 適用於複雜邏輯，Claude 適用於細膩的語言/較長上下文，Gemini 適用於 Google 生態系統整合）。
    *   根據任務選擇合適的模型，考量其能力、成本和速度。`AiBot` 組態允許為每個機器人指定模型。

2.  **提示工程 (Prompt Engineering)**：
    *   **清晰度與上下文 (Clarity and Context)**：提供清晰、無歧義的指令。包含足夠的上下文（例如：現有程式碼、聊天機器人的使用者查詢歷史）以引導模型。
    *   **系統提示詞 (System Prompts)**：利用 `AiBot.system_prompt` 欄位定義機器人的角色、職責、限制和一般指令。
    *   **少樣本學習 (Few-Shot Learning)**：適用時，在提示詞中提供期望的輸入/輸出格式範例。
    *   **迭代優化 (Iterative Refinement)**：提示工程通常是一個迭代過程。測試並優化提示詞以達到最佳結果。
    *   **輸出格式化 (Output Formatting)**：若需要特定的輸出結構（例如：JSON），請在提示詞中明確要求，並相應設定 `AiBot.response_format`。

3.  **處理模型特性 (Handling Model-Specifics)**：
    *   **Token 限制 (Token Limits)**：注意模型上下文視窗的限制 (`AiModel.context_window_tokens`)。必要時，實作處理長輸入/輸出的策略（例如：摘要、分塊），儘管 `BaseAiProvider` 應已抽象化部分處理。
    *   **速率限制與配額 (Rate Limits and Quotas)**：針對 API 速率限制或配額問題，實作健全的錯誤處理和重試機制。
    *   **知識截止日期 (Knowledge Cutoffs)**：注意模型的訓練資料截止日期；除非經過微調或與檢索增強生成 (RAG) 一起使用，否則它們不會知道近期事件。

4.  **成本管理 (Cost Management)**：
    *   注意 token 使用量 (`input_tokens`、`output_tokens`，來自 `AiUsageLog`)。
    *   優化提示詞和互動，以最小化不必要的 token 消耗。
    *   對於不需要尖端功能的任務，使用成本較低的模型。

5.  **錯誤處理與系統韌性 (Error Handling & Resilience)**：
    *   針對 API 呼叫（網路問題、供應商錯誤、格式錯誤的回應）實作全面的錯誤處理。
    *   若 AI 發生故障，向使用者提供資訊清晰的回饋。

6.  **驗證與測試 (Verification and Testing)**：
    *   徹底測試 AI 生成的內容和功能。不要假設 AI 的輸出總是正確或安全的。
    *   為 AI 相關服務實作單元測試和整合測試，必要時模擬供應商的回應。

---

## 8. 系統級機器人管理 (針對 SYSTEM 範疇)

SYSTEM 範疇的 AI 機器人是後端和管理性 AI 功能不可或缺的一部分。其管理遵循特定原則：

1.  **目的 (Purpose)**：專為系統級任務設計 (由 `AiSystemFeatureDefinition.is_system_level = true` 標識的功能)，不與特定租戶或工作空間綁定。
2.  **建立與組態設定 (Creation & Configuration)**：
    *   管理員建立 `AiBot` 記錄，並將 `scope` 設定為 `SYSTEM`。
    *   **命名慣例 (Naming Convention)**：SYSTEM `AiBot` 的 `name`（例如："AI 提示詞優化"）必須與其服務的 `AiSystemFeatureDefinition.name` 屬性相對應。
    *   **Scene 欄位 (Scene Field)**：SYSTEM `AiBot` 的 `scene` 欄位必須填入此機器人旨在服務的 `AiSystemFeatureDefinition.key`。此連結至關重要。
    *   其他組態如 `model_id`、`key_id`、`system_prompt`、`temperature` 等，則按照標準 `AiBot` 管理方式設定。
3.  **與系統功能關聯 (Association with System Features)**：
    *   `AiSystemFeatureDefinition` 記錄定義每個系統功能。
    *   對應的 `AiFeatureConfig` 記錄 (透過 `feature_definition_id` 關聯) 控制該功能的啟用狀態 (`is_enabled`)。
    *   對於 `AiSystemFeatureDefinition.is_system_level = true` 的功能，`AiFeatureConfig.bot_id` 欄位通常不使用或被忽略。
    *   後端動態解析適當的 SYSTEM `AiBot`，方法是：找到 `AiSystemFeatureDefinition.key`，然後查找 `AiBot` 其中 `scope = 'SYSTEM'` 且 `scene` 等於該 `key`。
4.  **管理介面 (Administrative Interface)**：
    *   管理後台應提供清晰的介面來管理 SYSTEM `AiBot` 實例。
    *   同時，也需要介面管理 `AiSystemFeatureDefinition` (定義功能) 和 `AiFeatureConfig` (啟用/配置功能，並為非系統級功能指派 `bot_id`)。
    *   介面應清楚顯示 `AiBot.scene` 與 `AiSystemFeatureDefinition.key` 的連結。

---

## 9. AI 開發原則 (一般 AI)

-   **嚴格的範疇驗證 (Strict Scope Validation)**：後端服務必須根據使用者上下文和機器人範疇，嚴格驗證 `tenant_id` 和 `workspace_id`。SYSTEM 級機器人在沒有這些上下文的情況下運作。
-   **安全性 (Security)**：API 金鑰 (`AiKey.api_key`) 加密儲存，絕不暴露給客戶端。`provider_config_override` 或其他欄位中的敏感資料必須安全處理。
-   **可測試性 (Testability)**：設計時應考量可測試性。這包括在自動化測試中模擬 AI 供應商回應的能力，以確保服務邏輯在獨立於即時 AI 模型行為的情況下是正確的。
-   **完整性 (Completeness)**：所有描述的 AI 功能、組態和架構組件都必須完整實作，不得有預留位置、待辦事項 (TODO) 或未完成的部分。確保系統健全並能妥善處理邊緣案例。
-   **迭代開發 (Iterative Development)**：雖然目標是完整性，但 AI 功能通常受益於迭代開發和使用者回饋。應規劃部署後的監控和優化。





