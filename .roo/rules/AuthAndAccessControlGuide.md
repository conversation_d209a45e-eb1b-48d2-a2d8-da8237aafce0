---
description: 
globs: 
alwaysApply: true
---
# HorizAI SaaS - 身份驗證與權限控制統一指南 (Auth & Access Control Unified Guide)

**版本**: 1.0 (持續更新以反映重構進度)
**基於**: `auth-system-prd.md`, `auth-system-refactor-todo.md`, `default-roles-permissions-map.md`, 原 `auth-system.md`, 原 `admin-permissions.md`

## 1. 引言

### 1.1 文件目的

本文件旨在統一並詳細定義 HorizAI SaaS 平台的身份驗證 (Authentication) 與授權 (Authorization) 系統的核心設計原則、架構、功能需求、技術規格及實施指南。本系統是平台安全的基石，確保只有合法的用戶才能存取其被授權的資源與功能，並為未來的擴展奠定堅實基礎。

本文檔將取代並整合以下舊有規則文件：
*   `auth-system.md`
*   `admin-permissions.md`

### 1.2 核心目標

1.  **安全性 (Security)**: 建立強固的安全屏障，符合業界標準，防範未經授權的存取、資料洩漏和潛在攻擊。確保 HorizAI 系統資源得到有效保護。
2.  **精細化權限控制 (Granular Access Control)**: 提供基於角色 (RBAC)、資源屬性 (ABAC/Conditions via CASL) 和潛在欄位級別的靈活權限管理。支援多租戶架構下的權限隔離。
3.  **易用性 (Usability)**: 為終端用戶提供流暢的身份驗證體驗，為管理員提供清晰易操作的權限管理介面 (包含角色管理、權限指派)。
4.  **可維護性與可擴展性 (Maintainability & Scalability)**: 系統設計應模組化、清晰，易於維護、擴展新功能及整合新的認證/授權機制。`@horizai/permissions` 作為權限常數的單一事實來源，`permission-sync` 工具確保程式碼宣告與資料庫的一致性。
5.  **多租戶支援 (Multi-Tenancy)**: 完美支援多租戶架構，確保租戶間的資料和權限隔離。
6.  **標準化 (Standardization)**: 遵循 JWT, OAuth 2.0 概念等業界最佳實踐和標準。
7.  **前端整合 (Frontend Integration)**: 無縫整合前端 UI 元素的動態顯隱和資訊屏蔽，提升使用者體驗。

### 1.3 名詞定義

(沿用 `auth-system-prd.md` 中的名詞定義，此處不再贅述，重點詞彙如：Authentication, Authorization, CASL, Role, Permission, Subject, Action, Conditions, JWT, Tenant, Workspace)

## 2. 系統架構

### 2.1 後端架構 (NestJS + Prisma + CASL)

#### 2.1.1 資料庫模型 (Prisma Schema)

核心模型 (詳見 `apps/backend/prisma/schema.prisma`):

**分離用戶架構**:
*   `system_users`: 系統管理員用戶（SuperAdmin, SystemAdmin 等）
    *   `id`: String (cuid)
    *   `email`: String (unique)
    *   `password`: String (bcryptjs hashed)
    *   `name`: String?
    *   `role`: Enum `SystemUserRole` (`SUPER_ADMIN`, `SYSTEM_ADMIN`, `SYSTEM_MODERATOR`)
    *   `status`: String (如 "active", "inactive")
    *   `created_at`, `updated_at`: DateTime
    *   `last_login_at`, `last_logout_at`: DateTime?
    *   關聯: `system_user_roles[]`, `refresh_tokens[]`
*   `tenant_users`: 租戶用戶（TenantAdmin, TenantUser 等）
    *   `id`: String (cuid)
    *   `email`: String (unique)
    *   `password`: String (bcryptjs hashed)
    *   `name`: String?
    *   `tenant_id`: String (關聯到租戶)
    *   `role`: Enum `TenantUserRole` (`TENANT_ADMIN`, `TENANT_MANAGER`, `TENANT_USER`, `TENANT_VIEWER`)
    *   `status`: Enum `TenantUserStatus` (`ACTIVE`, `INACTIVE`, `PENDING`, `LEFT_COMPANY`, `SUSPENDED`)
    *   `department`: String? (部門)
    *   `title`: String? (職位)
    *   `created_at`, `updated_at`: DateTime
    *   關聯: `tenant`, `tenant_user_roles[]`, `refresh_tokens[]`

**其他核心模型**:
*   `tenants`: 租戶資訊。
*   `workspaces`: 工作區資訊。
*   `roles`: 角色定義。
    *   `id`: String (cuid)
    *   `name`: String (角色名稱，特定 scope 下應唯一)
    *   `display_name`: String (顯示名稱)
    *   `description`: String? (角色描述)
    *   `scope`: Enum `RoleScope` (`SYSTEM`, `TENANT`, `WORKSPACE`) - 定義角色的適用範圍。
    *   `is_system`: Boolean (是否為系統預設角色，不可刪除/修改核心屬性)
    *   `tenant_id`: String? (當 scope 為 `TENANT` 或 `WORKSPACE` 時關聯的租戶 ID)
    *   關聯: `role_permissions[]`, `system_user_roles[]`, `tenant_user_roles[]`
*   `permissions`: 權限定義。
    *   `id`: String (cuid)
    *   `action`: String (操作，如 `create`, `read`, `manage`)
    *   `subject`: String (主體/資源，如 `SystemUser`, `TenantUser`, `Project`, `TenantSetting`)
    *   `conditions`: Json? (CASL 條件，JSON 格式，用於屬性級控制)
    *   `fields`: String[]? (CASL 欄位級控制，限制可讀/寫的欄位)
    *   `description`: String? (權限描述)
    *   `scope`: Enum `PermissionScope` (`SYSTEM`, `TENANT`, `WORKSPACE`, `GLOBAL`) - 權限適用範圍或分類。
    *   關聯: `role_permissions[]`

**角色關聯表（分離架構）**:
*   `system_user_roles`: 系統用戶與角色的關聯表
    *   `id`: String
    *   `system_user_id`: String (關聯到 system_users)
    *   `role_id`: String (關聯到 roles)
    *   `created_at`, `updated_at`: DateTime
*   `tenant_user_roles`: 租戶用戶與角色的關聯表
    *   `id`: String
    *   `tenant_user_id`: String (關聯到 tenant_users)
    *   `role_id`: String (關聯到 roles)
    *   `created_at`, `updated_at`: DateTime
*   `role_permissions`: 角色與權限的多對多關聯表。

**其他輔助模型**:
*   `refresh_tokens`: 儲存 Refresh Token（支援分離用戶架構）
    *   `system_user_id`: String? (關聯到系統用戶)
    *   `tenant_user_id`: String? (關聯到租戶用戶)
    *   `user_type`: String ("system" | "tenant")
*   `line_auth_states`: Line 登入相關狀態。
*   `login_logs`: 登入日誌。
*   `system_logs`: 系統操作日誌 (稽核)。

#### 2.1.2 核心服務

*   `AuthService` (`apps/backend/src/modules/core/auth/auth.service.ts`):
    *   處理使用者登入、註冊、登出。
    *   JWT (Access Token, Refresh Token) 的簽發、驗證、刷新。強化 JWT 安全性，如考慮 Refresh Token Rotation, Secure Signing Keys (RS256 優先)。
    *   密碼管理 (雜湊比對, 重設邏輯)。
    *   第三方登入 (Google, Line) 邏輯。
*   `UsersService` (System, Tenant, Workspace scopes): 使用者帳戶管理。
*   `RolesService` (`apps/backend/src/modules/admin/roles/roles.service.ts` 或各 scope 下):
    *   角色的 CRUD 操作。
    *   角色與權限的關聯管理。
*   `PermissionsService` (`apps/backend/src/modules/admin/roles/permissions.service.ts` 或各 scope 下):
    *   權限定義的 CRUD 操作 (若權限為完全資料庫驅動)。
    *   提供權限列表查詢。
*   `CaslAbilityFactory` (`apps/backend/src/casl/casl-ability.factory.ts`):
    *   根據使用者 ID、角色、直接指派的權限以及 `RolePermissionMapping`。
    *   生成 CASL `AppAbility` 實例 (包含 `can` 和 `cannot` 方法)。此 `AppAbility` 的規則應基於 `Permission` 表中由 `permission-sync` 工具維護的權限定義。
    *   處理 `conditions` 和 `fields` 的解析與應用。
    *   是後端權限檢查的核心。
*   `AuditLogService` / `SystemLogService`: 記錄重要的身份驗證和授權相關事件，如登入成功/失敗、權限變更、授權失敗等。

#### 2.1.3 API 端點 (主要路由)

*   **認證相關 (`/api/auth`)**:
    *   `POST /login`: Email/密碼登入。
    *   `POST /register`: (若開放) 自助註冊。
    *   `POST /logout`: 登出。
    *   `GET /me`: 獲取當前登入使用者資訊 (包含角色和 **序列化後的 CASL `AppAbility` 權限規則 JSON**，供前端使用)。
    *   `POST /refresh-token`: 使用 Refresh Token 刷新 Access Token。
    *   `POST /forgot-password`: 發起忘記密碼請求。
    *   `POST /reset-password`: 使用 token 重設密碼。
    *   `GET /google`, `GET /google/callback`: Google OAuth 登入。
    *   `GET /line`, `GET /line/callback`: Line OAuth 登入。
*   **管理員權限相關 (`/api/admin`)**:
    *   `/roles`: 系統角色 CRUD (`SYSTEM` scope)。
    *   `/permissions`: 系統權限 CRUD (若資料庫驅動)。
    *   `/users/system`: 系統使用者管理。
    *   (其他如 `admin-permissions.md` 所列，但會根據新架構調整 subject 和 action)
*   **租戶管理員權限相關 (`/api/workspace-admin/:workspaceId` 或 `/api/tenant-admin`)**:
    *   `/roles`: 租戶/工作區角色 CRUD (`TENANT`, `WORKSPACE` scope)。
    *   `/users`: 租戶/工作區使用者管理。
*   其他業務 API 端點將透過 Guards 進行權限保護。

#### 2.1.4 後端保護機制

*   `JwtAuthGuard`: 驗證 Access Token，確保請求者已登入。用於保護需要登入的 API。
*   `PoliciesGuard` (或自定義 CASL Guard):
    *   依賴 `CaslAbilityFactory` 生成的 `AppAbility`。
    *   配合 `@CheckPolicies()` decorator (或類似機制) 使用於 Controller 的 handler 上。
    *   Decorator 應能指定 `(action, subject)` 或一個處理函數來檢查權限。
    *   無權限的請求應返回 `403 Forbidden`。

### 2.2 前端架構 (Vue 3 + Pinia + @casl/vue)

#### 2.2.1 身份驗證狀態管理 (`@horizai/auth/src/store/auth.store.ts`)

*   **State**:
    *   `user: User | null` (當前登入使用者物件)
    *   `accessToken: string | null`
    *   `refreshTokenPresent: boolean` (標記 Refresh Token 是否存在於 HttpOnly Cookie)
    *   `isAuthenticated: boolean`
    *   `ability: AppAbility | null` (CASL `AppAbility` 實例，由後端 API `/api/auth/me` 傳遞的權限規則實例化而成，是前端權限判斷的核心)
    *   `loading: boolean` (處理初始化、登入過程中的載入狀態)
*   **Actions**:
    *   `login(credentials)`: 呼叫登入 API，成功後更新 state，儲存 token，並觸發 `fetchCurrentUser` 以初始化 `ability`。
    *   `logout()`: 呼叫登出 API，清除 state、token 和 `ability` 中的規則。
    *   `fetchCurrentUser()`: 呼叫 `/api/auth/me`，獲取使用者資訊及序列化的 CASL 權限規則 JSON，並使用這些規則調用 `updateAbility` 來恢復使用者狀態和前端權限判斷能力。此方法通常在應用初始化或登入成功後調用。
    *   `refreshToken()`: 呼叫刷新 token API。 (應由 `httpService` 的攔截器自動處理)
    *   `initAuth()`: 應用啟動時的核心函式，嘗試 `fetchCurrentUser`，設置 HTTP client 的 interceptors 處理 token 刷新和 401 錯誤。
    *   `updateAbility(rules: CaslRuleJson[])`: 使用從後端獲取的 CASL 規則 (JSON 格式陣列) 更新 `ability` 實例。
*   **Getters**:
    *   `isLoggedIn: boolean`
    *   `currentUser: User | null`
    *   `can(action, subject, field?): boolean` (直接使用 `ability` 實例的方法)

#### 2.2.2 前端 UI 保護

*   **路由保護 (Navigation Guards)** (`apps/frontend/src/router/guards.ts`):
    *   Vue Router `beforeEach` 全域守衛。
    *   檢查路由 `meta` 字段：
        *   `requiresAuth: boolean`: 是否需要登入。
        *   `requiresGuest: boolean`: 是否僅限訪客訪問 (已登入用戶將被重定向)。
        *   `permission: { action: string, subject: string, field?: string } | (ability: AppAbility) => boolean`: 權限要求。前端應使用從共享套件 (`@horizai/permissions`) 引入的靜態權限常數來定義 `action` 和 `subject`。
    *   未登入訪問受保護路由 -> 重定向到登入頁 (可帶 `redirect` 參數)。
    *   已登入訪問訪客路由 -> 重定向到預設儀表板。
    *   權限不足 -> 重定向到 403 頁面或首頁，並提示訊息。
*   **UI 元素顯隱/禁用**:
    *   使用 `@casl/vue` 提供的 `$can(action, subject, field?)` (template) 或 `useAbility().can(action, subject, field?)` (script) 方法。
    *   考慮使用 `<Can I='action' A='subject' F='field?'>...</Can>` 組件。
    *   **權限標識符一致性**: 前端進行權限判斷時，**必須**使用與後端一致的、從共享權限常數套件 (`@horizai/permissions` 或類似套件) 導入的靜態權限標識符 (Action, Subject)。
    *   範例: `<button v-if="$can(PERMISSIONS.PROJECT.CREATE)">Create Project</button>`
*   **資訊屏蔽 (Information Masking/Filtering)**:
    *   **主要策略 (後端驅動)**: 敏感數據的過濾（例如列表中的某些行）和物件特定欄位的屏蔽（例如用戶電話號碼）**應主要在後端 API 層面完成**。後端 `CaslAbilityFactory` 應根據權限（特別是 CASL 的 `fields` 功能）來決定 API 回應中包含哪些數據和欄位。前端直接渲染已過濾的數據。
    *   **前端輔助顯隱 (謹慎使用)**: 在某些情況下，若前端已獲取包含可選顯示欄位的數據對象，可以基於權限動態決定是否渲染該欄位。這通常用於展示邏輯，而非主要安全過濾。
        *   使用 `$can('read', 'Subject', 'fieldName')` 或 `ability.can('read', 'Subject', 'fieldName')` 來判斷是否顯示特定欄位。
        *   例如：`<div v-if="$can(PERMISSIONS.USER.READ_SENSITIVE_INFO, userObject)">{{ userObject.sensitiveInfo }}</div>` (需確保 `PERMISSIONS.USER.READ_SENSITIVE_INFO` 已定義)。
*   **權限狀態同步**:
    *   登入或 `fetchCurrentUser` 成功後，從 API (`/api/auth/me` 的回應) 獲取序列化的 CASL 權限規則 (JSON 格式陣列)。
    *   將這些規則傳遞給 `authStore` 的 `updateAbility` action 來實例化/更新前端的響應式 `AppAbility` 實例，確保 UI 能自動根據權限變化更新。

## 3. 用戶角色與職責 (FR3)

(詳細定義參考 `auth-system-prd.md` Section 3 和 `default-roles-permissions-map.md`)

*   **超級管理員 (SuperAdmin / SUPER_ADMIN)**: 平台擁有者，最高權限。
*   **系統管理員 (SystemAdmin / SYSTEM_ADMIN)**: 管理平台，租戶，系統級設定。
*   **租戶管理員 (TenantAdmin / TENANT_ADMIN)**: 管理其租戶內的資源、用戶、設定。
*   **工作區管理員 (WorkspaceAdmin / WORKSPACE_ADMIN)**: (可選的細分角色) 管理特定工作區。
*   **租戶用戶/工作區成員 (TenantUser / TENANT_USER)**: 使用平台功能。
*   **訪客/未驗證用戶 (Guest)**: 瀏覽公開資訊。

**預設角色及其核心權限映射表請參考 `apps/DevDoc/default-roles-permissions-map.md`。** 此表應作為 Prisma Seeder 初始化預設角色和權限的基礎。

## 4. 功能需求 (FR4)

**注意**: 以下許多功能點的進度追蹤於 `apps/DevDoc/auth-system-refactor-todo.md`。本指南描述的是目標狀態。

### 4.1 用戶註冊與建立 (FR4.1)
*   **FR4.1.1** 系統管理員建立租戶 (及租戶管理員)。
*   **FR4.1.2** 租戶管理員邀請使用者 (Email 邀請連結)。
*   **FR4.1.3** (可選) 使用者自助註冊 (需明確定義流程，預設關閉)。
*   **FR4.1.4** 使用者資料收集與儲存 (姓名、Email、密碼 bcryptjs 雜湊)。

### 4.2 用戶登入 (FR4.2)
*   **FR4.2.1** Email 和密碼登入。
    *   "記住我" 選項 (延長 Refresh Token 效期)。
    *   清晰的登入失敗錯誤提示。
    *   登入嘗試次數限制 (Throttle)。
*   **FR4.2.2 (未來/部分實現)** 第三方登入 (Social Login)。
    *   Google (後端已部分實現)。
    *   Line (規劃中)。
    *   帳戶綁定/建立流程。
*   **FR4.2.3 (未來)** 單點登入 (SSO - SAML, OAuth 2.0/OIDC)。

### 4.3 密碼管理 (FR4.3)
*   **FR4.3.1** 忘記密碼/密碼重設 (Email token 驗證)。
*   **FR4.3.2** 修改密碼 (需驗證舊密碼)。
*   **FR4.3.3** 密碼複雜性策略 (後端強制)。

### 4.4 Session 管理與 JWT (FR4.4)
*   **FR4.4.1** JWT 簽發:
    *   Access Token (短效期，HttpOnly Cookie `auth_token` 或 Authorization Header)。Payload 包含 `userId`, `username`, `roles`, `iss`, `sub`, `aud`, `exp`, `iat`, `jti`。**權限規則不直接放入 Access Token，而是通過 `/api/auth/me` 單獨獲取**。
    *   Refresh Token (長效期，HttpOnly, Secure, SameSite=Strict Cookie `refresh_token`，其雜湊值存於後端 `AuthRefreshToken` 表)。考慮實施 Refresh Token Rotation 機制。
*   **FR4.4.2** Access Token 驗證 (後端 `JwtAuthGuard`)。
*   **FR4.4.3** Token 刷新 (前端 `httpService` (通常是 `@horizai/auth` 包提供的) 攔截器，後端 `/api/auth/refresh-token` 端點)。
*   **FR4.4.4** Token 失效 (吊銷):
    *   登出時使 Refresh Token 失效 (後端資料庫標記)。
    *   修改密碼時使所有 Refresh Tokens 失效。

### 4.5 用戶登出 (FR4.5)
*   前端清除狀態 (Pinia store)。
*   後端失效 Refresh Token。
*   重定向。

### 4.6 角色管理 (RBAC) (FR4.6)
*   **FR4.6.1** 角色定義:
    *   預設角色: `SUPER_ADMIN`, `SYSTEM_ADMIN`, `TENANT_ADMIN`, `TENANT_USER` (透過 Prisma Seeder 初始化)。
    *   管理員可 CRUD 自定義角色 (在其 scope 內)。
    *   角色欄位: `name`, `description`, `scope`, `tenant_id`。
*   **FR4.6.2 (目前未實現)** 角色層級與繼承。

### 4.7 權限管理 (FR4.7)
*   **FR4.7.1** 權限定義 (Permissions):
    *   `action`: 如 `create`, `read`, `update`, `delete`, `manage`, 及特定業務操作 (如 `execute_ai_bot`)。
    *   `subject`: Prisma 模型名稱或自定義業務主體。
    *   **所有 Action 和 Subject 的組合 (權限標識符) 應在 `@horizai/permissions` 共享套件中靜態定義，作為系統的單一事實來源。**
    *   `conditions` (JSON): 屬性級控制。
    *   `fields` (String[]): 欄位級控制。
    *   `description`: 權限的易讀描述，可由 `permission-sync` 工具從程式碼註解或配置中提取。
    *   `category`, `scope`: 由 `permission-sync` 工具推斷或配置。
*   **FR4.7.2** 權限列表維護:
    *   **`Permission` 資料庫表的內容完全由 `permission-sync` 工具根據程式碼中的宣告 (主要來自 `@horizai/permissions` 和後端控制器/服務的權限使用點) 自動填充和更新。**
    *   管理員介面提供對此列表的查看和篩選功能，但不應允許直接修改由程式碼同步的權限核心定義。
    *   系統核心權限可透過 Seeder 初始化 (但 `permission-sync` 應能覆蓋或確認這些初始化的權限)。
*   **FR4.7.3** 權限 UI 管理: 管理員查看/管理權限定義 (主要為查看由 `permission-sync` 維護的列表，以及為角色指派這些權限)。

### 4.8 角色-權限指派 (FR4.8)
*   **FR4.8.1** 指派機制: 管理員將權限指派給角色 (`RolePermissionMapping` 表)。
*   **FR4.8.2** UI 操作: 角色管理介面中修改角色權限。

### 4.9 使用者-角色指派 (FR4.9)
*   **FR4.9.1** 指派機制: 管理員將角色指派給使用者 (`UserRoleMapping` 表)，使用者可擁有多個角色。
*   **FR4.9.2** UI 操作: 使用者管理介面中修改使用者角色。

### 4.10 API 端點保護 (後端) (FR4.10)
*   **FR4.10.1** 身份驗證保護 (`JwtAuthGuard`)。
*   **FR4.10.2** 權限檢查保護 (`PoliciesGuard` + `@CheckPolicies()`)。

### 4.11 前端 UI 保護 (FR4.11)
*   **FR4.11.1** 路由保護 (Navigation Guards) - 參考 2.2.2 節。
*   **FR4.11.2** UI 元素顯隱/禁用 (`@casl/vue`) - 參考 2.2.2 節，強調使用共享權限常數，以及正確使用 `$can` 或 `<Can>` 組件。
*   **FR4.11.3** 權限狀態同步 (登入後獲取 CASL 規則，實例化 `AppAbility`) - 參考 2.2.2 節，明確後端 API `/api/auth/me` 是權限規則的來源。
*   **FR4.11.4 (新增)** 前端權限常數一致性: 前端**必須**使用與後端一致的、從共享權限常數套件 (`@horizai/permissions` 或類似套件) 導入的靜態權限標識符 (Action, Subject)，以確保權限判斷的準確性。
*   **FR4.11.5 (新增)** 資訊屏蔽策略: 前端應遵循以「後端驅動為主，前端輔助為輔」的資訊屏蔽策略，如 2.2.2 節所述。

### 4.12 (未來) 多因子認證 (MFA) (FR4.12)
*   TOTP 設定、驗證、備用碼 (目前 Email OTP 可作為基礎)。
*   考慮整合 WebAuthn / Passkeys。

### 4.13 系統日誌 (Audit Logging) (FR4.13)
*   **FR4.13.1** 記錄範圍: 登入成功/失敗、登出、密碼操作（重設、修改）、角色建立/修改/刪除、權限指派給角色的變動、使用者角色變動、觸發 `PoliciesGuard` 時的授權成功/失敗、`permission-sync` 工具的執行（開始、結束、變更內容摘要）、MFA 相關操作等重要安全事件。
*   **FR4.13.2** 日誌內容: 時間戳, 使用者ID (或系統進程名), IP位址, 事件類型 (枚舉), 操作對象 (如 Role ID, User ID, Permission Name), 操作結果 (成功/失敗), 變更前後的摘要 (若適用, JSON diff 或描述性文字), 請求上下文 (如 User-Agent, request ID)。
*   **FR4.13.3** 儲存與查詢 (`SystemLog` 表，提供管理員查詢介面，支援篩選和排序)。考慮日誌分級與歸檔策略。

## 5. 權限設計與管理原則

(參考 `default-roles-permissions-map.md` Section 5)

### 5.1 設計階段
*   **最小權限原則 (Principle of Least Privilege)**。
*   **權限粒度** 適中。
*   **Scope 一致性** (`RoleScope`, `PermissionScope`)。
*   **Subject 命名** 清晰，與 Prisma 模型或業務實體對應。
*   **Action 命名** 標準化 (CRUD, manage, execute 等)。
*   **`@horizai/permissions` 作為權限常數的唯一真實來源 (Single Source of Truth)**，所有 Action 和 Subject 組合在此定義。
*   **Conditions** 的合理使用，實現 ABAC。
*   新功能同步規劃權限，並在 `@horizai/permissions` 中定義。

### 5.2 開發階段
*   **Prisma Seeder 腳本 (`apps/backend/prisma/seed.ts`)**: 初始化預設角色、核心權限 (這些權限應已在 `@horizai/permissions` 中定義，Seeder 只是建立映射關係)。
*   **`permission-sync` 工具**: 定期或在 CI/CD 流程中運行，確保 `Permission` 表與程式碼宣告 (源自 `@horizai/permissions` 和 `@CheckPolicies`) 一致。
*   **`CaslAbilityFactory.ts`**: 權限檢查核心，正確處理角色、直接權限、條件，其權限來源基於 `Permission` 表。
*   **`@CheckPolicies` Decorator / `PoliciesGuard`**: 嚴格保護所有需授權的 API 端點，引用的權限常數來自 `@horizai/permissions`。
*   **前端權限使用 (`@casl/vue`, 路由守衛)**: 與後端權限同步，同樣引用 `@horizai/permissions` 中的常數。
*   **錯誤處理**: `401 Unauthorized`, `403 Forbidden`。
*   **完整測試**: 單元測試、整合測試、端到端測試，特別是權限相關邏輯。

### 5.3 維護階段
*   **同步更新**: 模型、API、角色職責變更時，首先更新 `@horizai/permissions` 中的權限常數，然後更新程式碼中的使用，最後運行 `permission-sync` 工具更新資料庫 `Permission` 表。本指南也需同步更新。
*   **定期審查**: 確保權限符合當前需求和最小權限原則。
*   **日誌監控**: 監控系統日誌中的權限相關事件。
*   **版本控制**: `@horizai/permissions` 套件、`permission-sync` 工具程式碼、Seeder 腳本。
*   **文件更新**: 保持本指南及相關 PRD、TODO 文件最新。

### 5.4 (新增) 前端權限 UI/UX 管理原則 (Admin Console Focus)
*   **清晰的權限列表**: 管理後台應提供清晰、易於理解的權限列表界面，展示由 `permission-sync` 工具同步到 `Permission` 資料庫表的所有可用權限。列表應包含權限的 Action, Subject, Scope, Category, 以及易懂的描述。
*   **直觀的角色指派**:
    *   為角色指派權限的操作應直觀、高效。考慮使用視覺化輔助工具，如按模組/功能 (Category) 分組的權限樹狀結構或列表。
    *   支援篩選和搜索權限，方便管理員快速定位。
    *   如 `permission-sync-system.md` 中提及的「左側權限樹/列表 + 右側角色選定」概念，是提升易用性的良好方向。介面應允許管理員勾選或通過類似方式將權限賦予選定的角色。
*   **操作反饋與預防錯誤**:
    *   所有權限修改操作（新增、移除、指派）後，應提供明確的成功或失敗反饋。
    *   對於可能導致重大影響的操作（例如移除角色的所有權限，或修改系統級角色的核心權限），應提供警告提示或二次確認機制。
*   **批量處理能力**: 支援批量為角色添加或移除權限，以提高管理效率。
*   **一致性與易學性**: 權限管理界面的設計風格應與整體應用 UI 風格保持一致 (`UIDesignAndStyleGuide.md`)，並確保新管理員也能快速上手。

## 6. 安全考量 (NFR5.1)

(詳細參考 `auth-system-prd.md` Section 5.1 和 `auth-system-refactor-todo.md` NFR5.1)

*   **OWASP Top 10 風險緩解**。
*   **數據加密**: 傳輸中 (HTTPS), 靜態加密 (密碼雜湊, API 金鑰等敏感資訊使用 `@EncryptionService`)。
*   **輸入驗證與輸出編碼**: Class-validator, ORM 防 SQL Injection, Vue template 防 XSS。
*   **依賴安全**: 定期掃描與更新 (Snyk, npm audit)。
*   **Session 安全**:
    *   JWT 強簽名算法 (RS256 優先，HS256 作為備選)，密鑰安全管理 (環境變數, Vault)。密鑰應定期輪換。
    *   Refresh Token 安全儲存 (HttpOnly, Secure, SameSite=Strict cookie; 後端雜湊儲存)。實施 Refresh Token Rotation。
    *   CSRF 防護 (若使用 Cookie 儲存 Token，需考慮 Double Submit Cookie 或 Synchronizer Token Pattern)。NestJS 預設可能有一些防護，但需確認。
*   **錯誤處理**: 不洩漏敏感系統細節。
*   **速率限制 (Rate Limiting / Throttling)**: 防止暴力破解和 DoS 攻擊 (NestJS ThrottlerModule)。
*   **安全 Headers**: `helmet` 或手動設置 (X-Content-Type-Options, X-Frame-Options, CSP 等)。

## 7. 待辦與重構中事項

本指南旨在描述目標狀態。許多功能的具體實現和細化仍在進行中，進度請參考：
*   `apps/DevDoc/auth-system-refactor-todo.md`
*   `apps/backend/docs/permission-sync-todo.md` (專注於權限同步工具的迭代)

開發團隊應持續更新相關 TODO 文件，並在本指南的相關章節或附錄中註明已完成/進行中/待規劃的狀態，以確保所有人對當前系統能力和未來方向有一致的理解。
**關鍵迭代方向包括：**
*   全面推廣使用 `@horizai/permissions` 作為權限常數的唯一來源。
*   完善 `permission-sync` 工具的功能，確保其能準確、高效地完成權限掃描與同步。
*   強化 JWT 管理機制，包括 Refresh Token Rotation。
*   規劃並逐步實施 MFA (如 TOTP)。
*   細化並實施 Audit Logging 策略。
*   持續優化管理後台的權限管理 UI/UX。
*   編寫更全面的測試案例。

## 8. 附錄 (可選)

*   詳細的 API 端點權限需求表 (可從 `default-roles-permissions-map.md` 的前端路由權限表擴展)。
*   更詳細的 CASL `conditions` 和 `fields` 使用範例。

---
確保所有身份驗證和權限相關功能完整實作，無 TODO 或未完成部分 (指最終交付狀態，開發過程中允許 TODO)。
本文檔應作為身份驗證與授權系統開發與維護的**唯一真實來源 (Single Source of Truth)**。





