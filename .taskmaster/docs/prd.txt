# HorizAI SaaS Platform - Product Requirements Document

## Overview
HorizAI SaaS 是一個以 **AI 代理 (Agent)** 為核心的企業級多租戶 SaaS 平台。專案初期專注於解決**室內設計產業**的特定痛點，包括工地照片辨識、自動化進度管理、智能派工、圖紙報價及合約管理。

平台的最終願景是將這套經過驗證的 Agent 框架抽象化，成為一個可跨行業應用的、強大的 AI 業務流程自動化 SaaS 產品。

核心價值：
- **AI 代理優先 (Agent-First)**：以自主規劃、使用工具、存取知識的 AI Agent 作為解決方案的核心。
- **多租戶安全架構**：從架構層面確保租戶間的資料與 AI 模型訪問權限的絕對隔離。
- **RAG 驅動的知識管理**：整合 LlamaIndex，將企業內部的非結構化資料（PDF、圖片、對話）轉化為 Agent 可用的知識庫。
- **靈活的工具生態系統**：整合 LangChain，將平台所有業務功能（專案、任務、文件）封裝為 Agent 可調用的工具。
- **現代化的使用者體驗與完整的商業化支援**。

## Core Features

### 1. 多租戶管理系統
- **租戶建立與管理**：系統管理員可建立和管理多個租戶組織
- **工作區管理**：每個租戶可擁有多個工作區，支援不同專案或部門
- **資料隔離**：確保租戶間資料完全隔離和安全性
- **租戶設定**：可自訂租戶級別的配置和偏好設定

### 2. 身份驗證與權限系統
- **多層級用戶系統**：系統管理員、租戶管理員、租戶用戶
- **靈活的角色權限**：基於 CASL 的細粒度權限控制
- **多種登入方式**：Email/密碼、Google OAuth、LINE 登入
- **JWT Token 管理**：Access Token + Refresh Token 機制
- **系統日誌**：完整的用戶操作記錄

### 3. AI 基礎設施管理 (Configuration & Governance)
此層為 Agent 的運作提供基礎支援與治理能力。
- **多供應商支援 (LLM Agnostic)**：無縫接軌 OpenAI、Anthropic、Google Gemini 及本地模型。
- **金鑰與模型管理**：提供對 `AiKey` 和 `AiModel` 的完整 CRUD 與測試功能。
- **成本控制與用量追蹤**：`AiUsage` 和 `TenantAiQuota` 模組，精準追蹤每次 Agent 執行的成本，並對租戶進行配額管理。
- **監控與穩定性**：`AiErrorHandler` 和 `AiMonitoring` 服務提供重試、斷路器、回退機制，確保 Agent 運行的穩定性。

### 4. 智能代理系統 (Intelligent Agent System)
這是平台的核心。取代原有的 `AI Workflow`，我們將直接建構一個由 Agent 驅動的系統。

- **Agent 大腦 (Core Brain)**
  - 由 **LangChain** 和 **LangGraph** 驅動，支援兩種執行模式：
    - **工具型 Agent (Tool-based Agent)**: 透過 LangChain，執行單一目標任務，可調用指定的工具集。
    - **圖譜型 Agent (Graph-based Agent)**: 透過 LangGraph，執行多步驟、有狀態的複雜協作流程，支援循環、條件分支和多代理協調。
  - **可視化 Agent 搭建器**: 管理員可在前端介面中直觀地建立和配置 Agent：
    - 定義 Agent 的角色和系統提示詞
    - 選擇 AI 模型和 API 金鑰
    - 為工具型 Agent 選擇可用的工具集（核取方塊式選擇）
    - 為圖譜型 Agent 選擇預定義的協作圖譜範本

- **Agent 長期記憶 (Knowledge Base - RAG)**
  - 由 **LlamaIndex** 驅動，負責將租戶的私有數據轉化為 Agent 的長期記憶。
  - **數據來源**：
    - **檔案 (`files.service`)**: 自動索引使用者上傳的 PDF（合約、圖紙）、圖片等。
    - **結構化數據 (`projects.service`, `tasks.service`)**: 將專案和任務的描述性文字納入索引。
    - **對話紀錄 (`chat.service`)**: 索引 LINE Bot 或內部聊天中的重要對話，作為問題追溯和上下文的依據。
  - **多租戶隔離**：所有被索引的數據都將**強制標記 `tenant_id`**，確保 Agent 在查詢時只能檢索到其所屬租戶的資料。

- **Agent 工具箱 (Toolbox)**
  - 將現有的 NestJS 服務（`projects.service`, `tasks.service`, `files.service` 等）封裝成 Agent 可調用的 `Tool`。
  - **工具範例**：
    - `ProjectInfoTool`: 查詢特定專案的狀態與細節。
    - `CreateTaskTool`: 根據指令（如「為李師傅安排油漆任務」）建立新任務。
    - `AnalyzeDrawingTool` (RAG): 分析上傳的 PDF 圖紙，提取工程項目。
    - `QueryContractTool` (RAG): 根據問題查詢合約內容。
    - `UploadPhotoTool`: 接收 LINE Bot 上傳的工地照片並存檔。

### 5. 核心業務場景 (由 Agent 驅動)

- **工地照片與進度管理**:
  - **流程**: 現場人員透過 LINE Bot 將照片傳至專案群組 -> Webhook 觸發 -> Agent 調用 `UploadPhotoTool` 存檔，並調用視覺分析工具辨識進度 -> Agent 調用 `CreateProgressEntryTool` 自動更新進度報告。
- **智能派工**:
  - **流程**: 客戶在 LINE 群組反應「插座不通」 -> Agent 接收訊息，理解意圖 -> Agent 查詢知識庫找到對應專案 -> Agent 調用 `CreateTaskTool` 建立「客服維修」任務 -> Agent 根據任務類型和工班技能庫 (RAG) 建議或自動指派最適合的電工師傅。
- **客戶報價**:
  - **流程**: 專案經理上傳平面圖 PDF -> Agent 觸發 -> Agent 調用 `AnalyzeDrawingTool` (RAG) 提取工程項目與尺寸 -> Agent 調用 `PriceQueryTool` (RAG) 查詢內部單價 -> Agent 整合資訊生成報價單草稿，並透過 `SendMessageTool` 發送給專案經理確認。
- **合約管理**:
  - **流程**: 法務上傳多份合約範本 -> RAG 管道自動索引 -> 專案經理詢問 Agent：「我們的標準付款條件是什麼？」 -> Agent 調用 `QueryContractTool` (RAG) 從所有合約中檢索、總結並給出答案。

## User Experience

### 用戶角色
- **超級管理員**: 平台最高權限。
- **系統管理員**: 管理租戶、系統設定、監控 Agent 運行狀態、配置 Agent 範本和通用工具。
- **租戶管理員**: 啟用/停用租戶內的 Agent、管理租戶專屬知識庫（上傳文件）、查看用量報表。
- **租戶用戶 (專案經理、設計師、現場人員)**: 在 Workspace 或 LINE 等通路中與 Agent 互動，觸發任務、查詢資訊、接收 Agent 的主動匯報。

### 關鍵用戶流程
1. **Agent 搭建流程 (管理員)**: 
   - 登入後台 -> 進入 Agent 搭建器 -> 建立新 Agent
   - **基本設定**: 輸入 Agent 名稱、描述、系統提示詞
   - **大腦配置**: 選擇 AI 模型、API 金鑰、溫度參數等
   - **執行類型選擇**: 
     - 選擇「工具型 Agent」-> 從工具庫中勾選可用工具（如專案查詢、任務建立等）
     - 選擇「圖譜型 Agent」-> 從圖譜範本中選擇（如專案進度分析師、財務報表生成器）
   - 保存並啟用 -> Agent 立即可供使用者調用
2. **工具管理流程 (系統管理員)**: 登入系統後台 -> 進入工具註冊管理 -> 添加新工具定義 -> 設定工具名稱、描述、輸入結構描述 -> 啟用供 Agent 選擇使用。
3. **知識庫管理流程 (租戶管理員)**: 登入後台 -> 進入知識庫管理 -> 上傳合約、報價單等通用文件 -> 系統自動索引 -> Agent 可在回答時引用這些知識。
4. **Agent 互動流程 (用戶)**:
   - 在 LINE 中 @Agent 並提問：「幫我查一下 A 專案的進度」。
   - 上傳一張工地照片到指定的 LINE 群組。
   - 在 Workspace 的專案頁面點擊「呼叫 AI 分析風險」。
   - Agent 完成任務後，透過 LINE 或 Workspace 內部訊息主動回報。

## Technical Architecture

### 核心技術棧 (維持不變)
- **前端**: Vue 3 + TypeScript + Pinia + Shadcn-Vue
- **後端**: NestJS + TypeScript + Prisma ORM
- **資料庫**: PostgreSQL
- **即時通訊**: WebSocket

### **新增：Agent-Based Architecture**
我們將引入 LangChain.js 和 LlamaIndex.js 作為核心依賴，並建立新的模組來整合它們。

1.  **`agent` 核心模組 (`apps/backend/src/modules/agent`)**:
    - `agent.module.ts`: 引入 LangChain/LlamaIndex/LangGraph 服務，並導入其他需要用到的模組 (e.g., `ProjectsModule`, `FilesModule`)。
    - `agent.service.ts` (`AgentRunnerService`): 負責接收請求，根據 Agent 執行類型（工具型或圖譜型），初始化 `AgentExecutor` (LangChain) 或 `LangGraph` 實例，配置 LLM 和工具箱，然後執行。
    - `tools/`: 存放所有 `Tool` 的實現，將現有服務邏輯封裝起來。
    - `graphs/`: 存放預定義的 `LangGraph` 圖譜定義（如專案進度分析師、財務報表生成器等）。

2.  **Agent 配置擴展 (Backend Schema)**:
    - **`AiTool` 模型**: 註冊系統中所有可用的 Agent 工具，包含工具名稱、描述、輸入結構描述等。
    - **`AiBotTool` 關聯表**: 建立 `AiBot` 與 `AiTool` 的多對多關聯，定義每個 Agent 可使用的工具集。
    - **`AiBot` 模型擴展**: 新增 `execution_type` 欄位區分工具型和圖譜型 Agent，以及 `scene` 欄位用於圖譜範本選擇。

3.  **多租戶 RAG 管道 (Multi-Tenant RAG Pipeline)**:
    - **`rag-ingestion.service.ts`**:
        - 一個事件驅動的服務，監聽來自 `workspace` 模組的事件（如 `file.uploaded`, `message.sent`）。
        - 收到事件後，調用 LlamaIndex 將對應的數據（文件內容、訊息）進行處理和索引。
        - **在索引過程中，強制將 `tenant_id` 作為 `metadata` 寫入每一條向量記錄中。**
    - **向量資料庫 (Vector Database)**:
        - 推薦使用支援元數據過濾的資料庫 (如 Pinecone, Weaviate, Qdrant, or PGVector with filters)。
    - **`KnowledgeBaseTool`**:
        - 一個特殊的 Agent 工具，用於查詢 RAG 索引。
        - **在查詢時，強制使用當前用戶的 `tenant_id` 作為查詢過濾條件 (Metadata Filter)。** 這從根本上保證了租戶數據的絕對隔離。

4.  **與現有模組的關係**:
    - `agent` 模組是**調用者 (Consumer)**，`admin/ai` 和 `workspace/*` 模組是**提供者 (Provider)**。
    - `admin/ai` 提供 Agent 的大腦配置（哪個模型、哪個 Key）。
    - `workspace/*` 提供 Agent 的業務能力（操作專案、任務等）和 RAG 的數據源。

## Development Roadmap

### Phase 1: Agent 核心基礎設施 (MVP)
**目標**: 搭建 Agent 運作的骨架，打通一個完整的、具備多租戶 RAG 能力的端到端流程。
- [ ] **後端**: 建立 `agent` 模組，引入 LangChain.js 和 LlamaIndex.js。
- [ ] **後端**: 實現 `AgentRunnerService`，能夠初始化並執行一個基本的 LangChain Agent。
- [ ] **後端**: 建立 `RAGIngestionService` 和事件管道。初步先實現對 `files.service` 的檔案上傳事件進行索引。
- [ ] **後端**: 在 LlamaIndex 索引和查詢流程中，**實現強制性的 `tenant_id` 隔離**。
- [ ] **後端**: 實現兩個核心工具作為 POC：
    - `ProjectInfoTool`: 封裝 `projects.service` 的查詢功能。
    - `KnowledgeBaseTool`: 封裝 LlamaIndex 的 RAG 查詢功能。
- [ ] **前端**: 建立一個極簡的內部測試頁面，用於輸入問題，觸發 Agent 並查看日誌，驗證端到端流程。

### Phase 2: Agent 自定義系統與工具集擴展
**目標**: 建立可視化 Agent 搭建器，並將核心業務問題系統性地轉換為 Agent 可用的工具。
- [ ] **資料庫結構擴展**: 建立 `AiTool`, `AiBotTool` 模型，擴展 `AiBot` 模型以支援執行類型和工具關聯。
- [ ] **Agent 搭建器 API**: 開發後端 API 支援 Agent 的可視化建立、工具選擇和配置管理。
- [ ] **工具註冊系統**: 建立工具註冊和管理機制，讓系統管理員可以定義和啟用新工具。
- [ ] **前端 Agent 搭建器**: 開發直觀的 Agent 搭建介面，支援拖拉拽式工具選擇和圖譜範本選擇。
- [ ] **核心業務工具開發**: 依據「核心業務場景」的定義，逐一開發對應的 Tools:
    - [ ] `CreateTaskTool`, `UpdateProgressTool`, `SendMessageTool`, `AnalyzeDrawingTool`
- [ ] **RAG 擴展**: 將 `chat.service` 的訊息也納入 RAG 索引範圍。
- [ ] **通路整合**: 將 LINE Bot 的 Webhook 對接到 `AgentRunnerService`，允許使用者透過 LINE 與 Agent 互動。

### Phase 3: Agent 體驗優化與治理
**目標**: 讓 Agent 更好用、更可靠，並讓管理者能有效監控。
- [ ] **記憶模組**: 為 Agent 引入 LangChain 的 `Memory` 模組，讓其能夠記住多輪對話的上下文。
- [ ] **日誌與監控**: 將 Agent 的思考過程、工具調用日誌、Token 消耗等詳細資訊，整合到現有的 `ai_usage_logs` 中。
- [ ] **前端**: 開發 Agent 互動歷史介面，以及知識庫管理介面。
- [ ] **Agent 優化**: 測試更複雜的 Agent 類型（如 Plan-and-Execute），優化 Prompt Engineering。

## Logical Dependency Chain
1.  **AI 基礎設施 (admin/ai)**: 金鑰、模型管理是 Agent 運作的前提。
2.  **Agent 核心 + RAG 管道 (Phase 1)**: 這是整個新架構的基石，必須最先建立並驗證其多租戶安全性。
3.  **業務工具封裝 (Phase 2)**: 在核心穩固後，平行地將現有業務能力轉化為工具。
4.  **通路整合 (Phase 2)**: 在 Agent 具備基礎能力後，將其對接到 LINE 等真實用戶入口。
5.  **前端管理介面 (Phase 2/3)**: 在後端能力成熟後，再提供對應的前端 UI 進行管理。
6.  **體驗與監控 (Phase 3)**: 在 Agent 能用之後，再投入資源讓它變得更好用、更可控。

## Risks and Mitigations
- **技術風險**: LangChain/LlamaIndex 快速迭代，可能存在 API 變更。
  - **緩解**: 鎖定穩定的函式庫版本，並將其核心邏輯封裝在我們自己的 Service (`AgentRunnerService`, `LlamaIndexService`) 中，建立防腐層。
- **產品風險**: Agent 的行為不夠穩定或不符合預期 (幻覺)。
  - **緩解**:
    1. 初期 Agent 的定位是「強大的助手」而非「全自動決策者」，關鍵操作（如派工、報價）由 Agent 生成草稿，交由人類確認。
    2. 透過精細的 Prompt Engineering 和 Few-shot Learning 來約束 Agent 的行為。
    3. 強化 RAG，讓 Agent 的回答盡量基於內部知識，而非自由發揮。
- **安全風險**: 多租戶 RAG 資料隔離失敗。
  - **緩解**: 將 `tenant_id` 過濾作為架構的強制要求，進行嚴格的單元測試和整合測試，確保任何查詢都無法繞過此限制。