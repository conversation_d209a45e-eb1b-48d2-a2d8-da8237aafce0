# Task ID: 8
# Title: Implement `RAGIngestionService` for File Indexing
# Status: done
# Dependencies: 1, 2
# Priority: high
# Description: Create `RAGIngestionService` to listen for file upload events (e.g., from `files.service`). Use LlamaIndex.js to process and index content of uploaded files (initially PDFs, images) into a vector store.
# Details:
Choose and setup vector DB (e.g., PGVector with `prisma-extension-pgvector`). Create `rag-ingestion.service.ts`. `@OnEvent('file.uploaded') async handleFileUpload(event: { filePath: string, tenantId: string, fileId: string })`. Logic: load file content, create LlamaIndex `Document` with `metadata: { tenant_id: event.tenantId, file_id: event.fileId }`, index into vector store. Requires `EventEmitterModule` and `files.service` to emit event.

# Test Strategy:
Unit test `handleFileUpload` with mock file/vector store. Verify event listener triggers. Check vector DB for indexed documents with correct metadata.

# Subtasks:
## 1. 安裝 LlamaIndex.js 及相關依賴項 [done]
### Dependencies: None
### Description: 在後端專案中安裝 `LlamaIndex.js` 及其相關依賴項 (如 PDF 解析庫 `pdf-parse`)。
### Details:


## 2. 設定向量資料庫 (Vector DB) [done]
### Dependencies: None
### Description: 選擇並設定向量資料庫。若使用 PostgreSQL，則安裝並配置 `pgvector` 擴充功能及 `@prisma/extension-pgvector`。
### Details:


## 3. 建立 RAG 模組與服務檔案 [done]
### Dependencies: None
### Description: 在 `apps/backend/src/modules/ai/` 或合適位置下，建立 `rag` 模組，並在其中創建 `rag-ingestion.service.ts`。
### Details:


## 4. 註冊 RAGIngestionService [done]
### Dependencies: None
### Description: 在 `rag.module.ts` 中註冊 `RAGIngestionService` 並確保其能被應用程式其他部分注入和使用。
### Details:


## 5. 實作核心文件處理方法 [done]
### Dependencies: None
### Description: 在 `RAGIngestionService` 中，實作核心的文件處理方法，該方法應能接收文件路徑和租戶 ID。
### Details:


## 6. 整合 LlamaIndex.js 進行文件加載 [done]
### Dependencies: None
### Description: 整合 `LlamaIndex.js`，實現加載不同文件類型 (優先處理 PDF) 的邏輯。
### Details:


## 7. 創建帶有 Metadata 的 LlamaIndex Document [done]
### Dependencies: None
### Description: 創建 LlamaIndex `Document` 物件，並確保將 `tenant_id` 和 `file_id` 等重要資訊存入其 `metadata` 中。
### Details:


## 8. 將 Document 索引至向量資料庫 [done]
### Dependencies: None
### Description: 將 `Document` 物件索引到先前設定好的向量資料庫中。
### Details:


## 9. 配置 EventEmitterModule [done]
### Dependencies: None
### Description: 確保 NestJS 的 `EventEmitterModule` 已在專案中全局設定。
### Details:


## 10. 建立 'file.uploaded' 事件監聽器 [done]
### Dependencies: None
### Description: 在 `RAGIngestionService` 中使用 `@OnEvent('file.uploaded')` 裝飾器，建立一個事件監聽器 `handleFileUpload(payload)` 來觸發索引流程。
### Details:


## 11. 從文件服務中發出 'file.uploaded' 事件 [done]
### Dependencies: None
### Description: 修改現有的文件上傳服務 (例如 `files.service`)，使其在文件成功上傳並儲存後，能發出 `file.uploaded` 事件，並附帶必要的 `payload` (如文件路徑、租戶 ID、文件 ID)。
### Details:


## 12. 編寫單元測試 [done]
### Dependencies: None
### Description: 為 `RAGIngestionService` 編寫單元測試，模擬 `file.uploaded` 事件並驗證索引邏輯是否被正確調用。
### Details:


## 13. 進行整合測試 [done]
### Dependencies: None
### Description: 進行整合測試，實際的上傳一個文件，並檢查向量資料庫中是否已成功創建帶有正確 `metadata` 的向量記錄。
### Details:


