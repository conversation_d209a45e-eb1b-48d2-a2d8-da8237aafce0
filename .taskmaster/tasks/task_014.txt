# Task ID: 14
# Title: Add Google OAuth and LINE Login Options
# Status: done
# Dependencies: 4
# Priority: medium
# Description: Google OAuth 2.0 and LINE Login features are fully implemented. The remaining step is to configure the necessary environment variables for both services to enable them.
# Details:
**Implementation Status: Complete**
- **Google OAuth Strategy**: Implemented (`GoogleStrategy` in `apps/backend/src/modules/core/auth/strategies/google.strategy.ts`) using `passport-google-oauth20`.
- **LINE Login Strategy**: Implemented (`LineStrategy` in `apps/backend/src/modules/core/auth/strategies/line.strategy.ts`) using `passport-line-auth`.
- **OAuth Routes**: `/auth/google`, `/auth/google/callback`, `/auth/line`, `/auth/line/callback` are functional.
- **Database**: `oauth_accounts` model in `schema.prisma` supports multiple providers.
- **Dependencies**: `passport-google-oauth20` and `passport-line-auth` are installed.
- **User Handling**: `findOrCreateUserFromOAuth()` logic for user creation/linking, JWT generation, and cookie setting is in place.
- **Testing**: E2E tests (`test/oauth.e2e-spec.ts`) are available.

**Required Final Configuration:**
The following environment variables must be set:
- `GOOGLE_CLIENT_ID`
- `GOOGLE_CLIENT_SECRET`
- `GOOGLE_CALLBACK_URL`
- `LINE_CLIENT_ID`
- `LINE_CLIENT_SECRET`
- `LINE_REDIRECT_URI`

# Test Strategy:
E2E tests for OAuth (`test/oauth.e2e-spec.ts`) are available. Once the environment variables are configured, run these tests. Additionally, perform manual end-to-end testing of both Google and LINE login flows to verify successful authentication, user account creation/linking, and error handling.

# Subtasks:
## sub_14_1. Configure Google OAuth Environment Variables [done]
### Dependencies: None
### Description: 
### Details:
Set GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, and GOOGLE_CALLBACK_URL in the environment configuration files or deployment settings.

## sub_14_2. Configure LINE Login Environment Variables [done]
### Dependencies: None
### Description: 
### Details:
Set LINE_CLIENT_ID, LINE_CLIENT_SECRET, and LINE_REDIRECT_URI in the environment configuration files or deployment settings.

## sub_14_3. Verify OAuth Integrations Post-Configuration [done]
### Dependencies: None
### Description: 
### Details:
After configuring all required environment variables, run the existing E2E tests (`test/oauth.e2e-spec.ts`) and perform manual testing of both Google and LINE login flows to ensure they are working correctly in the target environment.

