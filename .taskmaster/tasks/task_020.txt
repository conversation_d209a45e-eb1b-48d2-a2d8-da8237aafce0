# Task ID: 20
# Title: Develop System-Wide Audit Logging
# Status: done
# Dependencies: 4
# Priority: medium
# Description: Create a comprehensive audit trail for significant user actions (logins, resource creation/modification/deletion, settings changes).
# Details:
Prisma schema for `AuditLog`: `id`, `userId`, `action String`, `details Json?`, `ipAddress String?`, `timestamp DateTime`, `tenantId String?`. Create `AuditLogService`. Use NestJS interceptors/decorators for automatic logging.

# Test Strategy:
Perform user actions, verify audit logs created with correct details. Ensure no sensitive data logged.

# Subtasks:
## 1. 創建專用AuditLogService (Create Dedicated AuditLogService) [done]
### Dependencies: None
### Description: 開發一個專門的AuditLogService，通過擴展現有的SystemLogService來處理審計特定的日誌記錄需求，確保與現有架構模式兼容。
### Details:
此服務將封裝審計日誌記錄的核心邏輯，利用現有的`system_logs`表。需整合tenant隔離機制、符合CASL權限控制，並實現敏感資料過濾功能。這是後續所有審計功能的基礎。
<info added on 2025-06-20T10:36:23.131Z>
子任務20.1「創建專用AuditLogService」已成功完成！

完成的功能：

核心AuditLogService實現：
創建了專用的AuditLogService類，擴展現有SystemLogService功能
支持多種審計日誌記錄方法：log、logUserAction、logResourceAction、logSecurityEvent、logPermissionEvent
完整的TypeScript類型定義：AuditLogData、AuditContextData、AuditLogFilter

安全功能：
實現了敏感資料自動過濾，支持嵌套對象和陣列
支持多種敏感字段模式：password、token、key、auth、credential等
錯誤處理不會影響主要業務流程

查詢和統計：
findLogs方法支持多維度查詢和分頁
getAuditStats方法提供統計分析功能
支持租戶隔離和日期範圍過濾

架構整合：
已集成到CommonModule中，與現有架構兼容
使用現有system_logs表，無需額外數據庫變更
支持JwtUser上下文和租戶隔離機制

測試覆蓋：
完整的單元測試，15個測試案例全部通過
覆蓋所有核心功能和邊界情況
專案編譯成功無錯誤

此服務現在可以作為後續審計功能的基礎組件使用。
</info added on 2025-06-20T10:36:23.131Z>

## 2. 開發@Audit裝飾器用於方法級審計 (Develop @Audit Decorator for Method-Level Auditing) [done]
### Dependencies: 20.1
### Description: 實現一個@Audit裝飾器，以便能夠輕鬆地對特定業務方法進行聲明式審計，記錄方法調用及參數。
### Details:
該裝飾器應與新創建的AuditLogService集成，自動記錄方法執行信息。必須遵守tenant隔離機制、CASL權限控制，並支持對方法參數進行敏感資料過濾。
<info added on 2025-06-20T10:47:31.763Z>
@Audit Decorator Implementation Summary

Key Achievements:
1. Created @Audit decorator (audit.decorator.ts): Comprehensive AuditOptions interface with 10+ configuration options, support for template-based descriptions with variable substitution, flexible parameter and property exclusion capabilities, custom context and resource ID extraction functions.
2. Implemented AuditInterceptor (audit.interceptor.ts): Full integration with existing AuditLogService, template engine for dynamic message generation (${args.0}, ${user.email}, etc.), comprehensive error handling without disrupting business flow, smart parameter sanitization and sensitive data filtering, execution time tracking and context extraction.
3. Added to CommonModule: Proper NestJS module integration, AuditInterceptor available for dependency injection.
4. Comprehensive Testing: 10 test cases covering all major functionality, tests for success/failure scenarios, parameter exclusion, template processing, 100% test pass rate with proper mocking.
5. Usage Examples: Created detailed examples file showing real-world usage patterns, examples for user management, permissions, settings, file operations, complex scenarios with multiple features combined.

Architecture Integration:
Compatible with existing JwtUser type and tenant isolation.
Uses existing AuditLogService foundation.
Follows NestJS best practices with proper decorators/interceptors.
Type-safe implementation with comprehensive TypeScript support.

Quality Metrics:
All tests passing (10/10).
Project builds successfully.
No compilation errors.
Comprehensive error handling and logging.

Ready for Production Use:
The @Audit decorator can now be applied to any controller method to automatically capture audit logs with flexible configuration options. The implementation supports complex business scenarios while maintaining high performance and security standards.
</info added on 2025-06-20T10:47:31.763Z>

## 3. 增強LoggingInterceptor以支持更完整的審計 (Enhance LoggingInterceptor for Comprehensive Auditing) [done]
### Dependencies: 20.1
### Description: 修改現有的LoggingInterceptor，使其能夠捕獲更全面的API請求審計信息，並與新的AuditLogService集成。
### Details:
確保攔截器能捕獲包括請求路徑、方法、用戶信息、IP地址、請求體和響應狀態等數據。需與tenant隔離機制、CASL權限控制集成，並對捕獲的數據應用敏感資料過濾。
<info added on 2025-06-20T11:06:44.872Z>
Enhanced LoggingInterceptor Summary
Key Enhancements:
1. Full Integration with AuditLogService: The interceptor now uses the new AuditLogService for detailed, structured audit logging, in addition to SystemLogService for general request logging. Leverages JwtUser context for accurate user and tenant information.
2. Comprehensive Data Capture: Logs extensive request/response data for audited events, including: Request: method, URL, body, query, params, headers; Response: status code, size, execution time; Context: User, IP, User-Agent, Tenant; Metadata: Timestamps, session/request IDs.
3. Smart Auditing Logic: Implemented shouldAuditRequest to automatically perform detailed auditing for: All data modification requests (POST, PUT, PATCH, DELETE). Sensitive GET requests to critical endpoints (/auth, /admin, /users, /settings, etc.). General requests are still logged at a system level without the detailed audit overhead.
4. Intelligent Data Extraction: mapMethodToAction: Maps HTTP methods to standardized action names (e.g., POST -> RESOURCE_CREATE). extractResourceType & extractResourceId: Intelligently determines the target resource and its ID from the URL and request parameters.
5. Robust Security & Sanitization: Enhanced sanitizeRequestBody and sanitizeHeaders to filter a wider range of sensitive fields (passwords, tokens, API keys, etc.) recursively in nested objects and arrays. Added sanitizeErrorStack to prevent leaking file system paths in logs. Improved getClientIp to correctly prioritize proxy headers (x-forwarded-for), which is crucial for production environments.
6. Resilience: The interceptor is designed to be resilient. Failures in either logging service will not disrupt the main application flow. Errors are logged to the console via NestJS Logger.
Quality Metrics:
All new unit tests are passing.
The project builds successfully without errors.
The new implementation is backward-compatible with the existing logging structure while adding a much more powerful auditing layer.
Conclusion:
The LoggingInterceptor is now a powerful, centralized mechanism for capturing detailed audit trails for all significant API interactions. It provides deep visibility into system activity automatically, forming a critical component of the overall audit logging system.
</info added on 2025-06-20T11:06:44.872Z>

## 4. 建立敏感操作的自動審計機制 (Establish Automated Auditing for Sensitive Operations) [done]
### Dependencies: 20.1
### Description: 識別系統中的關鍵敏感操作（例如，用戶權限變更、重要數據修改/刪除），並實施機制以自動記錄這些操作的審計日誌。
### Details:
此機制可能涉及在特定服務層或通過配置來觸發AuditLogService的記錄。確保所有敏感操作的審計記錄都包含足夠的上下文信息，並符合tenant隔離、CASL權限及敏感資料過濾要求。
<info added on 2025-06-20T11:10:38.897Z>
Automated Auditing for Sensitive Operations Summary

Objective: Identify and implement automated audit logging for critical sensitive operations across the system.

Methodology:
Leveraged the previously developed @Audit decorator and LoggingInterceptor to declaratively apply auditing to sensitive controller endpoints. This approach ensures that auditing is consistently applied and easy to maintain.

Implementation Details:
I systematically identified and instrumented the following key areas:
1. System Settings (SettingsController):
    - Applied @Audit to all setting modification endpoints (create, updateGeneralSettings, updateSecuritySettings, updateUserSettings, updateEmailSettings, updateStorageSettings, etc.).
    - Configured decorators to log relevant context and exclude sensitive values like API keys, passwords, and secrets from the audit trail.
    - Example Action: SETTINGS_UPDATE_SECURITY

2. System User Management (SystemUsersController):
    - Audited all critical user lifecycle events: create, update, delete.
    - Audited sensitive security operations: resetPassword, updateStatus.
    - Audited bulk operations: batchOperation.
    - Descriptions were crafted to be informative, e.g., "Reset password for system user X".
    - Example Action: SYSTEM_USER_PASSWORD_RESET

3. Permission Management (PermissionsController):
    - Audited the creation, update, and deletion of individual permission records.
    - Audited high-level, system-wide operations: syncPermissions and scanPermissions.
    - Replaced previous manual logging calls with the @Audit decorator for consistency and to leverage its advanced features (like context extraction).
    - Removed the now-redundant SystemLogService dependency from this controller.
    - Example Action: PERMISSION_SYNC

Architectural Impact:
- Declarative Auditing: By using decorators, the intent to audit an operation is clearly declared in the code, improving readability and maintainability.
- Centralized Logic: The core auditing logic remains centralized in the AuditInterceptor and AuditLogService, preventing code duplication.
- Comprehensive Coverage: Key administrative and security-sensitive functions are now under automated audit, significantly improving the system's security posture and traceability.
- No Manual Intervention Required: The LoggingInterceptor continues to provide a baseline audit for all API calls, while the @Audit decorator provides deep, specific auditing where it matters most, creating a robust, multi-layered audit system.

Conclusion:
The system now has a powerful, automated mechanism for auditing its most sensitive operations. This fulfills the requirements of the task and provides a strong foundation for security monitoring, compliance, and incident response.
</info added on 2025-06-20T11:10:38.897Z>

## 5. 創建審計日誌查詢和管理API (Create Audit Log Query and Management API) [done]
### Dependencies: 20.1
### Description: 開發RESTful API端點，用於查詢、過濾審計日誌。管理功能可能包括日誌歸檔策略的配置（如果適用）。
### Details:
API應支持按時間範圍、用戶、操作類型、目標資源等多維度查詢和篩選，並支持分頁和排序。所有API訪問必須嚴格遵守CASL權限控制和tenant隔離原則。
<info added on 2025-06-20T12:02:46.025Z>
1. 已實現AuditLogsController，提供三個主要端點：
   - GET /admin/audit-logs：支持分頁和多維度過濾的日誌查詢
   - GET /admin/audit-logs/stats：提供聚合統計資訊用於儀表板
   - GET /admin/audit-logs/actions：返回可用的審計操作類型供過濾使用
2. 查詢功能支持以下過濾條件：
   - 分頁（page, limit）
   - 事件類型（event_type）
   - 用戶ID（user_id）
   - 時間範圍（startDate, endDate）
   - 關鍵字搜索（search）
3. 安全性實現：
   - 使用CASL授權：@CheckPolicies(Actions.READ, Subjects.AUDIT_LOG)
   - 完整的tenant隔離
   - 敏感資料過濾
4. 所有API端點都已經過單元測試驗證，確保功能正確性和安全性。
</info added on 2025-06-20T12:02:46.025Z>

## 6. 實現審計日誌的安全存儲和查詢優化 (Implement Secure Storage and Query Optimization for Audit Logs) [done]
### Dependencies: 20.1, 20.5
### Description: 確保審計日誌在`system_logs`表中安全存儲，並對查詢性能進行優化，以應對大量日誌數據。
### Details:
評估並實施必要的安全措施，如數據加密（如果尚未實施）、訪問控制強化。針對`system_logs`表的查詢優化可能包括索引策略調整。確保符合tenant隔離和CASL要求。
<info added on 2025-06-21T01:12:59.835Z>
完成內容：
查詢性能優化：
為 system_logs 表添加了 10 個效能索引。
包含單一字段索引：tenant_id, user_id, action, target_resource, status, level, created_at。
包含複合索引：[tenant_id, created_at], [tenant_id, action], [tenant_id, user_id, created_at]。

安全存儲增強：
整合 EncryptionService 到 AuditLogService。
對敏感字段（IP 地址、詳細信息）進行加密存儲。
實現 encryptSensitiveField 和 decryptSensitiveField 方法。
新增 encryptDetailsForStorage 方法處理複雜數據結構加密。
實現 parseDecryptedDetails 方法支持多種格式解密。
在查詢時自動解密敏感數據。

向後兼容性：
支持舊格式數據的解密。
確保加密失敗時不影響主要業務流程。
提供完整的錯誤處理和日誌記錄。

模組整合：
更新 CommonModule 以導入 EncryptionModule。
確保代碼編譯成功無錯誤。

系統現在具備了高效能的審計日誌查詢能力和強化的安全存儲機制。
</info added on 2025-06-21T01:12:59.835Z>

## 7. 開發審計報告和匯出功能 (Develop Audit Report Generation and Export Functionality) [done]
### Dependencies: 20.5
### Description: 實現生成可配置的審計報告，並提供將審計日誌數據以常用格式（如CSV、JSON）導出的功能。
### Details:
報告功能應允許用戶根據查詢API提供的篩選條件生成。導出功能需確保數據完整性，並在導出過程中應用敏感資料過濾。所有操作均需符合CASL權限和tenant隔離。
<info added on 2025-06-21T01:47:58.054Z>
子任務20.7「開發審計報告和匯出功能」已成功完成！

完成的功能：

1. AuditLogsController 控制器：
   - 創建完整的審計日誌控制器，提供四個主要端點
   - GET /admin/audit-logs：支持分頁和多維度過濾的日誌查詢
   - GET /admin/audit-logs/stats：提供聚合統計資訊用於儀表板
   - GET /admin/audit-logs/actions：返回可用的審計操作類型供過濾使用
   - POST /admin/audit-logs/reports/generate：生成並匯出審計報告

2. 多格式報告匯出：
   - JSON 格式：包含元數據和完整日誌數據的結構化報告
   - CSV 格式：支持可選的詳細信息欄位，適合數據分析
   - Excel 格式：使用 ExcelJS 生成專業的 .xlsx 報告，包含標題樣式和列寬調整

3. 安全性與權限控制：
   - 使用 PoliciesGuard 和 @CheckPolicies 裝飾器進行細粒度權限控制
   - 所有端點都檢查 Actions.READ, Subjects.SYSTEM_LOG 權限
   - 完整的租戶隔離機制，確保用戶只能訪問自己租戶的審計日誌
   - 在報告生成過程中應用敏感資料過濾

4. 查詢功能：
   - 支持按時間範圍、用戶ID、事件類型、目標資源等多維度過濾
   - 分頁支持，可處理大量日誌數據
   - 關鍵字搜索功能
   - 狀態過濾功能

5. 依賴管理：
   - 使用 pnpm 安裝 ExcelJS 依賴
   - 正確的模組註冊和導入路徑修復

6. 架構整合：
   - 控制器已註冊到 AdminModule
   - 與現有 AuditLogService 完美整合
   - 遵循現有的代碼結構和最佳實踐
   - 專案編譯成功無錯誤

此功能為管理員提供了完整的審計日誌查詢、統計和報告匯出功能，滿足合規性和監控需求。
</info added on 2025-06-21T01:47:58.054Z>

## 8. 測試審計系統完整性和性能 (Test Audit System Integrity and Performance) [done]
### Dependencies: 20.1, 20.2, 20.3, 20.4, 20.5, 20.6, 20.7
### Description: 對整個審計日誌系統進行全面的測試，包括功能正確性、安全性、合規性和性能。
### Details:
編寫單元測試、集成測試和端到端測試。驗證AuditLogService、@Audit裝飾器、LoggingInterceptor增強、API、報告和導出功能的正確性。特別測試tenant隔離、CASL權限控制、敏感資料過濾的有效性，以及系統在高負載下的性能和日誌記錄的可靠性。確保提供完整的測試覆蓋。

## 9. 實作非同步日誌處理以提升效能 [done]
### Dependencies: None
### Description: 引入 @nestjs/event-emitter，將 LoggingInterceptor 改為發送事件，並建立 AuditListener 處理背景日誌寫入，以降低 API 回應延遲並提高系統吞t吐量。
### Details:
<info added on 2025-06-21T02:54:20.789Z>
完成的功能：
LoggingInterceptor 非同步重構：
移除了直接調用 SystemLogService 和 AuditLogService 的同步操作
改為使用 EventEmitter2 發送事件：audit.system.log 和 audit.detailed.log
API 請求處理不再等待日誌寫入完成，大幅提升回應速度

SystemLogService 事件監聽器：
新增 @OnEvent('audit.system.log') 監聽器方法 handleSystemLogEvent
在背景處理系統日誌寫入，包含完整的錯誤處理機制
確保日誌處理失敗不影響主要業務流程

AuditLogService 事件監聽器：
新增 @OnEvent('audit.detailed.log') 監聽器方法 handleDetailedLogEvent
處理詳細的審計日誌記錄，支援完整的上下文資訊
包含錯誤處理和日誌記錄機制

測試完整性：
更新了所有 LoggingInterceptor 的單元測試
修改測試邏輯以驗證事件發送而非直接服務調用
所有 5 個測試案例均通過，確保功能正確性

架構優勢：
效能提升：API 回應時間不再受日誌寫入延遲影響
可靠性：日誌處理錯誤不會中斷主要業務流程
可擴展性：未來可以輕鬆添加更多事件監聽器
向後兼容：保持原有的日誌記錄功能和格式

這個重構為後續的日誌歸檔和更高級的非同步處理奠定了堅實的基礎。系統現在具備了更好的效能和擴展性。
</info added on 2025-06-21T02:54:20.789Z>

## 10. 擴充系統設定以支援動態歸檔 [done]
### Dependencies: 20.9
### Description: 在 schema.prisma 的 system_settings 中新增日誌歸檔相關欄位（啟用、排程時間、保留天數、儲存位置），並建立 archived_audit_logs 資料表。完成後執行資料庫遷移。
### Details:
<info added on 2025-06-21T03:04:16.155Z>
完成的功能：
設定類別擴展：
在 SettingCategory 枚舉中新增 ARCHIVING 類別
支援日誌歸檔的完整配置管理
歸檔設定 DTO 和介面：
創建 UpdateArchivingSettingsDto 包含完整的歸檔配置選項
創建 ArchivingSettings 介面定義歸檔設定結構
提供 DEFAULT_ARCHIVING_SETTINGS 預設配置
SettingsService 歸檔支援：
新增 getArchivingSettings() 方法讀取歸檔設定
新增 updateArchivingSettings() 方法更新歸檔設定
新增 updateArchivingStatus() 方法供歸檔服務更新執行狀態
實現基本的 cron 表達式驗證
支援敏感資料加密存儲
SettingsController API 端點：
GET /admin/settings/archiving：讀取歸檔設定
PUT /admin/settings/archiving：更新歸檔設定
完整的權限控制和審計日誌記錄
資料庫結構：
創建 archived_audit_logs 資料表用於存儲歸檔記錄
包含原始日誌資料和歸檔元數據
完善的索引優化以支援高效查詢
成功執行資料庫遷移
歸檔配置選項：
啟用/停用自動歸檔
cron 排程表達式配置
日誌保留天數設定
多種儲存提供商支援 (local, s3, azure, gcs)
壓縮和檔案格式選項
批次處理大小控制
歸檔後刪除選項
安全性和可靠性：
敏感資料自動加密
完整的錯誤處理
事件發射機制供其他服務監聽
TypeScript 類型安全
架構整合：
與現有設定系統完美整合
支援租戶隔離
遵循專案代碼規範
專案編譯成功無錯誤
系統現在具備了完整的動態歸檔配置基礎，為後續的儲存服務和歸檔服務實現提供了堅實的設定管理支援。
</info added on 2025-06-21T03:04:16.155Z>

## 11. 建立可擴充的儲存服務與工廠 [done]
### Dependencies: 20.10
### Description: 建立抽象的 StorageService 和 StorageFactory，並提供 S3StorageService 的實作，以支援未來將日誌歸檔到不同的雲端儲存位置。
### Details:
<info added on 2025-06-21T03:14:46.028Z>
成功建立了可擴充的儲存服務架構，包含以下核心組件：

完成的功能

1. 抽象儲存介面 (IStorageService)
- 定義了完整的儲存操作介面
- 支援檔案上傳、下載、刪除、複製、移動等操作
- 包含檔案資訊查詢、目錄列表、公開URL生成等功能
- 支援檔案壓縮和連接測試

2. 基礎儲存服務 (BaseStorageService)
- 提供抽象基礎類別，包含通用實作和輔助方法
- 檔案名稱生成、路徑清理、內容類型判斷等工具方法
- 統一的錯誤處理和日誌記錄
- 檔案驗證和格式化功能

3. 本地儲存實作 (LocalStorageService)
- 完整的本地檔案系統儲存實作
- 支援目錄自動創建、檔案壓縮、遞迴列表等功能
- 包含檔案複製、移動、gzip壓縮等進階功能
- 連接測試和權限驗證

4. S3 儲存實作 (S3StorageService)
- 完整的 AWS S3 儲存服務實作
- 支援 S3 的所有基本操作：上傳、下載、刪除、複製等
- 預簽名 URL 生成，支援公開和私有檔案
- 元數據管理和 ACL 控制
- 分頁列表和批次操作支援

5. 儲存工廠 (StorageFactory)
- 統一的儲存服務創建和管理
- 支援多種儲存提供商：local、s3（azure、gcs 預留）
- 實例緩存機制，提升性能
- 配置驗證和連接測試
- 為歸檔系統提供專用的儲存服務創建方法

6. 服務包裝 (StorageService)
- 保持向後相容性，現有代碼無需修改
- 提供進階 API，支援新的儲存功能
- 統一的服務入口點

技術特點

架構設計
- 工廠模式：統一創建和管理儲存服務實例
- 抽象工廠：支援多種儲存提供商
- 單例緩存：避免重複創建實例
- 介面隔離：清晰的抽象和實作分離

可擴充性
- 新增儲存提供商只需實作 IStorageService 介面
- 支援 Azure Blob Storage 和 Google Cloud Storage 擴展
- 配置驅動，支援動態切換儲存提供商
- 插件式架構，易於維護和測試

安全性
- 路徑清理和驗證，防止路徑遍歷攻擊
- 檔案驗證和類型檢查
- 認證資訊環境變數管理
- ACL 和權限控制支援

性能優化
- 實例緩存減少創建開銷
- 流式處理大檔案
- 檔案壓縮支援
- 批次操作和分頁查詢

檔案結構
src/modules/core/storage/
├── interfaces/
│   └── storage.interface.ts     # 儲存服務介面定義
├── base/
│   └── base-storage.service.ts  # 抽象基礎服務
├── implementations/
│   ├── local-storage.service.ts # 本地儲存實作
│   └── s3-storage.service.ts    # S3 儲存實作
├── storage.factory.ts           # 儲存工廠
├── storage.service.ts           # 服務包裝
├── storage.module.ts            # NestJS 模組
└── index.ts                     # 匯出索引

準備就緒
- TypeScript 編譯通過
- 所有依賴已安裝 (@aws-sdk/client-s3, @aws-sdk/s3-request-presigner)
- 模組註冊完成
- 向後相容性保持
- 為歸檔系統提供完整的儲存基礎設施

下一步可以開始實作 LogArchivingService，利用這個強大的儲存架構來實現動態配置的日誌歸檔功能。
</info added on 2025-06-21T03:14:46.028Z>

## 12. 重構 LogArchivingService 以實現動態配置 [done]
### Dependencies: 20.11
### Description: 修改 LogArchivingService，使其能從資料庫的 system_settings 中動態讀取歸檔設定，並透過 StorageFactory 動態選擇儲存提供商來執行歸檔。
### Details:


## 13. 整合模組並更新動態排程機制 [done]
### Dependencies: 20.12
### Description: 更新 CommonModule 與 AppModule 的依賴，確保 LogArchivingService 的動態排程能根據系統設定正確註冊與執行。編寫單元與整合測試以驗證歸檔功能。
### Details:
<info added on 2025-06-21T03:55:12.649Z>
已完成的工作

1. 動態排程機制實現
重構LogArchivingService：添加OnModuleInit和OnModuleDestroy接口，實現模組生命週期管理
動態排程設定：實現setupDynamicSchedule()方法，根據資料庫設定動態創建和更新排程
排程清理機制：實現cleanupSchedule()方法，確保舊排程正確清理
cron表達式解析：實現getNextExecutionTime()方法，支援基本的cron表達式解析

2. 事件驅動的排程更新
事件監聽器：添加@OnEvent('archiving.settings.updated')裝飾器，監聽設定更新事件
自動重新配置：當歸檔設定更新時，自動重新設定排程任務
與SettingsService整合：利用現有的事件發射機制實現動態更新

3. 模組依賴確認與整合
CommonModule配置：確認LogArchivingService已正確註冊並導出
AppModule整合：確認ScheduleModule.forRoot()已正確配置
依賴注入：確認所有必要的服務（PrismaService、SettingsService、StorageFactory、EventEmitter2、SchedulerRegistry）都已正確注入

4. 測試覆蓋
單元測試：創建log-archiving.service.spec.ts，測試動態排程功能
整合測試：創建log-archiving.integration.spec.ts，測試完整模組整合
測試通過：所有測試都成功通過，確保功能正常

5. 技術實現細節
使用setTimeout替代CronJob：避免cron套件版本衝突問題
SchedulerRegistry管理：使用NestJS內建的SchedulerRegistry管理排程任務
錯誤處理：添加完整的錯誤處理和日誌記錄
記憶體管理：確保排程任務正確清理，避免記憶體洩漏

6. 編譯和運行驗證
編譯成功：後端應用成功編譯，沒有TypeScript錯誤
依賴安裝：成功安裝cron套件及其類型定義
模組載入：確認所有模組正確載入和初始化

功能特性

動態排程功能
支援根據system_settings中的歸檔配置動態設定排程
當archiving_enabled設定變更時自動啟用/停用排程
支援動態更新排程間隔（cron表達式）
自動清理舊排程任務，避免衝突

事件驅動架構
監聽'archiving.settings.updated'事件
自動響應設定變更並重新配置排程
與現有的SettingsService事件機制完美整合

模組整合
完整的依賴注入配置
正確的模組生命週期管理
與現有系統架構無縫整合

子任務20.13現已完成，整個任務20（系統級審計日誌開發）的所有13個子任務都已完成。系統現在擁有完整的動態配置歸檔機制。
</info added on 2025-06-21T03:55:12.649Z>

