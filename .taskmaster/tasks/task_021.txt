# Task ID: 21
# Title: Develop `CreateTaskTool` for Agent
# Status: done
# Dependencies: 12
# Priority: medium
# Description: Create a Lang<PERSON>hain `Tool` that allows the Agent to create new tasks within a project, based on natural language instructions, by calling the `tasks.service`.
# Details:
Create `CreateTaskTool extends Tool`. `name = "CreateTaskTool"`, `description = "Creates a new task..."`. `_call(input: string): Promise<string>`: Parse `input` for task details (title, project, assignee, deadline - may need LLM or specific input format from agent). Call `tasks.service.createTask({ ...parsedDetails, tenantId })`. Return success message with task ID.

# Test Strategy:
Unit test `CreateTaskTool._call` with mock `tasks.service`. Integration test: Agent uses tool to create a (mocked) task.
