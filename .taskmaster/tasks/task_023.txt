# Task ID: 23
# Title: Develop `SendMessageTool` for Agent
# Status: done
# Dependencies: 12
# Priority: medium
# Description: Create a Lang<PERSON>hain `Tool` that enables the Agent to send messages to users, e.g., via LINE or internal workspace chat, for notifications or confirmations.
# Details:
Create `SendMessageTool extends Tool`. `name = "SendMessageTool"`, `description = "Sends a message..."`. `_call(input: string): Promise<string>`: Parse input for recipient (user/channel/LINE ID) and message. Call `notification.service.sendMessage({ recipient, message, tenantId })`. Return success message.

# Test Strategy:
Unit test `SendMessageTool._call`. Integration test: Agent uses tool to send a (mocked) message.
