# Task ID: 26
# Title: 建立 Agent 工具管理系統的資料庫結構
# Status: done
# Dependencies: 19
# Priority: high
# Description: 創建支援 Agent 自定義的核心資料庫架構，包括 AiTool 模型用於註冊系統工具，AiBotTool 關聯表建立 Agent 與工具的多對多關係，以及擴展 AiBot 模型支援執行類型區分。
# Details:
在 schema.prisma 中新增:
1. **AiTool 模型**: 包含 id, key (程式化名稱), name (顯示名稱), description (工具描述), input_schema (JSON Schema), is_enabled 等欄位
2. **AiBotTool 關聯表**: 多對多關聯表，連接 AiBot 和 AiTool，包含 bot_id, tool_id, created_at
3. **擴展 AiBot 模型**: 新增 execution_type 欄位 (SINGLE_CALL | GRAPH)，以及 tools 反向關聯
4. 建立資料庫遷移並同步 Prisma Client
5. 確保租戶隔離：AiTool 可能需要 tenant_id 或設為系統級別資源

# Test Strategy:
建立遷移後驗證新的模型關係。測試 AiBot 與 AiTool 的多對多關聯創建和查詢。驗證 execution_type 欄位的枚舉值限制。確認租戶隔離機制正確應用。

# Subtasks:
## 1. 26.1: 定義枚舉與核心模型 (Enums & Core Models) [done]
### Dependencies: None
### Description: 在 schema.prisma 中一次性定義 AiToolScope, AiBotExecutionType 枚舉，以及 AiTool, AiBotTool 模型，並擴展 AiBot 模型。同時設定好所有索引和關聯。
### Details:


## 2. 26.2: 建立與驗證資料庫遷移 (Database Migration) [done]
### Dependencies: 26.1
### Description: 執行 prisma migrate 創建遷移檔案，並運行 prisma generate 更新 Prisma Client。
### Details:


## 3. 26.3: 建立 AI 工具管理模組與控制器 (Module & Controller Setup) [done]
### Dependencies: 26.2
### Description: 在 `apps/backend/src/modules/ai/models/configuration/tools/` 路徑下，建立 `tools` 模組 (ai-tools.module.ts, ai-tools.service.ts, ai-tools.controller.ts)。
### Details:


## 4. 26.4: 實作工具的 CRUD API (Tool CRUD API) [done]
### Dependencies: 26.3
### Description: 在新建的 AiToolsController 中，實作對 AiTool 資源的 GET(列表/單一)、POST(新增)、PATCH(更新)、DELETE 操作的 API 端點。路由為 admin/ai/tools。
### Details:


## 5. 26.5: 實作 Bot-工具指派 API (Bot-Tool Assignment API) [done]
### Dependencies: 26.3
### Description: 在現有的 AiBotsController 中，新增 API 端點來管理 AiBot 與 AiTool 的關聯。例如：GET /admin/ai/bots/{botId}/tools 和 PATCH /admin/ai/bots/{botId}/tools。
### Details:


## 6. 26.6: 整合權限控制 (Permissions Integration) [done]
### Dependencies: 26.4, 26.5
### Description: 為所有新建立的 API 端點，定義對應的權限 (Action: 'manage', Subject: 'AiTool')，並使用 @CheckPolicies 裝飾器進行保護。
### Details:


## 7. 26.7: 創建基礎種子數據 (Seed Data) [done]
### Dependencies: 26.2
### Description: 在 seed.ts 中為 AiTool 添加一些系統級的預設工具，以便測試和演示。
### Details:


## 8. 26.8: 編寫後端單元與整合測試 (Unit & Integration Tests) [done]
### Dependencies: 26.6, 26.7
### Description: 為 ToolsService 的業務邏輯編寫單元測試，並為 ToolsController 的 API 端點編寫 E2E 或整合測試，驗證包含權限在內的完整流程。
### Details:


