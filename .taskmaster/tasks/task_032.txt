# Task ID: 32
# Title: 增強價格爬取系統以支援多個官方網站 (Enhance Price Scraping System to Support Multiple Official Websites)
# Status: pending
# Dependencies: 6
# Priority: medium
# Description: 實現一個多策略的價格爬取引擎，能夠分析並解析來自不同LLM供應商官方定價頁面（如OpenAI、Anthropic）的價格數據，並整合現有的 `llmpricecheck.com` 爬取邏輯。這將提高價格數據的可靠性與即時性。
# Details:
1. **實現策略模式 (Strategy Pattern)**：在 `AiPriceCrawlerService` 中建立一個策略管理器，能根據 URL 動態選擇解析策略。
2. **開發 OpenAI 解析器**: 創建一個專門解析 OpenAI 官方定價頁面結構的爬蟲與解析器。
3. **開發 Anthropic 解析器**: 創建一個專門解析 Anthropic 官方定價頁面結構的爬蟲與解析器。
4. **整合現有邏輯**: 將目前針對 `llmpricecheck.com` 的爬取邏輯封裝成一個獨立的策略。
5. **更新主流程**: 修改 `syncAllPrices` 等方法，使其能夠接收一個 URL 列表，並對每個 URL 應用相應的爬取策略。
6. **錯誤處理與日誌**: 為新的爬取策略實現健全的錯誤處理和詳細日誌記錄。

# Test Strategy:
1. 針對每個解析器編寫單元測試，模擬目標網站的 HTML 結構，驗證價格提取的正確性。
2. 進行整合測試，實際爬取各個目標網站，確保端到端流程正常運作。
3. 驗證資料庫中的 `ai_models` 表格是否被正確更新。
