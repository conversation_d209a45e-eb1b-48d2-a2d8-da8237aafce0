# Task ID: 34
# Title: 全系統重構：將「AI Bot」概念統一為「AI Agent」
# Status: pending
# Dependencies: 28, 7
# Priority: high
# Description: 對整個技術棧進行系統性的重構，將所有與「AI Bot」相關的概念和實作，全面替換為「AI Agent」。這旨在統一目前平行的 `ai_bots` 和 `agent` 模組，並使命名更貼近主流 AI 框架的術語。
# Details:
**階段一：後端核心重構 (資料庫與 API)**\n1.  **修改 Prisma Schema (`schema.prisma`)**: 將 `AiBot` 模型重命名為 `AiAgent`，同步更新所有相關的枚舉和關聯，並新增 `agent_type` 欄位以區分執行類型 (例如 `SIMPLE_COMPLETION`, `CONVERSATIONAL_AGENT`)。\n2.  **執行資料庫遷移**: 產生並應用新的資料庫遷移。\n3.  **重構後端模組**: 將 `apps/backend/src/modules/ai/bots` 目錄、檔案、類別、DTO 和 API 端點全面重命名為 `agents`。\n\n**階段二：整合 Agent 執行邏輯**\n1.  **建立中央執行服務 (`AIAgentExecutionService`)**: 作為統一的執行入口，根據 `AiAgent` 的 `agent_type` 調度不同的執行引擎。\n2.  **改造 `AgentRunnerService`**: 將現有的 `AgentRunnerService` 改造為一個純粹的 `CONVERSATIONAL_AGENT` 執行器，使其接收一個 `AiAgent` 物件作為設定。\n3.  **刪除 `AgentController`**: 移除獨立的 `/api/agent` 端點，所有請求將統一由新的 `AiAgentsController` 處理。\n\n**階段三：前端介面重構**\n1.  **檔案與目錄重命名**: 將所有前端與 `AIBot` 相關的 Vue 組件、服務和模型檔案重命名為 `AIAgent`。\n2.  **更新 API 呼叫**: 修改前端服務以呼叫新的 `/api/ai/agents` 後端端點。\n3.  **更新 UI 文字與路由**: 全面更新前端介面、路由和側邊欄，將所有可見的「Bot」替換為「Agent」。\n\n**階段四：規則與文件更新**\n1.  **更新 `AISystemGuide.mdc`**: 修改所有相關的規則文件，將 `AiBot` 的定義和描述替換為 `AiAgent`，確保文件與程式碼同步。

# Test Strategy:
- **後端**: 重構後運行所有現有測試。為 `AIAgentExecutionService` 新增單元測試。為新的 `/api/ai/agents` 端點編寫 E2E 測試。\n- **前端**: 手動測試所有 Agent 管理介面（列表、創建、編輯、刪除），確保 API 呼叫成功且資料顯示正確。

# Subtasks:
## 1. 階段一：後端核心重構 (資料庫與 API) [done]
### Dependencies: None
### Description: 修改 Prisma Schema，重命名相關實體，遷移資料庫，並重構後端模組、服務與 API 端點，將 'Bot' 全面替換為 'Agent'。
### Details:
<info added on 2025-06-21T15:03:52.991Z>
已完成後端模組重構的主要部分：

已完成：
1. 資料庫 Schema 重構 - 已將 `ai_bots` 表重命名為 `ai_agents`，所有相關 enum 和關聯都已更新
2. 資料庫遷移 - 已成功應用新的 schema 並重設資料庫
3. 檔案和目錄重新命名 - 將 `/bots/` 目錄重命名為 `/agents/`，所有相關檔案都已重新命名
4. DTO 類別重構 - 將所有 `*BotDto` 重命名為 `*AgentDto`，更新了屬性和描述
5. Service 類別重構 - `AiBotsService` 重命名為 `AiAgentsService`，所有方法和邏輯都已更新
6. Controller 類別重構 - `AiBotsController` 重命名為 `AiAgentsController`，API 路由從 `/admin/ai/bots` 更改為 `/admin/ai/agents`
7. Module 重構 - 更新了模組配置和依賴注入
8. 測試檔案重構 - 更新了單元測試以反映新的類別名稱
9. LLM 介面更新 - 將 `AiBotProviderType` 更新為 `AiAgentProviderType`

目前遇到的問題：
- Prisma Client 類型生成有延遲，部分新的 enum 類型（如 `AiAgentScope`, `AiAgentProviderType` 等）還未完全更新
- 需要重新啟動 TypeScript 語言服務或重新生成 Prisma Client

下一步：
需要繼續進行其他模組的更新，包括業務整合服務、Agent Runner Service 等檔案中的引用更新。
</info added on 2025-06-21T15:03:52.991Z>

## 2. 階段二：整合 Agent 執行邏輯 [done]
### Dependencies: 34.1
### Description: 建立統一的 AIAgentExecutionService，改造現有的 AgentRunnerService，並移除冗餘的 AgentController，將執行邏輯整合進新的 Agent 框架。
### Details:
<info added on 2025-06-21T15:17:27.517Z>
完成了 Agent 執行邏輯整合：

已完成的整合工作

1. AiAgentsService 整合
將 AgentRunnerService 的核心執行邏輯整合到 AiAgentsService 中
添加了 runAgent 方法，支持完整的 LangChain Agent 執行
整合了權限檢查、配額管理、錯誤處理等功能
保持了多租戶隔離和安全性

2. 執行功能整合
LLM 初始化（支持 OpenAI、Anthropic、Google）
工具系統整合（動態工具 + 傳統工具）
Agent 建立和執行（OpenAI Functions Agent + ReAct Agent）
結構化日誌記錄和使用量追蹤
重試機制和錯誤處理

3. API 端點整合
在 AiAgentsController 中添加了 /admin/ai/agents/:id/run 端點
添加了 /admin/ai/agents/tools/available 端點
添加了 /admin/ai/agents/status 端點
統一了 API 路由到 /admin/ai/agents 前綴

4. 依賴管理
更新了 AiAgentsModule 以包含所有必要的依賴
整合了工具系統相關的服務和工廠

5. 架構改進
建立了統一的 Agent 執行入口
保持了向後兼容性（支持傳統工具）
支持新的動態工具框架
統一了錯誤處理和日誌記錄

技術細節

核心方法：
runAgent() - 主要執行入口
executeAgentWithLangChain() - LangChain 執行邏輯
initializeToolsForExecution() - 工具初始化
initializeLLMForExecution() - LLM 初始化

API 路由變更：
原 /agent/run → 新 /admin/ai/agents/:id/run
原 /agent/tools → 新 /admin/ai/agents/tools/available
原 /agent/status → 新 /admin/ai/agents/status

整合完成後，現在有了統一的 Agent 管理和執行框架，可以根據 agent_type 調度不同的執行引擎。
</info added on 2025-06-21T15:17:27.517Z>

## 3. 階段三：前端介面重構 [in-progress]
### Dependencies: 34.1
### Description: 全面重構前端，包括重命名檔案、更新 API 呼叫、修改路由與所有使用者介面中的文字，將 'Bot' 替換為 'Agent'。
### Details:


## 4. 階段四：規則與文件更新 [pending]
### Dependencies: 34.1, 34.2, 34.3
### Description: 更新 AISystemGuide.mdc 及其他相關專案文件，確保所有開發者文件與新的 'AI Agent' 概念保持一致。
### Details:


