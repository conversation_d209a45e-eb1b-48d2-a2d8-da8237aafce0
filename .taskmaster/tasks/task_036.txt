# Task ID: 36
# Title: [Refactor] 遷移 AI Agents 模組至 Core
# Status: done
# Dependencies: None
# Priority: high
# Description: 將 AI Agents 核心邏輯從 `apps/backend/src/modules/ai/models/agents` 完整遷移至 `apps/backend/src/modules/core/agents`。這包括 services, DTOs, 和 tools。同時，將相關檔案和類別更名以符合核心模組的命名慣例 (e.g., `ai-agents.service.ts` -> `agents.service.ts`)。
# Details:


# Test Strategy:


# Subtasks:
## 1. 創建目標目錄結構 [done]
### Dependencies: None
### Description: 在 `apps/backend/src/modules/core/` 下創建 `agents/` 目錄結構，包括 services, dto, tools 等子目錄
### Details:
- 創建 `apps/backend/src/modules/core/agents/` 主目錄
- 創建子目錄：services/, dto/, tools/
- 確保目錄權限正確

## 2. 遷移 services 目錄 [done]
### Dependencies: None
### Description: 將 `ai/models/agents/services/` 目錄下的所有服務檔案遷移到 `core/agents/services/`
### Details:
- 移動 usage-tracking.service.ts
- 移動其他 service 檔案
- 保持檔案內容不變，僅移動位置

## 3. 遷移 DTOs 目錄 [done]
### Dependencies: None
### Description: 將 `ai/models/agents/dto/` 目錄下的所有 DTO 檔案遷移到 `core/agents/dto/`
### Details:
- 移動所有 DTO 檔案
- 保持檔案內容不變
- 確保檔案結構完整

## 4. 遷移 tools 目錄 [done]
### Dependencies: None
### Description: 將 `ai/models/agents/tools/` 目錄下的所有工具檔案遷移到 `core/agents/tools/`
### Details:
- 移動所有 tool 檔案
- 保持檔案內容不變
- 確保工具類別完整遷移

## 5. 遷移並重命名核心檔案 [done]
### Dependencies: None
### Description: 遷移根目錄的核心檔案並重命名以符合核心模組慣例
### Details:
- 移動 ai-agents.service.ts → agents.service.ts
- 移動 ai-agents.module.ts → agents.module.ts
- 移動 ai-agents.controller.ts → agents.controller.ts (如果存在)
- 移動其他根目錄檔案

## 6. 更新模組內部引用 [done]
### Dependencies: None
### Description: 更新 agents.module.ts 和 agents.service.ts 內部的類別名稱和引用路徑
### Details:
- 將 AiAgentsModule → AgentsModule
- 將 AiAgentsService → AgentsService
- 更新所有內部 import 路徑
- 更新 providers 和 exports 配置

## 7. 更新外部模組引用 [done]
### Dependencies: None
### Description: 更新所有引用舊 AI Agents 模組的外部檔案，將引用路徑指向新的 core/agents 位置
### Details:
- 搜尋所有引用 '@/modules/ai/models/agents' 的檔案
- 更新為 '@/modules/core/agents'
- 更新類別名稱引用 (AiAgentsModule → AgentsModule)
- 確保 app.module.ts 正確引用新模組

## 8. 驗證遷移完整性 [done]
### Dependencies: None
### Description: 驗證遷移後的模組功能完整性，確保所有服務正常運作
### Details:
- 檢查模組是否能正確啟動
- 驗證所有 service 依賴注入正常
- 測試 API 端點是否正常回應
- 確認沒有遺漏的檔案或配置

