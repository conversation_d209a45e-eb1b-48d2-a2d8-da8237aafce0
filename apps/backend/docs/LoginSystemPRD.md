# PRD：租戶自助註冊與公司唯一性驗證（重構版）

## 1. 目標

- 讓企業用戶可自行註冊並開通新租戶（Workspace），自助啟動服務。
- 確保同一家公司（依公司名稱、公司網域、統編）只能有一個租戶帳號，避免重複、資源浪費與管理困擾。
- 新進員工可快速加入現有公司租戶，不另啟多組公司帳號。
- 認證與權限系統完全支援多租戶、角色、細緻權限分配，並前後端共用權限定義。

---

## 2. 角色定義

- **潛在租戶申請人**：欲開通新公司帳號的企業用戶。
- **Workspace Admin**：自助註冊成功後，預設為該公司租戶管理員。
- **現有租戶員工**：欲加入本公司現有租戶的同事。
- **SaaS 系統管理員（SaaS Admin）**：僅在特殊審核或申訴流程中參與。

---

## 3. 流程描述

### 使用者註冊與公司歸屬流程

#### 階段一：建立個人使用者帳號

1.  使用者進入統一的「建立帳號」頁面。
2.  輸入個人基本資料：姓名、Email、密碼（此階段不涉及公司資訊）。
3.  系統檢查 Email 是否已在 `users` 表中註冊。
    - 若已註冊，引導至登入頁面或提供「忘記密碼」選項。
4.  若 Email 未註冊，則建立新的 `users` 記錄，初始狀態可設為 `pending_email_verification`，`tenantId` 暫時為 `null` 或留空。
5.  系統發送 Email 驗證信至使用者信箱。
6.  使用者點擊驗證連結，完成 Email 驗證，個人帳號狀態更新為 `active`。

#### 階段二：引導至「公司歸屬設定」頁面

1.  在使用者完成 Email 驗證後首次登入，或每次登入時若發現其個人帳號尚未歸屬任何公司 (`tenantId` 為空)，系統應將其引導至「公司歸屬設定」頁面。
2.  此頁面應清晰呈現以下三個主要操作路徑，引導使用者根據自身情況選擇：
    - **選項一：「我是首次為我的公司建立 HorizAI 帳號」** (或類似強調「首次建立」的措辭)
      - 對應下方描述的 **路徑 B：「建立新的公司資料」**。
    - **選項二：「我公司的同事已經在使用 HorizAI 了」** (或類似強調「加入已有」的措辯)
      - 對應下方描述的 **路徑 A：「搜尋並申請加入現有公司」**。
    - **選項三：「暫時以個人身份使用 (稍後再設定公司資訊)」**
      - 使用者點擊此選項後，將跳過立即的公司設定流程。
      - 系統將引導使用者進入 PRD 階段三描述的「尚未歸屬公司的使用者」狀態，其能使用的功能會受到限制。
      - 每次登入後，若系統檢測到其 `tenantId` 仍為空，應在其主介面提供明顯但非強制性的入口，再次引導其前往「公司歸屬設定」頁面。
3.  此頁面提供兩個主要操作路徑：
    - **路徑 A：「搜尋並申請加入現有公司」**
      1.  提供搜尋功能（可依公司名稱關鍵字、公司 Email 網域等進行搜尋）。
      2.  系統根據搜尋條件，列出符合或相似的公司供使用者選擇（注意資訊的適當屏蔽，如僅顯示部分網域）。
      3.  使用者選擇一家公司後，點擊「申請加入」。
      4.  系統記錄此申請（例如，在 `TenantInvite` 表中建立一條記錄，或採用其他申請機制），並通知該公司的 Workspace Admin 進行審核。
      5.  Workspace Admin 在其管理後台審核申請。若批准，則將該使用者帳號與對應的 `Tenant` 建立關聯（即更新 `users` 表中的 `tenantId`），並指派適當的租戶內角色（如 `TENANT_USER`）。
    - **路徑 B：「建立新的公司資料」**
      1.  使用者選擇此路徑，進入「建立新公司」的表單。
      2.  表單需填寫：公司名稱、公司主要營運國家/地區（必填）。「商業註冊號碼/Tax ID/統編」等欄位根據所選國家動態顯示，且為選填項。
      3.  系統進行新公司的唯一性檢查：
          - **公司 Email 網域**（若使用者提供的公司聯絡 Email 是自訂網域，非 gmail/outlook 等）是否重複。
            - 若重複，提示「此 Email 網域已被公司註冊」，並引導申請加入現有公司，或提示聯繫客服。
          - **公司名稱 + 國家/地區** 組合是否完全重複。
            - 若有，提示「同國家已有同名公司」，可選擇申請加入、繼續註冊（系統內部標記此類租戶）、或聯繫客服。
          - **公司名稱相似度**（同國家/地區內），若有高度相似名稱，友善提示，並同樣給予繼續註冊選項或聯繫客服。
      4.  若檢查通過，或使用者在有同名提示下確認繼續建立：
          - 系統建立新的 `tenants` 記錄。
          - 將當前已登入的使用者設為此新租戶的 Workspace Admin，即更新該 `users` 記錄的 `tenantId`，並指派相應的 `TENANT_ADMIN` 角色。
          - 引導使用者進入新建立公司的儀表板或初始設定頁面。
      5.  若唯一性檢查發現嚴重衝突（例如，公司自訂 Email 網域已完全重複且無法由使用者自行判斷），則應提示使用者聯繫客服處理。

#### 階段三：後續使用者狀態與操作

1.  **已成功歸屬公司的使用者**：正常登入後，直接進入其所屬公司的操作介面。
2.  **尚未歸屬公司的使用者**（例如，在「公司歸屬設定」頁面選擇了「稍後處理」或直接關閉）：
    - 系統允許此類個人帳號獨立存在。
    - 但其能使用的功能會受到限制（例如，無法存取任何與租戶/工作空間相關的功能）。
    - 每次登入後，若系統檢測到其 `tenantId` 仍為空，應在其主介面提供明顯但非強制性的入口，再次引導其前往「公司歸屬設定」頁面。

---

### 公司資訊審核與例外處理

1. 若在「建立新公司」流程中，公司名稱、網域或統編（如適用）等資訊產生爭議且使用者選擇聯繫客服，則進入客服審查流程。
2. 系統管理員可基於審查結果或特殊情況，強制合併、轉移、刪除多餘或有爭議的租戶（僅內部操作）。

---

## 4. 功能需求

### 必要功能

- 個人使用者帳號建立與 Email 驗證機制。
- 「公司歸屬設定」引導頁面，提供「加入現有公司」與「建立新公司」的選項。
- 搜尋現有公司的功能（依名稱、網域等），並呈現結果列表。
- 「建立新公司」的表單與唯一性校驗邏輯（針對公司名稱+國家、公司Email網域等，Tax ID/統編僅特定地區選填）。
- Workspace Admin 自動指派（於成功建立新公司時）。
- 申請加入現有公司的請求提交與 Workspace Admin 審核機制（後台界面）。
- 支援重發（個人帳號）驗證信與忘記密碼功能。
- 權限模組（CASL 能力模型，前後端共用，需能處理個人帳號與租戶內不同角色）。
- 系統通知（例如：帳號驗證、加入公司申請通知、審核結果通知）與稽核日誌。
- Tax ID/統編僅於台灣等特定地區可選填，非全球唯一性關鍵值。
- 個人帳號獨立存在機制，以及未歸屬公司時的功能限制與引導。

### 選用功能

- 支援 Google、Microsoft、Line 等 SSO 建立個人帳號，並於後續流程中綁定公司網域（若適用）。
- 「建立新公司」時，針對公司資訊（如統編）與第三方開放資料的自動驗證與補全（例如台灣經濟部商業司資料）。
- 公司資訊審核與人工處理申訴的進階管理介面。

---

## 5. 非功能需求

- 資料一致性保證（唯一性檢查需資料庫層面強化）。
- 保證操作流程在 3 秒內完成主要響應。
- 操作界面需支援桌面與手機版。
- 多語言預留欄位（但首發僅提供繁體中文）。

---

## 6. 風險與備註

- 公司名稱、網域有爭議時，需明確顯示人工審查管道，並記錄所有操作。
- 若同公司內不同部門需要獨立 Workspace，需由 Workspace Admin/系統管理員人工申請或轉派。
- 防止惡意註冊（建議結合 Email 驗證與 reCAPTCHA）。

---

## 7. 頁面/功能草圖

- 「建立個人帳號」頁面（欄位：姓名、Email、密碼）。
- Email 驗證成功/失敗提示頁面。
- 「公司歸屬設定」引導頁面：
  - 選項一：「我是首次為我的公司建立 HorizAI 帳號」(引導至建立新公司表單)
  - 選項二：「我公司的同事已經在使用 HorizAI 了」(引導至搜尋現有公司介面)
  - 選項三：「暫時以個人身份使用 (稍後再設定公司資訊)」(引導至受限的個人儀表板)
- 「搜尋現有公司」介面（輸入框、篩選器、搜尋按鈕、結果列表）。
- （加入現有公司）申請已提交/等待審核提示。
- 「建立新公司」表單（欄位：公司名稱、國家/地區，選填的商業註冊號碼等）。
- （建立新公司）唯一性衝突時的提示框（含繼續註冊、聯繫客服等選項）。
- Workspace Admin 審核新成員加入申請的列表與操作介面。
- 成功開通/加入公司後的引導頁面。
- （使用者未歸屬公司時）儀表板或主介面上的「設定公司資訊」引導入口。

---

## 8. 資料表設計（重構建議）

以下資料表設計已根據 `schema.prisma` 的現有模型進行更新，並**提出 User 表拆分的建議**。

**tenants (公司租戶)** - 對應 Prisma `Tenant`
| 欄位名稱 | 資料型態 | 說明 |
|----------------------|------------|----------------------------------------------------------------------|
| id | String | 唯一識別碼 (CUID) |
| name | String | 公司名稱 |
| domain | String? | 公司 Email 網域 (唯一) |
| company_registration_id | String? | 公司統一編號 (唯一，對於需要驗證的地區) |
| status | String | 租戶狀態 (例如：`pending`, `active`, `suspended`) |
| planId | String? | 訂閱方案 ID，關聯到 `Plan` 表 |
| adminEmail | String? | 管理員 Email (通常為第一個 `TenantUser` 的 Email) |
| adminName | String? | 管理員名稱 |
| companySize | String? | 公司規模 |
| industry | String? | 所屬產業 |
| departments | String[] | 部門列表 |
| maxUsers | Int? | 此租戶方案允許的最大使用者數量 (指 `TenantUser`) |
| maxProjects | Int? | 此租戶方案允許的最大專案數量 |
| maxStorage | Int? | 此租戶方案允許的最大儲存空間 (GB) |
| currentAiCredits | Decimal? | 目前 AI 點數餘額 |
| nextBillingDate | DateTime? | 下次計費日期 |
| paymentStatus | String? | 付款狀態 |
| billingCycle | String? | 計費週期 |
| contactEmail | String? | 聯絡人 Email |
| contactName | String? | 聯絡人名稱 |
| createdAt | DateTime | 建立時間 |
| updatedAt | DateTime | 最後更新時間 |

**workspaces (工作空間)** - 對應 Prisma `Workspace`
| 欄位名稱 | 資料型態 | 說明 |
|---------------|------------|----------------------------------------------------------------------|
| id | String | 唯一識別碼 (CUID) |
| name | String | 工作空間名稱 |
| description | String? | 工作空間描述 |
| tenantId | String | 所屬租戶 ID，關聯到 `Tenant` 表 |
| ownerId | String | 擁有者 `TenantUser` ID，關聯到 `TenantUser` 表 (假設拆分後) |
| status | String | 狀態 (例如：`active`, `archived`) |
| settings | Json? | 工作空間特定設定 |
| createdAt | DateTime | 建立時間 |
| updatedAt | DateTime | 最後更新時間 |

---

#### **建議拆分 User 模型**

**SystemUser (系統使用者)** - _建議新增此概念對應的模型 (基於現有 User 調整)_
| 欄位名稱 | 資料型態 | 說明 |
|-----------------|------------|----------------------------------------------------------------------|
| id | String | 唯一識別碼 (CUID) |
| email | String | Email (唯一) |
| password | String | 密碼 (雜湊後儲存) |
| name | String? | 使用者名稱 |
| role | SystemUserRole | 系統使用者角色 (Enum: `SUPER_ADMIN`, `SYSTEM_ADMIN` 等，需基於現有 `UserRole` 調整或新建) |
| status | String | 狀態 (例如：`active`, `suspended`) |
| avatar | String? | 頭像 URL |
| phone | String? | 電話號碼 |
| mfaEnabled | Boolean | 是否啟用多因素認證 |
| mfaSecret | String? | MFA 密鑰 |
| lastLoginAt | DateTime? | 最後登入時間 |
| lastLoginIp | String? | 最後登入 IP |
| passwordLastChangedAt | DateTime? | 密碼最後變更時間 |
| createdAt | DateTime | 建立時間 |
| updatedAt | DateTime | 最後更新時間 |

**TenantUser (租戶使用者)** - _建議新增此概念對應的模型 (基於現有 User 調整)_
| 欄位名稱 | 資料型態 | 說明 |
|-----------------|------------|----------------------------------------------------------------------|
| id | String | 唯一識別碼 (CUID) |
| email | String | Email (在租戶內唯一，或全域唯一，需根據設計決定) |
| password | String | 密碼 (雜湊後儲存) |
| name | String? | 使用者名稱 |
| tenantId | String | **所屬租戶 ID (必填)**，關聯到 `Tenant` 表 |
| role | TenantUserRole | 租戶使用者角色 (Enum: `TENANT_ADMIN`, `TENANT_USER` 等，需基於現有 `UserRole` 調整或新建) |
| status | String | 狀態 (例如：`active`, `invited`, `suspended`) |
| avatar | String? | 頭像 URL |
| phone | String? | 電話號碼 |
| title | String? | 職稱 |
| company | String? | 公司名稱 (使用者自行填寫，可能與 `Tenant.name` 不同) |
| department | String? | 部門 (使用者自行填寫) |
| location | String? | 地點 |
| bio | String? | 個人簡介 |
| socialLinks | Json? | 社群連結 |
| preferences | Json? | 個人偏好設定 |
| lineUserId | String? | LINE 使用者 ID (唯一) |
| lineConnected | Boolean | 是否已連結 LINE |
| lastLogoutAt | DateTime? | 上次登出時間 |
| lastLoginAt | DateTime? | 最後登入時間 |
| lastLoginIp | String? | 最後登入 IP |
| googleId | String? | Google ID (唯一) |
| passwordLastChangedAt | DateTime? | 密碼最後變更時間 |
| mfaEnabled | Boolean | 是否啟用多因素認證 |
| mfaSecret | String? | MFA 密鑰 |
| createdAt | DateTime | 建立時間 |
| updatedAt | DateTime | 最後更新時間 |
| _invited_by_ | String? | _邀請者 `TenantUser` ID (若由 `TenantInvite` 處理，此欄位可選)_ |

_(註：上述 `SystemUserRole` 和 `TenantUserRole` 為建議調整或新增的 Enum，以更清晰地界定角色。或者，可以繼續使用現有的 `UserRole` Enum，但在應用程式邏輯中嚴格區分其適用範圍。實際 Prisma 模型中可能仍為單一 `UserRole` Enum，透過業務邏輯區分。)_

**前端識別使用者類型說明**：
為了讓前端能夠清晰辨識當前登入的使用者屬於 `SystemUser` 還是 `TenantUser`，後端 API (`/api/auth/me`) 在回應使用者資訊時，應明確包含一個 `userType` 欄位 (值為 `'system'` 或 `'tenant'`)。前端的狀態管理 (`@horizai/auth` 套件中的 `auth.store.ts`) 將儲存此 `userType`，並可定義一個可辨識的聯合類型 (discriminated union) `CurrentUser` (如 `SystemUser | TenantUser | null`)。如此一來，前端的路由守衛、UI 元素顯隱及特定邏輯便能根據 `userType` 做出正確判斷。

---

**roles (角色)** - 對應 Prisma `Role` (需評估其 `scope` 與 User 拆分的對應關係)
| 欄位名稱 | 資料型態 | 說明 |
|--------------|------------|----------------------------------------------------------------------|
| id | String | 唯一識別碼 (CUID) |
| name | String | 角色內部名稱 (唯一) |
| displayName | String | 角色顯示名稱 |
| description | String? | 角色描述 |
| scope | RoleScope | 角色範圍 (Enum: `SYSTEM`, `TENANT`, `WORKSPACE`) |
| tenantId | String? | 若為租戶特定角色 (`scope=TENANT` 或 `WORKSPACE`)，其租戶 ID |
| isSystem | Boolean | 是否為系統預設角色 (此欄位可能與 `scope` 部分重疊，需釐清) |
| parentRoleId | String? | 父角色 ID (用於角色層級結構) |
| createdAt | DateTime | 建立時間 |
| updatedAt | DateTime | 最後更新時間 |

**user_roles (使用者角色對應)** - 對應 Prisma `UserRoleMapping` (需調整 `userId` 以對應到 `SystemUser` 或 `TenantUser` 的概念)
| 欄位名稱 | 資料型態 | 說明 |
|------------|------------|----------------------------------------------------------------------|
| id | String | 唯一識別碼 (CUID) |
| userId | String | 使用者 ID (在實作拆分後，此 ID 需能區分來源是 `SystemUser` 或 `TenantUser`) |
| userType | String? | _建議新增欄位_：使用者類別 (例如: 'system', 'tenant')，輔助區分 userId 的來源表。**此欄位對於後端建構權限及前端辨識使用者類型至關重要。** |
| roleId | String | 角色 ID，關聯到 `Role` 表 |
| createdAt | DateTime | 建立時間 |
| updatedAt | DateTime | 最後更新時間 |

**permission_categories (權限分類)** - 對應 Prisma `PermissionCategory`
| 欄位名稱 | 資料型態 | 說明 |
|-------------|------------|----------------------------------------------------------------------|
| id | String | 唯一識別碼 (CUID) |
| name | String | 分類名稱 (唯一) |
| description | String? | 分類描述 |
| icon | String? | 圖示 |
| sortOrder | Int | 排序順序 |
| isActive | Boolean | 是否啟用 |
| createdAt | DateTime | 建立時間 |
| updatedAt | DateTime | 最後更新時間 |

**permissions (權限)** - 對應 Prisma `Permission`
| 欄位名稱 | 資料型態 | 說明 |
|-----------------|-----------------|----------------------------------------------------------------------|
| id | String | 唯一識別碼 (CUID) |
| name | String? | 權限的易讀名稱 |
| action | String | 操作 (例如：`create`, `read`, `update`, `delete`) |
| subject | String | 主體 (例如：`SystemUser`, `TenantUser`, `Project` 等，需反映 User 拆分) |
| conditions | Json? | 權限條件 (CASL) |
| description | String? | 權限描述 |
| categoryId | String? | 權限分類 ID，關聯到 `PermissionCategory` 表 |
| scope | PermissionScope?| 權限範圍 (Enum: `SYSTEM`, `TENANT`, `WORKSPACE`, `GLOBAL`) |
| isSystemDefined | Boolean | 是否為系統定義 |
| deprecated | Boolean | 是否已棄用 |
| fields | String[] | 權限相關欄位 (CASL) |
| zone | String? | 權限區域 (特定業務邏輯使用) |
| createdAt | DateTime | 建立時間 |
| updatedAt | DateTime | 最後更新時間 |

**tenant_invitations (租戶邀請)** - 對應 Prisma `TenantInvite`
| 欄位名稱 | 資料型態 | 說明 |
|----------------|------------|----------------------------------------------------------------------|
| id | String | 唯一識別碼 (CUID) |
| email | String | 受邀者 Email |
| tenantId | String | 目標租戶 ID，關聯到 `Tenant` 表 |
| roleId | String | 邀請時預設指派的角色 ID，關聯到 `Role` 表 |
| status | String | 邀請狀態 (例如：`pending`, `accepted`, `expired`, `revoked`) |
| token | String | 邀請驗證 Token (唯一) |
| expiresAt | DateTime | 邀請過期時間 |
| createdById | String? | 邀請建立者 `TenantUser` ID |
| acceptedById | String? | 接受邀請的 `TenantUser` ID |
| acceptedAt | DateTime? | 接受邀請的時間 |
| createdAt | DateTime | 建立時間 |
| updatedAt | DateTime | 最後更新時間 |

---

## 9. 員工離職與帳號處理 (建議新增章節)

本章節闡述員工離開公司（主動或被動）時，系統應如何處理其帳號與公司之間的關聯，以及相關資料的處置原則。

### 9.1 觸發情境

1.  **員工主動申請離職 (Employee-Initiated Departure):**

    - **操作路徑**：使用者在其個人帳號設定頁面或所屬公司的成員列表中，應能找到「離開此公司」或類似功能的按鈕。
    - **系統反應**：
      - 點擊後，系統應彈出確認對話框，明確告知使用者此操作將使其無法再存取該公司的所有資料與功能。
      - （可選）系統可設定是否需要通知該公司的 Tenant Admin 有成員申請自行離開。

2.  **租戶管理員移除成員 (Admin-Initiated Removal):**
    - **操作路徑**：Tenant Admin 在其管理後台的「成員管理」或「使用者管理」介面中，應能對特定成員執行「移除成員」或「解除綁定」操作。
    - **系統反應**：
      - 執行前，系統應彈出確認對話框，要求 Tenant Admin 確認此操作。
      - （可選）可提供選項讓 Tenant Admin 選擇是否立即通知該被移除的成員。

### 9.2 系統核心處理流程

無論何種觸發情境，一旦確認執行離職/移除操作，後端系統應執行以下核心流程：

1.  **解除租戶關聯 (Disassociate User from Tenant):**

    - **目標**：切斷使用者帳號與特定租戶的直接關聯。
    - **實作**：
      - 對於該 `TenantUser`（或根據 User 模型拆分後的實體），將其 `tenant_id` 欄位更新為 `NULL`。
      - 刪除所有與該使用者在此特定租戶相關的 `UserRoleMapping` 記錄。這將移除其在該租戶內的所有角色（如 `TENANT_USER`, `TENANT_ADMIN` 等）。

2.  **即時撤銷存取權限 (Revoke Access Privileges):**

    - **目標**：確保使用者在解除綁定後，立即失去對原租戶所有資源的存取權限。
    - **實作**：
      - 由於權限（如 CASL `AppAbility`）是基於使用者的 `tenant_id` 和關聯角色動態生成的，一旦 `tenant_id` 和角色被移除，其對該租戶的權限應自動失效。
      - 後端 API 在每次請求時都應驗證使用者當前的租戶歸屬和權限。

3.  **個人帳號的保留與後續引導 (Preserve Individual Account & Subsequent Guidance):**
    - **目標**：使用者的個人 HorizAI 帳號不應被刪除，僅解除與特定公司的綁定。
    - **實作**：
      - 使用者的核心登入憑證（Email/密碼）保持不變。
      - 使用者下次登入 HorizAI 時，系統檢測到其 `tenant_id` 為 `NULL`。
      - 系統應自動將其引導至「公司歸屬設定」頁面（如 PRD 階段二所述），允許其：
        - 搜尋並申請加入另一家現有公司。
        - 建立一家新的公司。
        - 選擇「暫時以個人身份使用」。

### 9.3 資料處理原則與建議

離職員工先前在租戶內產生的資料如何處理，是一個需要仔細規劃的策略性問題。

1.  **使用者個人資料 (User Profile Data within Tenant Context):**

    - 例如其在該租戶內的職稱、部門等特定於租戶的資訊，在解除綁定時可以被清除或標記為「前員工」。

2.  **使用者產生的業務資料 (Business Data Generated by the User):**

    - 例如：建立的專案、上傳的文件、發布的訊息、指派的任務等。
    - **建議策略**：
      - **預設保留並標註**：資料預設應保留在原租戶內，以確保業務連續性。建立者/所有者等欄位可以標註為「(已離職員工) [原使用者名稱]」。
      - **提供轉移機制**：Tenant Admin 應有工具可以將離職員工的重要資料（如專案所有權、主要負責的文件夾等）轉移給其他在職員工。此轉移操作應在員工離職處理流程中或之後提供。
      - **內容可訪問性**：確保轉移後，新的負責人有足夠權限訪問和管理這些資料。
      - **避免自動刪除**：除非有明確的租戶政策或法律要求，否則不建議自動刪除離職員工產生的業務資料。

3.  **指派給離職員工的任務/項目 (Assigned Tasks/Items):**
    - 系統應能偵測到指派給即將離職或已離職員工的未完成任務。
    - 應提供機制通知相關的專案經理或 Tenant Admin，以便及時重新指派這些任務。

### 9.4 通知機制

1.  **對離職員工的通知**：

    - 當被 Tenant Admin 移除時，應發送 Email 或系統內通知，告知其已從 [公司名稱] 移除，其 HorizAI 個人帳號仍然有效，可重新登入並選擇加入其他公司或建立新公司。
    - 若員工主動離開，系統應顯示操作成功的即時反饋。

2.  **對 Tenant Admin 的通知**：
    - 當有員工主動申請離開時（若設定此流程）。
    - 當成功移除一名成員時，給予操作成功的即時反饋。
    - 當有待處理的、與離職員工相關的事項時（如資料轉移提醒、未完成任務提醒）。

### 9.5 使用者介面 (UI/UX) 考量

1.  **員工視角**：

    - 「離開公司」按鈕應放置在易於找到但不容易誤觸的位置（例如：個人帳號設定 -> 公司資訊頁籤，或公司成員列表頁面中自己的條目旁）。
    - 清晰的二次確認彈窗，說明後果。

2.  **Tenant Admin 視角**：
    - 「移除成員」按鈕應整合在成員管理列表中。
    - 提供明確的二次確認。
    - （進階）在移除成員的流程中，可整合引導 Tenant Admin 處理該成員相關資料（如轉移專案所有權）的步驟。

### 9.6 安全性與稽核

- 所有員工離職、成員移除的操作，都必須被詳細記錄在系統稽核日誌（`SystemLog`）中。
- 日誌應包含操作發起人、被操作的使用者、操作時間、操作的租戶 ID 等關鍵資訊。

---

## 10. 頁面/功能草圖

- 「建立個人帳號」頁面（欄位：姓名、Email、密碼）。
- Email 驗證成功/失敗提示頁面。
- 「公司歸屬設定」引導頁面：
  - 選項一：「我是首次為我的公司建立 HorizAI 帳號」(引導至建立新公司表單)
  - 選項二：「我公司的同事已經在使用 HorizAI 了」(引導至搜尋現有公司介面)
  - 選項三：「暫時以個人身份使用 (稍後再設定公司資訊)」(引導至受限的個人儀表板)
- 「搜尋現有公司」介面（輸入框、篩選器、搜尋按鈕、結果列表）。
- （加入現有公司）申請已提交/等待審核提示。
- 「建立新公司」表單（欄位：公司名稱、國家/地區，選填的商業註冊號碼等）。
- （建立新公司）唯一性衝突時的提示框（含繼續註冊、聯繫客服等選項）。
- Workspace Admin 審核新成員加入申請的列表與操作介面。
- 成功開通/加入公司後的引導頁面。
- （使用者未歸屬公司時）儀表板或主介面上的「設定公司資訊」引導入口。

---

## 11. 資料表設計（重構建議）

本章節整合了現有的以及因應「使用者模型拆分」建議而調整或新增的資料表設計。所有設計參考現有的 `schema.prisma` 檔案，並針對 User 模型的變動提出修改建議。

---

### 11.1 核心實體與使用者模型

#### **SystemUser (系統使用者)** - _建議基於現有 User 模型調整/拆分_

_代表平台級別的使用者，如超級管理員、系統管理員。_
| 欄位名稱 | 資料型態 | 說明 |
|-----------------------|------------------|----------------------------------------------------------------------|
| id | String | 唯一識別碼 (CUID) |
| email | String | Email (唯一) |
| password | String | 密碼 (雜湊後儲存) |
| name | String? | 使用者名稱 |
| role | `SystemUserRole` | 系統使用者角色 (Enum: `SUPER_ADMIN`, `SYSTEM_ADMIN` 等，需基於現有 `UserRole` 調整或新建) |
| status | String | 狀態 (例如：`active`, `suspended`) |
| avatar | String? | 頭像 URL |
| phone | String? | 電話號碼 |
| mfaEnabled | Boolean | 是否啟用多因素認證 |
| mfaSecret | String? | MFA 密鑰 |
| lastLoginAt | DateTime? | 最後登入時間 |
| lastLoginIp | String? | 最後登入 IP |
| passwordLastChangedAt | DateTime? | 密碼最後變更時間 |
| createdAt | DateTime | 建立時間 |
| updatedAt | DateTime | 最後更新時間 |

#### **TenantUser (租戶使用者)** - _建議基於現有 User 模型調整/拆分_

_代表屬於特定租戶的使用者，如租戶管理員、一般租戶成員。_
| 欄位名稱 | 資料型態 | 說明 |
|-----------------------|------------------|----------------------------------------------------------------------|
| id | String | 唯一識別碼 (CUID) |
| email | String | Email (在租戶內唯一，或全域唯一，需根據設計決定) |
| password | String | 密碼 (雜湊後儲存) |
| name | String? | 使用者名稱 |
| **tenantId** | **String** | **所屬租戶 ID (必填)**，關聯到 `Tenant` 表 |
| role | `TenantUserRole` | 租戶使用者角色 (Enum: `TENANT_ADMIN`, `TENANT_USER` 等，需基於現有 `UserRole` 調整或新建) |
| status | String | 狀態 (例如：`active`, `invited`, `suspended`) |
| avatar | String? | 頭像 URL |
| phone | String? | 電話號碼 |
| title | String? | 職稱 |
| company | String? | 公司名稱 (使用者自行填寫，可能與 `Tenant.name` 不同) |
| department | String? | 部門 (使用者自行填寫) |
| location | String? | 地點 |
| bio | String? | 個人簡介 |
| socialLinks | Json? | 社群連結 |
| preferences | Json? | 個人偏好設定 |
| lineUserId | String? | LINE 使用者 ID (唯一) |
| lineConnected | Boolean | 是否已連結 LINE |
| lastLogoutAt | DateTime? | 上次登出時間 |
| lastLoginAt | DateTime? | 最後登入時間 |
| lastLoginIp | String? | 最後登入 IP |
| googleId | String? | Google ID (唯一) |
| passwordLastChangedAt | DateTime? | 密碼最後變更時間 |
| mfaEnabled | Boolean | 是否啟用多因素認證 |
| mfaSecret | String? | MFA 密鑰 |
| createdAt | DateTime | 建立時間 |
| updatedAt | DateTime | 最後更新時間 |
| _invited_by_ | String? | _邀請者 `TenantUser` ID (若由 `TenantInvite` 處理，此欄位可選)_ |

**註記：**

- `SystemUserRole` 和 `TenantUserRole` 是為清晰劃分角色範圍而建議的 Enum 調整方向。實際 Prisma 模型中可能仍為單一 `UserRole` Enum，透過 `Role.scope` 和業務邏輯來區分。
- **前端識別使用者類型說明**：後端 API (`/api/auth/me`) 在回應使用者資訊時，應包含 `userType` 欄位 (值為 `'system'` 或 `'tenant'`)。前端 `auth.store.ts` 儲存此 `userType`，並可定義可辨識的聯合類型 `CurrentUser` (如 `SystemUser | TenantUser | null`)，以供前端邏輯判斷。

---

### 11.2 租戶與工作空間

#### **tenants (公司租戶)** - 對應 Prisma `Tenant`

| 欄位名稱                | 資料型態  | 說明                                              |
| ----------------------- | --------- | ------------------------------------------------- |
| id                      | String    | 唯一識別碼 (CUID)                                 |
| name                    | String    | 公司名稱                                          |
| domain                  | String?   | 公司 Email 網域 (唯一)                            |
| company_registration_id | String?   | 公司統一編號 (唯一，對於需要驗證的地區)           |
| status                  | String    | 租戶狀態 (例如：`pending`, `active`, `suspended`) |
| planId                  | String?   | 訂閱方案 ID，關聯到 `Plan` 表                     |
| adminEmail              | String?   | 管理員 Email (通常為第一個 `TenantUser` 的 Email) |
| adminName               | String?   | 管理員名稱                                        |
| companySize             | String?   | 公司規模                                          |
| industry                | String?   | 所屬產業                                          |
| departments             | String[]  | 部門列表                                          |
| maxUsers                | Int?      | 此租戶方案允許的最大使用者數量 (指 `TenantUser`)  |
| maxProjects             | Int?      | 此租戶方案允許的最大專案數量                      |
| maxStorage              | Int?      | 此租戶方案允許的最大儲存空間 (GB)                 |
| currentAiCredits        | Decimal?  | 目前 AI 點數餘額                                  |
| nextBillingDate         | DateTime? | 下次計費日期                                      |
| paymentStatus           | String?   | 付款狀態                                          |
| billingCycle            | String?   | 計費週期                                          |
| contactEmail            | String?   | 聯絡人 Email                                      |
| contactName             | String?   | 聯絡人名稱                                        |
| createdAt               | DateTime  | 建立時間                                          |
| updatedAt               | DateTime  | 最後更新時間                                      |

#### **workspaces (工作空間)** - 對應 Prisma `Workspace`

| 欄位名稱    | 資料型態 | 說明                                                          |
| ----------- | -------- | ------------------------------------------------------------- |
| id          | String   | 唯一識別碼 (CUID)                                             |
| name        | String   | 工作空間名稱                                                  |
| description | String?  | 工作空間描述                                                  |
| tenantId    | String   | 所屬租戶 ID，關聯到 `Tenant` 表                               |
| ownerId     | String   | 擁有者 `TenantUser` ID，關聯到 `TenantUser` 表 (_需對應調整_) |
| status      | String   | 狀態 (例如：`active`, `archived`)                             |
| settings    | Json?    | 工作空間特定設定                                              |
| createdAt   | DateTime | 建立時間                                                      |
| updatedAt   | DateTime | 最後更新時間                                                  |

---

### 11.3 角色、權限與邀請

#### **roles (角色)** - 對應 Prisma `Role`

| 欄位名稱    | 資料型態    | 說明                                             |
| ----------- | ----------- | ------------------------------------------------ | --- | -------- | ------- | ----------------------------------------------------------- | --- | -------- | ------- | ---------------------------------------------------------- | --- | ------------ | ------- | ---------------------------- | --- | --------- | -------- | -------- | --- | --------- | -------- | ------------ |
| id          | String      | 唯一識別碼 (CUID)                                |
| name        | String      | 角色內部名稱 (唯一)                              |
| displayName | String      | 角色顯示名稱                                     |
| description | String?     | 角色描述                                         |
| scope       | `RoleScope` | 角色範圍 (Enum: `SYSTEM`, `TENANT`, `WORKSPACE`) | \n  | tenantId | String? | 若為租戶特定角色 (`scope=TENANT` 或 `WORKSPACE`)，其租戶 ID | \n  | isSystem | Boolean | 是否為系統預設角色 (此欄位可能與 `scope` 部分重疊，需釐清) | \n  | parentRoleId | String? | 父角色 ID (用於角色層級結構) | \n  | createdAt | DateTime | 建立時間 | \n  | updatedAt | DateTime | 最後更新時間 |

#### **user_roles (使用者角色對應)** - 對應 Prisma `UserRoleMapping`

_此表需調整以反映 `SystemUser` 和 `TenantUser` 的區分。_
| 欄位名稱 | 資料型態 | 說明 |
|------------|-----------|----------------------------------------------------------------------|
| id | String | 唯一識別碼 (CUID) |
| **userId** | **String**| **使用者 ID (此 ID 需能區分來源是 `SystemUser` 或 `TenantUser`)** |
| **userType**| **String?**| **_建議新增欄位_**: 使用者類別 (例如: 'system', 'tenant')，輔助區分 `userId` 的來源表。 |
| roleId | String | 角色 ID，關聯到 `Role` 表 |
| createdAt | DateTime | 建立時間 |
| updatedAt | DateTime | 最後更新時間 |

#### **permission_categories (權限分類)** - 對應 Prisma `PermissionCategory`

| 欄位名稱    | 資料型態 | 說明              |
| ----------- | -------- | ----------------- |
| id          | String   | 唯一識別碼 (CUID) |
| name        | String   | 分類名稱 (唯一)   |
| description | String?  | 分類描述          |
| icon        | String?  | 圖示              |
| sortOrder   | Int      | 排序順序          |
| isActive    | Boolean  | 是否啟用          |
| createdAt   | DateTime | 建立時間          |
| updatedAt   | DateTime | 最後更新時間      |

#### **permissions (權限)** - 對應 Prisma `Permission`

_`subject` 欄位需能反映 `SystemUser` 和 `TenantUser` 的區分。_
| 欄位名稱 | 資料型態 | 說明 |
|-----------------|-------------------|----------------------------------------------------------------------|
| id | String | 唯一識別碼 (CUID) |
| name | String? | 權限的易讀名稱 |
| action | String | 操作 (例如：`create`, `read`, `update`, `delete`) |
| **subject** | **String** | **主體 (例如：`SystemUser`, `TenantUser`, `Project` 等，需反映 User 拆分)** |
| conditions | Json? | 權限條件 (CASL) |
| description | String? | 權限描述 |
| categoryId | String? | 權限分類 ID，關聯到 `PermissionCategory` 表 |\n| scope | `PermissionScope`?| 權限範圍 (Enum: `SYSTEM`, `TENANT`, `WORKSPACE`, `GLOBAL`) |\n| isSystemDefined | Boolean | 是否為系統定義 |\n| deprecated | Boolean | 是否已棄用 |\n| fields | String[] | 權限相關欄位 (CASL) |\n| zone | String? | 權限區域 (特定業務邏輯使用) |\n| createdAt | DateTime | 建立時間 |\n| updatedAt | DateTime | 最後更新時間 |

#### **tenant_invitations (租戶邀請)** - 對應 Prisma `TenantInvite`

_`createdById` 和 `acceptedById` 需對應到 `TenantUser`。_
| 欄位名稱 | 資料型態 | 說明 |
|----------------|-----------|----------------------------------------------------------------------|
| id | String | 唯一識別碼 (CUID) |
| email | String | 受邀者 Email |
| tenantId | String | 目標租戶 ID，關聯到 `Tenant` 表 |
| roleId | String | 邀請時預設指派的角色 ID，關聯到 `Role` 表 |
| status | String | 邀請狀態 (例如：`pending`, `accepted`, `expired`, `revoked`) |
| token | String | 邀請驗證 Token (唯一) |
| expiresAt | DateTime | 邀請過期時間 |
| createdById | String? | 邀請建立者 `TenantUser` ID (_需對應調整_) |
| acceptedById | String? | 接受邀請的 `TenantUser` ID (_需對應調整_) |
| acceptedAt | DateTime? | 接受邀請的時間 |
| createdAt | DateTime | 建立時間 |
| updatedAt | DateTime | 最後更新時間 |

---
