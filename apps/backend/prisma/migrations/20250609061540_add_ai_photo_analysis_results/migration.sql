-- This is an empty migration.

-- 建立照片分析結果表
CREATE TABLE "ai_photo_analysis_results" (
    "id" TEXT NOT NULL,
    "photo_url" TEXT NOT NULL,
    "project_id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "analysis_type" TEXT NOT NULL,
    "context" TEXT,
    "confidence" DECIMAL NOT NULL DEFAULT 0.8,
    "description" TEXT NOT NULL,
    "progress_percentage" INTEGER,
    "quality_score" INTEGER,
    "safety_issues" TEXT[],
    "equipment_detected" TEXT[],
    "recommendations" TEXT[],
    "ai_model_used" TEXT,
    "processing_time_ms" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ai_photo_analysis_results_pkey" PRIMARY KEY ("id")
);

-- 建立索引來優化查詢性能
CREATE INDEX "ai_photo_analysis_results_project_id_idx" ON "ai_photo_analysis_results"("project_id");
CREATE INDEX "ai_photo_analysis_results_tenant_id_idx" ON "ai_photo_analysis_results"("tenant_id");
CREATE INDEX "ai_photo_analysis_results_user_id_idx" ON "ai_photo_analysis_results"("user_id");
CREATE INDEX "ai_photo_analysis_results_analysis_type_idx" ON "ai_photo_analysis_results"("analysis_type");
CREATE INDEX "ai_photo_analysis_results_created_at_idx" ON "ai_photo_analysis_results"("created_at");

-- 添加外鍵約束
ALTER TABLE "ai_photo_analysis_results" ADD CONSTRAINT "ai_photo_analysis_results_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ai_photo_analysis_results" ADD CONSTRAINT "ai_photo_analysis_results_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;