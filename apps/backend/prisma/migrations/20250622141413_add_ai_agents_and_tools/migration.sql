/*
  Warnings:

  - You are about to drop the column `bot_id` on the `ai_feature_configs` table. All the data in the column will be lost.
  - You are about to drop the column `bot_id` on the `ai_usage_logs` table. All the data in the column will be lost.
  - You are about to drop the `ai_bots` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `agent_id` to the `ai_usage_logs` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "AiAgentProviderType" AS ENUM ('OPENAI', 'AZURE_OPENAI', 'ANTHROPIC', 'GOOGLE_GEMINI', 'CLAUDE', 'GEMINI', 'O<PERSON><PERSON><PERSON><PERSON>_COMPATIBLE', 'CUSTOM');

-- CreateEnum
CREATE TYPE "AiAgentResponseFormat" AS ENUM ('TEXT', 'JSON');

-- CreateEnum
CREATE TYPE "AiAgentScope" AS ENUM ('SYSTEM', 'TENANT', 'WORKSPACE');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "AiAgentExecutionType" AS ENUM ('SINGLE_CALL', 'AGENT', 'WORKFLOW');

-- CreateEnum
CREATE TYPE "AiToolScope" AS ENUM ('SYSTEM', 'TENANT', 'WORKSPACE');

-- DropForeignKey
ALTER TABLE "ai_bots" DROP CONSTRAINT "ai_bots_key_id_fkey";

-- DropForeignKey
ALTER TABLE "ai_bots" DROP CONSTRAINT "ai_bots_model_id_fkey";

-- DropForeignKey
ALTER TABLE "ai_bots" DROP CONSTRAINT "ai_bots_tenant_id_fkey";

-- DropForeignKey
ALTER TABLE "ai_bots" DROP CONSTRAINT "ai_bots_workspace_id_fkey";

-- DropForeignKey
ALTER TABLE "ai_feature_configs" DROP CONSTRAINT "ai_feature_configs_bot_id_fkey";

-- DropForeignKey
ALTER TABLE "ai_usage_logs" DROP CONSTRAINT "ai_usage_logs_bot_id_fkey";

-- DropIndex
DROP INDEX "ai_usage_logs_bot_id_idx";

-- AlterTable
ALTER TABLE "ai_feature_configs" DROP COLUMN "bot_id",
ADD COLUMN     "agent_id" TEXT;

-- AlterTable
ALTER TABLE "ai_usage_logs" DROP COLUMN "bot_id",
ADD COLUMN     "agent_id" TEXT NOT NULL;

-- DropTable
DROP TABLE "ai_bots";

-- DropEnum
DROP TYPE "AiBotProviderType";

-- DropEnum
DROP TYPE "AiBotResponseFormat";

-- DropEnum
DROP TYPE "AiBotScope";

-- CreateTable
CREATE TABLE "ai_agents" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "scene" TEXT,
    "temperature" DOUBLE PRECISION DEFAULT 0.7,
    "scope" "AiAgentScope" NOT NULL,
    "provider_type" "AiAgentProviderType" NOT NULL,
    "model_id" TEXT NOT NULL,
    "key_id" TEXT NOT NULL,
    "provider_config_override" JSONB,
    "system_prompt" TEXT,
    "max_tokens" INTEGER,
    "response_format" "AiAgentResponseFormat" NOT NULL DEFAULT 'TEXT',
    "execution_type" "AiAgentExecutionType" NOT NULL DEFAULT 'SINGLE_CALL',
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "is_template" BOOLEAN NOT NULL DEFAULT false,
    "tenant_id" TEXT NOT NULL,
    "workspace_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "created_by" TEXT NOT NULL,
    "updated_by" TEXT,

    CONSTRAINT "ai_agents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ai_tools" (
    "id" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "input_schema" JSONB,
    "scope" "AiToolScope" NOT NULL,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "tenant_id" TEXT,
    "workspace_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "created_by" TEXT,
    "updated_by" TEXT,

    CONSTRAINT "ai_tools_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ai_agent_tools" (
    "id" TEXT NOT NULL,
    "ai_agent_id" TEXT NOT NULL,
    "ai_tool_id" TEXT NOT NULL,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "config" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ai_agent_tools_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ai_agents_scope_tenant_id_workspace_id_idx" ON "ai_agents"("scope", "tenant_id", "workspace_id");

-- CreateIndex
CREATE UNIQUE INDEX "ai_tools_key_key" ON "ai_tools"("key");

-- CreateIndex
CREATE INDEX "ai_tools_scope_tenant_id_workspace_id_idx" ON "ai_tools"("scope", "tenant_id", "workspace_id");

-- CreateIndex
CREATE INDEX "ai_tools_is_enabled_idx" ON "ai_tools"("is_enabled");

-- CreateIndex
CREATE INDEX "ai_tools_key_idx" ON "ai_tools"("key");

-- CreateIndex
CREATE INDEX "ai_agent_tools_ai_agent_id_idx" ON "ai_agent_tools"("ai_agent_id");

-- CreateIndex
CREATE INDEX "ai_agent_tools_ai_tool_id_idx" ON "ai_agent_tools"("ai_tool_id");

-- CreateIndex
CREATE INDEX "ai_agent_tools_is_enabled_idx" ON "ai_agent_tools"("is_enabled");

-- CreateIndex
CREATE UNIQUE INDEX "ai_agent_tools_ai_agent_id_ai_tool_id_key" ON "ai_agent_tools"("ai_agent_id", "ai_tool_id");

-- CreateIndex
CREATE INDEX "ai_usage_logs_agent_id_idx" ON "ai_usage_logs"("agent_id");

-- AddForeignKey
ALTER TABLE "ai_agents" ADD CONSTRAINT "ai_agents_key_id_fkey" FOREIGN KEY ("key_id") REFERENCES "ai_keys"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_agents" ADD CONSTRAINT "ai_agents_model_id_fkey" FOREIGN KEY ("model_id") REFERENCES "ai_models"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_agents" ADD CONSTRAINT "ai_agents_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_agents" ADD CONSTRAINT "ai_agents_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_feature_configs" ADD CONSTRAINT "ai_feature_configs_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "ai_agents"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_usage_logs" ADD CONSTRAINT "ai_usage_logs_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "ai_agents"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_tools" ADD CONSTRAINT "ai_tools_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_tools" ADD CONSTRAINT "ai_tools_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_agent_tools" ADD CONSTRAINT "ai_agent_tools_ai_agent_id_fkey" FOREIGN KEY ("ai_agent_id") REFERENCES "ai_agents"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_agent_tools" ADD CONSTRAINT "ai_agent_tools_ai_tool_id_fkey" FOREIGN KEY ("ai_tool_id") REFERENCES "ai_tools"("id") ON DELETE CASCADE ON UPDATE CASCADE;
