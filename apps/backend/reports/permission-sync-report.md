# 權限同步報告

- **產生時間**: 2025-06-21T09:30:45.591Z
- **版本**: unknown
- **模式**: sync

## 統計摘要
- 總權限數: 73
- 按範圍:
  - GLOBAL: 21
  - SYSTEM: 22
  - TENANT: 10
  - WORKSPACE: 20
- 按分類:
  - other: 2
  - user_management: 11
  - system_management: 9
  - tenant_management: 6
  - member_management: 4
  - log_management: 2
  - ai_management: 17
  - subscription_management: 4
  - collaboration: 14
  - role_management: 2
  - ai_features: 1
  - workspace_settings: 1

## 權限列表
- `Workspace` `access` [WORKSPACE/workspace_settings]
- `Permission` `create` [SYSTEM/user_management]
- `Permission` `delete` [SYSTEM/user_management]
- `Permission` `manage` [SYSTEM/user_management]
- `Permission` `read` [SYSTEM/user_management]
- `Permission` `update` [SYSTEM/user_management]
- `Role` `create` [SYSTEM/user_management]
- `Role` `delete` [SYSTEM/user_management]
- `Role` `read` [SYSTEM/user_management]
- `Role` `update` [SYSTEM/user_management]
- `SystemUser` `read` [SYSTEM/user_management]
- `TenantUser` `manage` [TENANT/user_management]
- `Tenant` `manage` [SYSTEM/tenant_management]
- `Tenant` `read` [SYSTEM/tenant_management]
- `Workspace` `create` [TENANT/tenant_management]
- `Workspace` `delete` [TENANT/tenant_management]
- `Workspace` `read` [TENANT/tenant_management]
- `Workspace` `update` [TENANT/tenant_management]
- `AdminPanel` `access` [SYSTEM/system_management]
- `Dashboard` `read` [SYSTEM/system_management]
- `DashboardActiveUsers` `read` [GLOBAL/system_management]
- `DashboardActivity` `read` [GLOBAL/system_management]
- `DashboardRecentOrders` `read` [GLOBAL/system_management]
- `DashboardRecentTenants` `read` [TENANT/system_management]
- `DashboardRevenue` `read` [GLOBAL/system_management]
- `DashboardStats` `read` [GLOBAL/system_management]
- `SystemUser` `delete` [SYSTEM/system_management]
- `Plan` `create` [GLOBAL/subscription_management]
- `Plan` `delete` [GLOBAL/subscription_management]
- `Plan` `read` [GLOBAL/subscription_management]
- `Plan` `update` [GLOBAL/subscription_management]
- `Role` `Action.MANAGE` [GLOBAL/role_management]
- `Role` `Action.READ` [GLOBAL/role_management]
- `all` `Actions.MANAGE` [GLOBAL/other]
- `subjectType` `action` [GLOBAL/other]
- `User` `create` [WORKSPACE/member_management]
- `User` `delete` [WORKSPACE/member_management]
- `User` `read` [WORKSPACE/member_management]
- `User` `update` [WORKSPACE/member_management]
- `LineMessageLog` `read` [SYSTEM/log_management]
- `SystemLog` `read` [SYSTEM/log_management]
- `Comment` `create` [WORKSPACE/collaboration]
- `Comment` `delete` [WORKSPACE/collaboration]
- `Comment` `read` [WORKSPACE/collaboration]
- `Comment` `update` [WORKSPACE/collaboration]
- `CommentReaction` `create` [WORKSPACE/collaboration]
- `CommentReaction` `delete` [WORKSPACE/collaboration]
- `FilePermission` `create` [WORKSPACE/collaboration]
- `FileShare` `create` [WORKSPACE/collaboration]
- `FileShare` `delete` [WORKSPACE/collaboration]
- `FileShare` `read` [WORKSPACE/collaboration]
- `SharedFile` `create` [WORKSPACE/collaboration]
- `SharedFile` `delete` [WORKSPACE/collaboration]
- `SharedFile` `read` [WORKSPACE/collaboration]
- `SharedFile` `update` [WORKSPACE/collaboration]
- `ai_bots` `create` [GLOBAL/ai_management]
- `ai_bots` `delete` [GLOBAL/ai_management]
- `ai_bots` `read` [GLOBAL/ai_management]
- `ai_bots` `update` [GLOBAL/ai_management]
- `ai_models` `create` [SYSTEM/ai_management]
- `ai_models` `delete` [SYSTEM/ai_management]
- `ai_models` `manage` [SYSTEM/ai_management]
- `ai_models` `read` [SYSTEM/ai_management]
- `ai_models` `update` [SYSTEM/ai_management]
- `ai_tools` `create` [GLOBAL/ai_management]
- `ai_tools` `delete` [GLOBAL/ai_management]
- `ai_tools` `read` [GLOBAL/ai_management]
- `ai_tools` `update` [GLOBAL/ai_management]
- `LineBot` `create` [TENANT/ai_management]
- `LineBot` `delete` [TENANT/ai_management]
- `LineBot` `read` [TENANT/ai_management]
- `LineBot` `update` [TENANT/ai_management]
- `ai_bots` `execute` [WORKSPACE/ai_features]