import { Test, TestingModule } from '@nestjs/testing';
import { CaslAbilityFactory } from './casl-ability.factory';
import { PrismaService } from '../../modules/core/prisma/prisma.service';
import { Actions, Subjects } from '@horizai/permissions';
import { UserType, SUPER_ADMIN_ROLE_NAME } from './types';
import { createMongoAbility } from '@casl/ability';
import * as conditionUtils from '../utils/condition.utils';

// Mock data
const mockSystemUser = { id: 'system-user-id', role: 'SYSTEM_ADMIN' };
const mockUserWithNoRole = { id: 'no-role-user-id', role: 'some-basic-role' };
const mockTenantUser = { id: 'tenant-user-id', tenant_id: 'tenant-1' };
const mockSuperAdminUser = { id: 'super-admin-id', role: SUPER_ADMIN_ROLE_NAME };

const mockSystemAdminRole = {
  id: 'system-admin-role-id',
  name: 'SYSTEM_ADMIN',
};
const mockSystemUserRoleMapping = {
  system_user_id: mockSystemUser.id,
  role: mockSystemAdminRole,
};

const mockReadSystemUserPermission = {
  action: Actions.READ,
  subject: Subjects.SYSTEM_USER,
  conditions: null,
  fields: null,
};

describe('CaslAbilityFactory', () => {
  let factory: CaslAbilityFactory;
  let prisma: PrismaService;

  const mockPrismaService = {
    system_users: {
      findUnique: jest.fn(),
    },
    tenant_users: {
      findUnique: jest.fn(),
    },
    system_user_roles: {
      findMany: jest.fn(),
    },
    tenant_user_roles: {
      findMany: jest.fn(),
    },
    permissions: {
      findMany: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CaslAbilityFactory, { provide: PrismaService, useValue: mockPrismaService }],
    }).compile();

    factory = module.get<CaslAbilityFactory>(CaslAbilityFactory);
    prisma = module.get<PrismaService>(PrismaService);

    // Reset mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(factory).toBeDefined();
  });

  describe('Super Admin', () => {
    it('should grant manage all ability to a super admin', async () => {
      mockPrismaService.system_users.findUnique.mockResolvedValue(mockSuperAdminUser);
      mockPrismaService.tenant_users.findUnique.mockResolvedValue(null);
      mockPrismaService.system_user_roles.findMany.mockResolvedValue([
        { role: { name: SUPER_ADMIN_ROLE_NAME } },
      ]);
      mockPrismaService.permissions.findMany.mockResolvedValue([]);

      const ability = await factory.createForUser({
        user_id: mockSuperAdminUser.id,
        user_type: UserType.SYSTEM,
      });

      expect(ability.can(Actions.MANAGE, 'all')).toBe(true);
    });
  });

  describe('System User', () => {
    it('should grant specific permissions to a system admin', async () => {
      mockPrismaService.system_users.findUnique.mockResolvedValue(mockSystemUser);
      mockPrismaService.tenant_users.findUnique.mockResolvedValue(null);
      mockPrismaService.system_user_roles.findMany.mockResolvedValue([mockSystemUserRoleMapping]);
      mockPrismaService.permissions.findMany.mockResolvedValue([mockReadSystemUserPermission]);

      const ability = await factory.createForUser({
        user_id: mockSystemUser.id,
        user_type: UserType.SYSTEM,
      });

      expect(ability.can(Actions.READ, Subjects.SYSTEM_USER)).toBe(true);
      expect(ability.can(Actions.DELETE, Subjects.SYSTEM_USER)).toBe(false);
      expect(ability.can(Actions.READ, Subjects.TENANT)).toBe(false);
    });
  });

  describe('Tenant User', () => {
    it('should grant permissions with conditions for a tenant user', async () => {
      // Spy on the utility function
      const processConditionsSpy = jest.spyOn(conditionUtils, 'processConditions');
      processConditionsSpy.mockImplementation((conditions, context) => {
        if (conditions && typeof conditions === 'object' && !Array.isArray(conditions)) {
          if (conditions['tenant_id'] === '${tenant_id}') {
            return { tenant_id: context.tenant_id };
          }
          return conditions;
        }
        return undefined;
      });

      mockPrismaService.system_users.findUnique.mockResolvedValue(null);
      mockPrismaService.tenant_users.findUnique.mockResolvedValue(mockTenantUser);
      mockPrismaService.tenant_user_roles.findMany.mockResolvedValue([
        { role: { name: 'TENANT_ADMIN' } },
      ]);
      mockPrismaService.permissions.findMany.mockResolvedValue([
        {
          action: Actions.MANAGE,
          subject: Subjects.TENANT_USER,
          conditions: { tenant_id: '${tenant_id}' }, // Template variable
          fields: null,
        },
      ]);

      const ability = await factory.createForUser({
        user_id: mockTenantUser.id,
        tenant_id: mockTenantUser.tenant_id,
        user_type: UserType.TENANT,
      });

      const rules = ability.rules;
      const tenantRule = rules.find((rule) => rule.subject === Subjects.TENANT_USER);

      expect(tenantRule).toBeDefined();
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      expect(tenantRule!.conditions).toEqual({ tenant_id: mockTenantUser.tenant_id });
      expect(ability.can(Actions.MANAGE, Subjects.TENANT_USER)).toBe(true); // Basic check

      // Restore the original function
      processConditionsSpy.mockRestore();
    });
  });

  describe('Edge Cases', () => {
    it('should return an empty ability for a non-existent user', async () => {
      mockPrismaService.system_users.findUnique.mockResolvedValue(null);
      mockPrismaService.tenant_users.findUnique.mockResolvedValue(null);

      const ability = await factory.createForUser({
        user_id: 'non-existent-user',
        user_type: UserType.SYSTEM, // Assume a type for the call
      });

      expect(ability.rules.length).toBe(0);
    });

    it('should return an empty ability for a user with no roles', async () => {
      // This user exists in the system_users table...
      mockPrismaService.system_users.findUnique.mockResolvedValue(mockUserWithNoRole);
      mockPrismaService.tenant_users.findUnique.mockResolvedValue(null);
      // ...but has no entry in system_user_roles
      mockPrismaService.system_user_roles.findMany.mockResolvedValue([]);

      // AND the fallback check on the user object itself also finds no roles (or a role that has no permissions)
      (prisma.system_users.findUnique as jest.Mock).mockResolvedValueOnce({
        id: mockUserWithNoRole.id,
        role: null, // Explicitly return null in the fallback to ensure no role is found
      });
      // AND this non-existent role has no permissions attached
      mockPrismaService.permissions.findMany.mockResolvedValue([]);

      const ability = await factory.createForUser({
        user_id: mockUserWithNoRole.id,
        user_type: UserType.SYSTEM,
      });

      expect(ability.rules.length).toBe(0);
    });

    it('should return an empty ability for a user whose roles have no permissions', async () => {
      mockPrismaService.system_users.findUnique.mockResolvedValue(mockSystemUser);
      mockPrismaService.tenant_users.findUnique.mockResolvedValue(null);
      mockPrismaService.system_user_roles.findMany.mockResolvedValue([mockSystemUserRoleMapping]);
      mockPrismaService.permissions.findMany.mockResolvedValue([]); // Role exists, but has no permissions

      const ability = await factory.createForUser({
        user_id: mockSystemUser.id,
        user_type: UserType.SYSTEM,
      });

      expect(ability.rules.length).toBe(0);
    });
  });
});
