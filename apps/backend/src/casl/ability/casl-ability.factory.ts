import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../modules/core/prisma/prisma.service';
import { AbilityBuilder, createMongoAbility, Subject } from '@casl/ability';
import { Actions } from '@horizai/permissions';
import { AppAbility } from '../../types/models/casl.model';
import { processConditions as processConditionsUtil, UserContext } from '../utils/condition.utils';
import {
  DbPermission,
  SystemUserRoleWithRole,
  TenantUserRoleWithRole,
  UserType,
  CreateUserAbilityParams,
  SUPER_ADMIN_ROLE_NAME,
} from './types';

@Injectable()
export class CaslAbilityFactory {
  private readonly logger = new Logger(CaslAbilityFactory.name);

  constructor(private prisma: PrismaService) {}

  /**
   * 根據用戶 ID 和類型確定用戶身份
   */
  private async determineUserType(
    userId: string,
  ): Promise<{ type: UserType; tenant_id?: string } | null> {
    try {
      // 首先嘗試查找系統用戶
      const systemUser = await this.prisma.system_users.findUnique({
        where: { id: userId },
      });

      if (systemUser) {
        return { type: UserType.SYSTEM };
      }

      // 然後嘗試查找租戶用戶
      const tenantUser = await this.prisma.tenant_users.findUnique({
        where: { id: userId },
        select: { id: true, tenant_id: true },
      });

      if (tenantUser) {
        return { type: UserType.TENANT, tenant_id: tenantUser.tenant_id };
      }

      return null;
    } catch (error: any) {
      this.logger.error(
        `Error determining user type for user ID ${userId}: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * 為使用者建立 CASL Ability 物件
   * JWT payload should provide userId. Roles and tenant_id (if primary) are fetched or contextually determined.
   */
  async createForUser(userPayloadFromJwt: CreateUserAbilityParams): Promise<AppAbility> {
    // 使用 createMongoAbility 工廠並以 constructor.name 作為 detectSubjectType
    const subjectTypeDetector = (subject: any) =>
      (subject && ((subject as any).__typename || subject.constructor.name)) || undefined;
    const builder = new AbilityBuilder<AppAbility>(createMongoAbility);
    const { can, build } = builder;

    if (!userPayloadFromJwt || !userPayloadFromJwt.user_id) {
      this.logger.warn(`User payload or ID is missing. Building an empty ability.`);
      return build({ detectSubjectType: subjectTypeDetector });
    }

    const userId = userPayloadFromJwt.user_id;

    try {
      // 確定用戶類型和租戶 ID
      const userIdentity = await this.determineUserType(userId);

      if (!userIdentity) {
        this.logger.warn(`User with ID ${userId} not found in system_users or tenant_users`);
        return build({ detectSubjectType: subjectTypeDetector });
      }

      // User context for variable replacement in conditions
      const userContextForConditions: UserContext = {
        id: userId,
        userId: userId,
        tenant_id: userIdentity.tenant_id || userPayloadFromJwt.tenant_id,
      };

      // 根據用戶類型獲取角色
      let userRoleNames: string[] = [];

      if (userIdentity.type === UserType.SYSTEM) {
        // 首先嘗試從角色關聯表獲取角色
        const systemUserRoles: SystemUserRoleWithRole[] =
          await this.prisma.system_user_roles.findMany({
            where: { system_user_id: userId },
            include: {
              role: true,
            },
          });

        userRoleNames = systemUserRoles.map((mapping) => mapping.role.name);

        // 如果沒有在關聯表中找到角色，檢查用戶表的 role 字段
        if (userRoleNames.length === 0) {
          const systemUser = await this.prisma.system_users.findUnique({
            where: { id: userId },
            select: { role: true },
          });

          if (systemUser && systemUser.role) {
            userRoleNames = [systemUser.role];
            this.logger.debug(`Using role from system_users table: ${systemUser.role}`);
          }
        }

        this.logger.debug(
          `Found ${
            userRoleNames.length
          } system user roles for user ${userId}: ${userRoleNames.join(', ')}`,
        );
      } else if (userIdentity.type === UserType.TENANT) {
        const tenantUserRoles: TenantUserRoleWithRole[] =
          await this.prisma.tenant_user_roles.findMany({
            where: { tenant_user_id: userId },
            include: {
              role: true,
            },
          });

        userRoleNames = tenantUserRoles.map((mapping) => mapping.role.name);

        this.logger.debug(
          `Found ${
            tenantUserRoles.length
          } tenant user roles for user ${userId}: ${userRoleNames.join(', ')}`,
        );
      }

      if (userRoleNames.length === 0) {
        this.logger.warn(
          `No roles found for user ID: ${userId} (type: ${
            userIdentity.type
          }). User might have no permissions.`,
        );
      }

      // 獲取這些角色對應的所有權限
      const permissions: DbPermission[] = await this.prisma.permissions.findMany({
        where: {
          role_permissions: {
            some: {
              roles: {
                name: {
                  in: userRoleNames,
                },
              },
            },
          },
        },
        select: {
          action: true,
          subject: true,
          conditions: true,
          fields: true,
          created_at: true,
          updated_at: true,
          deprecated: true,
          description: true,
          id: true,
        },
      });

      if (permissions.length === 0 && userRoleNames.length > 0) {
        // 只對非SUPER_ADMIN角色記錄警告，SUPER_ADMIN有預設的全權限
        if (!userRoleNames.includes(SUPER_ADMIN_ROLE_NAME)) {
          this.logger.warn(
            `No specific permissions found in DB for roles: ${userRoleNames.join(
              ', ',
            )}. User ID: ${userId}`,
          );
        }
      } else {
        this.logger.debug(
          `Found ${
            permissions.length
          } permissions for user ${userId} with roles: ${userRoleNames.join(', ')}`,
        );
      }

      for (const perm of permissions) {
        const action = perm.action as any;
        const subject = perm.subject as Subject;
        const processedCond = processConditionsUtil(perm.conditions, userContextForConditions);

        if (perm.fields && perm.fields.length > 0) {
          can(action, subject, perm.fields, processedCond);
        } else {
          can(action, subject, processedCond);
        }
      }

      // 如果用戶是超級管理員，則賦予所有權限
      if (userRoleNames.includes(SUPER_ADMIN_ROLE_NAME)) {
        this.logger.debug(`User ${userId} has SUPER_ADMIN role. Granting manage:all permission.`);
        can(Actions.MANAGE, 'all');
      }

      return build({ detectSubjectType: subjectTypeDetector });
    } catch (error: any) {
      this.logger.error(
        `Error creating ability for user ID ${userId}: ${error.message}`,
        error.stack,
      );
      // Return a restrictive ability in case of errors
      return build({ detectSubjectType: subjectTypeDetector });
    }
  }
}
