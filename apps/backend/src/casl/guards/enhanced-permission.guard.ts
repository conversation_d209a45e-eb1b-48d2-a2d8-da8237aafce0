import { Injectable, ExecutionContext, Logger, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { CaslAbilityFactory } from '../ability/casl-ability.factory';
import { AppAbility } from '../../types/models/casl.model';
import {
  CHECK_POLICIES_KEY,
  REQUIRE_PERMISSIONS_KEY,
  PermissionRequirement,
} from '../decorators/check-policies.decorator';
import { PolicyHandler } from '../interfaces/policy-handler.interface';
import { JwtUser } from '../../types/jwt-user.type';
import { Request } from 'express';
import { PermissionCheckerService } from '../services/permission-checker.service';

interface ExtendedRequest extends Request {
  ability?: AppAbility;
}

/**
 * 增強的權限守衛
 * 支援 @CheckPolicies 和 @RequirePermissions 兩種 decorator
 * 提供更靈活的權限檢查方式
 */
@Injectable()
export class EnhancedPermissionGuard {
  private readonly logger = new Logger(EnhancedPermissionGuard.name);

  constructor(
    private reflector: Reflector,
    private caslAbilityFactory: CaslAbilityFactory,
    private permissionChecker?: PermissionCheckerService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 檢查 @CheckPolicies
    const policies =
      this.reflector.get<PolicyHandler[]>(CHECK_POLICIES_KEY, context.getHandler()) || [];

    // 檢查 @RequirePermissions
    const requirements =
      this.reflector.get<PermissionRequirement[]>(REQUIRE_PERMISSIONS_KEY, context.getHandler()) ||
      [];

    // 如果兩種 decorator 都沒有使用，則允許通過
    if (policies.length === 0 && requirements.length === 0) {
      return true;
    }

    const req = context.switchToHttp().getRequest<ExtendedRequest>();
    const user = req.user as JwtUser;

    if (!user) {
      this.logger.warn('EnhancedPermissionGuard: No user object found in request.');
      return false;
    }

    this.logger.debug(
      `EnhancedPermissionGuard: Checking permissions for user ${user.sub} (${user.email}) - Policies: ${policies.length}, Requirements: ${requirements.length}`,
    );

    const ability = await this.createUserAbility(user);
    req.ability = ability;

    this.logger.debug(
      `EnhancedPermissionGuard: Created ability with ${ability.rules.length} rules`,
    );

    // 檢查 @CheckPolicies
    let policiesResult = true;
    if (policies.length > 0) {
      const results = policies.map((handler) => {
        const result = this.execPolicyHandler(handler, ability);
        this.logger.debug(`EnhancedPermissionGuard: Policy check result: ${result}`);
        return result;
      });
      policiesResult = results.every((result) => result);
    }

    // 檢查 @RequirePermissions
    let requirementsResult = true;
    if (requirements.length > 0) {
      const results = requirements.map((requirement) => {
        const result = this.checkPermissionRequirement(requirement, ability, user);
        this.logger.debug(
          `EnhancedPermissionGuard: Permission requirement check (${requirement.action}:${requirement.subject}): ${result}`,
        );
        return result;
      });
      requirementsResult = results.every((result) => result);
    }

    const finalResult = policiesResult && requirementsResult;
    this.logger.debug(`EnhancedPermissionGuard: Final permission check result: ${finalResult}`);

    return finalResult;
  }

  private execPolicyHandler(handler: PolicyHandler, ability: AppAbility): boolean {
    if (typeof handler === 'function') {
      return handler(ability);
    }
    return handler.handle(ability);
  }

  private checkPermissionRequirement(
    requirement: PermissionRequirement,
    ability: AppAbility,
    user: JwtUser,
  ): boolean {
    const { action, subject, conditions, fields } = requirement;

    // 處理條件中的變數替換
    let processedConditions = conditions;
    if (conditions) {
      processedConditions = this.processConditions(conditions, user);
    }

    // 使用 CASL ability 檢查權限
    if (fields && fields.length > 0) {
      // 如果指定了特定欄位，檢查欄位級權限
      return fields.every((field) => ability.can(action as any, subject as any, field));
    } else if (processedConditions) {
      // 如果有條件，使用條件檢查
      const subjectWithConditions = Object.assign({ __type: subject }, processedConditions);
      return ability.can(action as any, subjectWithConditions as any);
    } else {
      // 基本權限檢查
      return ability.can(action as any, subject as any);
    }
  }

  private processConditions(conditions: Record<string, any>, user: JwtUser): Record<string, any> {
    const processed = { ...conditions };

    // 替換常見的用戶變數
    Object.keys(processed).forEach((key) => {
      const value = processed[key];
      if (typeof value === 'string') {
        processed[key] = value
          .replace('{{user_id}}', user.sub)
          .replace('{{tenant_id}}', user.tenant_id || '')
          .replace('{{user_type}}', user.user_type || '');
      }
    });

    return processed;
  }

  private async createUserAbility(user: JwtUser): Promise<AppAbility> {
    try {
      const ability = await this.caslAbilityFactory.createForUser({
        user_id: user.sub,
        tenant_id: user.tenant_id,
        user_type: user.user_type,
      });
      this.logger.debug(`Ability ready for user ${user.sub} with ${ability.rules.length} rules`);
      return ability;
    } catch (error) {
      this.logger.error(`Failed to create ability for user ${user.sub}:`, error.stack);
      throw new ForbiddenException('Failed to create permissions for user.');
    }
  }
}
