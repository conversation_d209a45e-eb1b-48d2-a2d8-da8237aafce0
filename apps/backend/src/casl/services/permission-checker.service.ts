import { Injectable, Logger } from '@nestjs/common';
import { CaslAbilityFactory } from '@/casl/ability/casl-ability.factory';
import { AppAbility, Actions, Subjects } from '@/types/models/casl.model';
import { JwtUser } from '@/types/jwt-user.type';
import {
  performPermissionCheck,
  isSuperAdmin,
  isSystemAdmin,
  isTenantAdmin,
} from '@/casl/utils/permission.utils';

/**
 * 權限檢查結果介面
 */
export interface PermissionCheckResult {
  /**
   * 是否有權限
   */
  granted: boolean;

  /**
   * 檢查的權限
   */
  permission: {
    action: Actions;
    subject: Subjects;
    conditions?: any;
    fields?: string[];
  };

  /**
   * 用戶資訊
   */
  user: {
    id: string;
    /** system | tenant，舊 Token 可能沒有 */
    user_type?: string;
    tenant_id?: string;
  };

  /**
   * 拒絕原因（如果有）
   */
  reason?: string;

  /**
   * 檢查時間戳
   */
  timestamp: Date;
}

/**
 * 批量權限檢查結果介面
 */
export interface BatchPermissionCheckResult {
  /**
   * 所有權限是否都通過
   */
  allGranted: boolean;

  /**
   * 個別權限檢查結果
   */
  results: PermissionCheckResult[];

  /**
   * 通過的權限數量
   */
  grantedCount: number;

  /**
   * 總權限數量
   */
  totalCount: number;
}

/**
 * 權限檢查工具服務
 * 提供統一的權限檢查介面和工具方法
 */
@Injectable()
export class PermissionCheckerService {
  private readonly logger = new Logger(PermissionCheckerService.name);

  constructor(private readonly caslAbilityFactory: CaslAbilityFactory) {}

  /**
   * 檢查用戶是否有特定權限
   */
  async checkPermission(
    user: JwtUser,
    action: Actions,
    subject: Subjects,
    conditions?: any,
    fields?: string[],
  ): Promise<PermissionCheckResult> {
    const startTime = Date.now();

    try {
      const ability = await this.caslAbilityFactory.createForUser({
        user_id: user.id,
        user_type: user.user_type,
        tenant_id: user.tenant_id,
      });

      const granted = performPermissionCheck(ability, action, subject, conditions, fields);
      const executionTime = Date.now() - startTime;

      const result: PermissionCheckResult = {
        granted,
        permission: { action, subject, conditions, fields },
        user: {
          id: user.id,
          user_type: user.user_type,
          tenant_id: user.tenant_id || undefined,
        },
        reason: granted ? undefined : '權限不足',
        timestamp: new Date(),
      };

      this.logger.debug(
        `Permission check: ${action}:${subject} for user ${user.id} = ${granted} ` +
          `(execution time: ${executionTime}ms)`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Error checking permission ${action}:${subject} for user ${user.id}:`,
        error.message,
      );

      return {
        granted: false,
        permission: { action, subject, conditions, fields },
        user: {
          id: user.id,
          user_type: user.user_type,
          tenant_id: user.tenant_id || undefined,
        },
        reason: `權限檢查失敗: ${error.message}`,
        timestamp: new Date(),
      };
    }
  }

  /**
   * 批量檢查多個權限
   */
  async checkPermissions(
    user: JwtUser,
    permissions: Array<{
      action: Actions;
      subject: Subjects;
      conditions?: any;
      fields?: string[];
    }>,
  ): Promise<BatchPermissionCheckResult> {
    const results = await Promise.all(
      permissions.map(({ action, subject, conditions, fields }) =>
        this.checkPermission(user, action, subject, conditions, fields),
      ),
    );

    const grantedCount = results.filter((r) => r.granted).length;
    const allGranted = grantedCount === results.length;

    this.logger.debug(
      `Batch permission check for user ${user.id}: ${grantedCount}/${results.length} granted`,
    );

    return {
      allGranted,
      results,
      grantedCount,
      totalCount: results.length,
    };
  }

  /**
   * 檢查用戶是否可以管理特定資源
   */
  async canManage(user: JwtUser, subject: Subjects, conditions?: any): Promise<boolean> {
    const result = await this.checkPermission(user, 'manage', subject, conditions);
    return result.granted;
  }

  /**
   * 檢查用戶是否可以讀取特定資源
   */
  async canRead(user: JwtUser, subject: Subjects, conditions?: any): Promise<boolean> {
    const result = await this.checkPermission(user, 'read', subject, conditions);
    return result.granted;
  }

  /**
   * 檢查用戶是否可以建立特定資源
   */
  async canCreate(user: JwtUser, subject: Subjects, conditions?: any): Promise<boolean> {
    const result = await this.checkPermission(user, 'create', subject, conditions);
    return result.granted;
  }

  /**
   * 檢查用戶是否可以更新特定資源
   */
  async canUpdate(user: JwtUser, subject: Subjects, conditions?: any): Promise<boolean> {
    const result = await this.checkPermission(user, 'update', subject, conditions);
    return result.granted;
  }

  /**
   * 檢查用戶是否可以刪除特定資源
   */
  async canDelete(user: JwtUser, subject: Subjects, conditions?: any): Promise<boolean> {
    const result = await this.checkPermission(user, 'delete', subject, conditions);
    return result.granted;
  }

  /**
   * 檢查用戶是否可以訪問特定資源
   */
  async canAccess(user: JwtUser, subject: Subjects, conditions?: any): Promise<boolean> {
    const result = await this.checkPermission(user, 'access', subject, conditions);
    return result.granted;
  }

  /**
   * 獲取用戶的所有權限規則
   */
  async getUserPermissions(user: JwtUser): Promise<AppAbility> {
    return this.caslAbilityFactory.createForUser({
      user_id: user.id,
      user_type: user.user_type,
      tenant_id: user.tenant_id,
    });
  }

  /**
   * 檢查用戶是否為超級管理員
   */
  isSuperAdmin(user: JwtUser): boolean {
    return isSuperAdmin(user.user_type, user.role);
  }

  /**
   * 檢查用戶是否為系統管理員
   */
  isSystemAdmin(user: JwtUser): boolean {
    return isSystemAdmin(user.user_type, user.role);
  }

  /**
   * 檢查用戶是否為租戶管理員
   */
  isTenantAdmin(user: JwtUser): boolean {
    return isTenantAdmin(user.user_type, user.role);
  }

  private getSubjectName(subject: any): string {
    if (typeof subject === 'string') {
      return subject;
    }
    if (typeof subject === 'function' && subject.name) {
      return subject.name;
    }
    if (typeof subject === 'object' && subject !== null && subject.constructor?.name) {
      return subject.constructor.name;
    }
    return 'unknown';
  }

  private logPermissionFailure(user: JwtUser, action: string, subject: any, reason: string): void {
    try {
      const logDetails = {
        user: {
          id: user.id,
          user_type: user.user_type,
          tenant_id: user.tenant_id,
        },
        reason,
        timestamp: new Date(),
        action,
        subject: this.getSubjectName(subject),
      };
      this.logger.warn(`Permission Denied: ${JSON.stringify(logDetails)}`);
    } catch (logError) {
      this.logger.error(`Failed to log permission failure: ${logError.message}`);
    }
  }
}
