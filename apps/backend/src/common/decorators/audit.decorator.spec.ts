import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, CallHandler } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { of, throwError } from 'rxjs';
import { AuditInterceptor } from '../interceptors/audit.interceptor';
import { AuditLogService } from '../services/audit-log.service';
import { Audit, AuditOptions } from './audit.decorator';
import { JwtUser } from '../../types/jwt-user.type';
import { Role } from '../../common/enums/role.enum';

describe('Audit Decorator and Interceptor', () => {
  let interceptor: AuditInterceptor;
  let auditLogService: AuditLogService;
  let reflector: Reflector;

  const mockUser: JwtUser = {
    sub: 'user-123',
    id: 'user-123',
    email: '<EMAIL>',
    role: Role.TENANT_USER,
    tenant_id: 'tenant-123',
    user_type: 'tenant',
  };

  const mockAuditLogService = {
    log: jest.fn(),
  };

  const mockReflector = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuditInterceptor,
        {
          provide: AuditLogService,
          useValue: mockAuditLogService,
        },
        {
          provide: Reflector,
          useValue: mockReflector,
        },
      ],
    }).compile();

    interceptor = module.get<AuditInterceptor>(AuditInterceptor);
    auditLogService = module.get<AuditLogService>(AuditLogService);
    reflector = module.get<Reflector>(Reflector);

    jest.clearAllMocks();
  });

  describe('Audit Decorator', () => {
    it('should create metadata with correct options', () => {
      const options: AuditOptions = {
        action: 'USER_CREATE',
        resource: 'user',
        description: 'User ${args.0.email} created',
        logArgs: true,
      };

      // 測試裝飾器是否正確設置元數據
      class TestController {
        @Audit(options)
        createUser(userData: any) {
          return { id: '123', ...userData };
        }
      }

      const controller = new TestController();
      const descriptor = Object.getOwnPropertyDescriptor(TestController.prototype, 'createUser');

      expect(descriptor).toBeDefined();
    });
  });

  describe('AuditInterceptor', () => {
    const createMockExecutionContext = (
      user?: JwtUser,
      args: any[] = [],
      url = '/test',
      method = 'POST',
    ): ExecutionContext => {
      return {
        switchToHttp: () => ({
          getRequest: () => ({
            user,
            url,
            method,
            ip: '127.0.0.1',
            get: (header: string) => (header === 'User-Agent' ? 'Test-Agent' : undefined),
          }),
        }),
        getHandler: () => ({}),
        getArgs: () => args,
      } as any;
    };

    const createMockCallHandler = (returnValue: any): CallHandler => ({
      handle: () => of(returnValue),
    });

    it('should skip interception when no audit metadata is found', async () => {
      mockReflector.get.mockReturnValue(undefined);

      const context = createMockExecutionContext();
      const callHandler = createMockCallHandler('result');

      const result = await interceptor.intercept(context, callHandler).toPromise();

      expect(result).toBe('result');
      expect(mockAuditLogService.log).not.toHaveBeenCalled();
    });

    it('should log audit event on successful method execution', async () => {
      const auditOptions: AuditOptions = {
        action: 'USER_CREATE',
        resource: 'user',
        description: 'User ${args.0.email} created',
        logArgs: true,
        logResult: true,
      };

      mockReflector.get.mockReturnValue(auditOptions);
      mockAuditLogService.log.mockResolvedValue(undefined);

      const context = createMockExecutionContext(mockUser, [{ email: '<EMAIL>' }]);
      const callHandler = createMockCallHandler({ id: '123', email: '<EMAIL>' });

      const result = await interceptor.intercept(context, callHandler).toPromise();

      expect(result).toEqual({ id: '123', email: '<EMAIL>' });
      expect(mockAuditLogService.log).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'USER_CREATE',
          message: 'User <EMAIL> created',
          target_resource: 'user',
          status: 'SUCCESS',
          details: expect.objectContaining({
            success: true,
            arguments: [{ email: '<EMAIL>' }],
            result: { id: '123', email: '<EMAIL>' },
          }),
        }),
        expect.objectContaining({
          user: mockUser,
          ip: '127.0.0.1',
          userAgent: 'Test-Agent',
        }),
      );
    });

    it('should log audit event on method failure', async () => {
      const auditOptions: AuditOptions = {
        action: 'USER_CREATE',
        resource: 'user',
        logArgs: true,
      };

      mockReflector.get.mockReturnValue(auditOptions);
      mockAuditLogService.log.mockResolvedValue(undefined);

      const context = createMockExecutionContext(mockUser, [{ email: '<EMAIL>' }]);
      const error = new Error('Creation failed');
      const callHandler: CallHandler = {
        handle: () => throwError(() => error),
      };

      try {
        await interceptor.intercept(context, callHandler).toPromise();
      } catch (e) {
        // Expected to throw
      }

      expect(mockAuditLogService.log).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'USER_CREATE',
          target_resource: 'user',
          status: 'FAILURE',
          error_message: 'Creation failed',
          details: expect.objectContaining({
            success: false,
            arguments: [{ email: '<EMAIL>' }],
            error: expect.objectContaining({
              message: 'Creation failed',
              name: 'Error',
            }),
          }),
        }),
        expect.any(Object),
      );
    });

    it('should exclude specified arguments and properties', async () => {
      const auditOptions: AuditOptions = {
        action: 'USER_UPDATE',
        logArgs: true,
        excludeArgs: [1], // 排除第二個參數
        excludeProperties: ['password'], // 排除password屬性
      };

      mockReflector.get.mockReturnValue(auditOptions);
      mockAuditLogService.log.mockResolvedValue(undefined);

      const context = createMockExecutionContext(mockUser, [
        { email: '<EMAIL>', password: 'secret123' },
        'sensitive-token',
        'allowed-data',
      ]);
      const callHandler = createMockCallHandler('success');

      await interceptor.intercept(context, callHandler).toPromise();

      expect(mockAuditLogService.log).toHaveBeenCalledWith(
        expect.objectContaining({
          details: expect.objectContaining({
            arguments: [
              { email: '<EMAIL>', password: '[EXCLUDED]' },
              '[EXCLUDED]',
              'allowed-data',
            ],
          }),
        }),
        expect.any(Object),
      );
    });

    it('should use resource ID extractor', async () => {
      const auditOptions: AuditOptions = {
        action: 'USER_UPDATE',
        resource: 'user',
        resourceIdExtractor: (args, result) => result?.id || args[0]?.id,
      };

      mockReflector.get.mockReturnValue(auditOptions);
      mockAuditLogService.log.mockResolvedValue(undefined);

      const context = createMockExecutionContext(mockUser, [{ id: 'user-456' }]);
      const callHandler = createMockCallHandler({ id: 'user-456', updated: true });

      await interceptor.intercept(context, callHandler).toPromise();

      expect(mockAuditLogService.log).toHaveBeenCalledWith(
        expect.objectContaining({
          target_resource_id: 'user-456',
        }),
        expect.any(Object),
      );
    });

    it('should use custom context extractor', async () => {
      const auditOptions: AuditOptions = {
        action: 'USER_LOGIN',
        contextExtractor: (args) => ({
          loginMethod: args[0]?.method || 'email',
          ipWhitelisted: true,
        }),
      };

      mockReflector.get.mockReturnValue(auditOptions);
      mockAuditLogService.log.mockResolvedValue(undefined);

      const context = createMockExecutionContext(mockUser, [{ method: 'oauth' }]);
      const callHandler = createMockCallHandler('success');

      await interceptor.intercept(context, callHandler).toPromise();

      expect(mockAuditLogService.log).toHaveBeenCalledWith(
        expect.objectContaining({
          details: expect.objectContaining({
            loginMethod: 'oauth',
            ipWhitelisted: true,
          }),
        }),
        expect.any(Object),
      );
    });

    it('should respect onlyOnSuccess option', async () => {
      const auditOptions: AuditOptions = {
        action: 'USER_READ',
        onlyOnSuccess: true,
      };

      mockReflector.get.mockReturnValue(auditOptions);
      mockAuditLogService.log.mockResolvedValue(undefined);

      const context = createMockExecutionContext(mockUser);
      const error = new Error('Read failed');
      const callHandler: CallHandler = {
        handle: () => throwError(() => error),
      };

      try {
        await interceptor.intercept(context, callHandler).toPromise();
      } catch (e) {
        // Expected to throw
      }

      // 不應該記錄失敗情況
      expect(mockAuditLogService.log).not.toHaveBeenCalled();
    });

    it('should respect onlyOnFailure option', async () => {
      const auditOptions: AuditOptions = {
        action: 'USER_READ',
        onlyOnFailure: true,
      };

      mockReflector.get.mockReturnValue(auditOptions);
      mockAuditLogService.log.mockResolvedValue(undefined);

      const context = createMockExecutionContext(mockUser);
      const callHandler = createMockCallHandler('success');

      await interceptor.intercept(context, callHandler).toPromise();

      // 不應該記錄成功情況
      expect(mockAuditLogService.log).not.toHaveBeenCalled();
    });

    it('should handle template description with complex patterns', async () => {
      const auditOptions: AuditOptions = {
        action: 'USER_UPDATE',
        description:
          'User ${user.email} updated profile of ${args.0} with ${args.1.changes} changes',
      };

      mockReflector.get.mockReturnValue(auditOptions);
      mockAuditLogService.log.mockResolvedValue(undefined);

      const context = createMockExecutionContext(mockUser, ['target-user-123', { changes: 5 }]);
      const callHandler = createMockCallHandler('success');

      await interceptor.intercept(context, callHandler).toPromise();

      expect(mockAuditLogService.log).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'User <EMAIL> updated profile of target-user-123 with 5 changes',
        }),
        expect.any(Object),
      );
    });
  });
});
