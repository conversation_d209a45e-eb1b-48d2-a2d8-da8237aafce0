import { SetMetadata } from '@nestjs/common';

export const AUDIT_METADATA_KEY = 'audit_metadata';

export interface AuditOptions {
  /**
   * 稽核操作類型
   */
  action: string;

  /**
   * 目標資源類型
   */
  resource?: string;

  /**
   * 稽核描述，支持模板變數
   * 例如：'User ${args.0.name} updated profile'
   */
  description?: string;

  /**
   * 是否記錄方法參數
   */
  logArgs?: boolean;

  /**
   * 是否記錄返回值
   */
  logResult?: boolean;

  /**
   * 要排除的參數索引
   */
  excludeArgs?: number[];

  /**
   * 要排除的參數屬性名稱
   */
  excludeProperties?: string[];

  /**
   * 自定義上下文提取函數
   */
  contextExtractor?: (args: any[], result?: any) => Record<string, any>;

  /**
   * 資源ID提取函數
   */
  resourceIdExtractor?: (args: any[], result?: any) => string;

  /**
   * 是否只在操作成功時記錄
   */
  onlyOnSuccess?: boolean;

  /**
   * 是否只在操作失敗時記錄
   */
  onlyOnFailure?: boolean;
}

/**
 * Audit 裝飾器 - 用於方法級稽核記錄
 *
 * @param options 稽核配置選項
 *
 * @example
 * ```typescript
 * @Audit({
 *   action: 'USER_CREATE',
 *   resource: 'user',
 *   description: 'User created with email ${args.0.email}',
 *   logArgs: true,
 *   resourceIdExtractor: (args, result) => result?.id
 * })
 * async createUser(userData: CreateUserDto): Promise<User> {
 *   // 方法實現
 * }
 * ```
 */
export const Audit = (options: AuditOptions) => SetMetadata(AUDIT_METADATA_KEY, options);
