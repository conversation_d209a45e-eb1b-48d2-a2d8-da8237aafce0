import { Test, TestingModule } from '@nestjs/testing';
import { Execution<PERSON>ontext, <PERSON><PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { of, throwError } from 'rxjs';
import { LoggingInterceptor } from './logging.interceptor';
import { SystemLogService } from '../services/system-log.service';
import { AuditLogService } from '../services/audit-log.service';
import { JwtUser } from '../../types/jwt-user.type';
import { Role } from '../../common/enums/role.enum';

describe('LoggingInterceptor', () => {
  let interceptor: LoggingInterceptor;
  let systemLogService: SystemLogService;
  let auditLogService: AuditLogService;

  const mockUser: JwtUser = {
    sub: 'user-123',
    id: 'user-123',
    email: '<EMAIL>',
    role: Role.TENANT_USER,
    tenant_id: 'tenant-123',
    user_type: 'tenant',
  };

  const mockSystemLogService = {
    logAudit: jest.fn(),
  };

  const mockAuditLogService = {
    log: jest.fn(),
  };

  const mockEventEmitter = {
    emit: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LoggingInterceptor,
        {
          provide: SystemLogService,
          useValue: mockSystemLogService,
        },
        {
          provide: AuditLogService,
          useValue: mockAuditLogService,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
      ],
    }).compile();

    interceptor = module.get<LoggingInterceptor>(LoggingInterceptor);
    systemLogService = module.get<SystemLogService>(SystemLogService);
    auditLogService = module.get<AuditLogService>(AuditLogService);

    jest.clearAllMocks();

    // 靜默化Logger輸出
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});
  });

  const createMockExecutionContext = (
    method = 'POST',
    url = '/users',
    user?: JwtUser,
    body = {},
    params = {},
    query = {},
    headers = {},
  ): ExecutionContext => {
    return {
      switchToHttp: () => ({
        getRequest: () => ({
          method,
          url,
          user,
          body,
          params,
          query,
          headers: {
            'user-agent': 'Test-Agent',
            'x-forwarded-for': '***********',
            ...headers,
          },
          ip: '127.0.0.1',
          get: (header: string) => {
            switch (header.toLowerCase()) {
              case 'user-agent':
                return 'Test-Agent';
              case 'referer':
                return 'https://example.com';
              case 'origin':
                return 'https://example.com';
              case 'content-type':
                return 'application/json';
              default:
                return undefined;
            }
          },
          cookies: {},
        }),
        getResponse: () => ({
          statusCode: 200,
        }),
      }),
    } as any;
  };

  const createMockCallHandler = (returnValue: any): CallHandler => ({
    handle: () => of(returnValue),
  });

  describe('Basic Functionality', () => {
    it('should emit events for successful POST request with both system and audit logs', async () => {
      const context = createMockExecutionContext('POST', '/users', mockUser, { name: 'Test User' });
      const callHandler = createMockCallHandler({ id: '123', name: 'Test User' });

      const result = await interceptor.intercept(context, callHandler).toPromise();

      expect(result).toEqual({ id: '123', name: 'Test User' });

      // 檢查系統日誌事件
      expect(mockEventEmitter.emit).toHaveBeenCalledWith(
        'audit.system.log',
        expect.objectContaining({
          message: expect.stringContaining('POST /users - 成功'),
          user_id: 'user-123',
          tenant_id: 'tenant-123',
          status: 'SUCCESS',
          action: 'API_REQUEST',
        }),
      );

      // 檢查詳細系統日誌事件
      expect(mockEventEmitter.emit).toHaveBeenCalledWith(
        'audit.detailed.log',
        expect.objectContaining({
          logData: expect.objectContaining({
            action: 'USERS_CREATE',
            status: 'SUCCESS',
            target_resource: 'USERS',
          }),
          context: expect.objectContaining({
            user: mockUser,
            ip: '***********',
            userAgent: 'Test-Agent',
          }),
        }),
      );
    });

    it('should handle requests without user', async () => {
      const context = createMockExecutionContext('POST', '/auth/login', undefined);
      const callHandler = createMockCallHandler({ token: 'jwt-token' });

      await interceptor.intercept(context, callHandler).toPromise();

      expect(mockEventEmitter.emit).toHaveBeenCalledWith(
        'audit.system.log',
        expect.objectContaining({
          user_id: undefined,
          tenant_id: undefined,
        }),
      );
    });
  });

  describe('Audit Decision Logic', () => {
    it('should emit detailed audit events for modification operations', async () => {
      const methods = ['POST', 'PUT', 'PATCH', 'DELETE'];

      for (const method of methods) {
        const context = createMockExecutionContext(method, '/users/123', mockUser);
        const callHandler = createMockCallHandler('success');

        await interceptor.intercept(context, callHandler).toPromise();

        expect(mockEventEmitter.emit).toHaveBeenCalledWith(
          'audit.detailed.log',
          expect.any(Object),
        );

        jest.clearAllMocks();
      }
    });

    it('should not emit detailed audit events for regular GET requests', async () => {
      const context = createMockExecutionContext('GET', '/public/data', mockUser);
      const callHandler = createMockCallHandler('data');

      await interceptor.intercept(context, callHandler).toPromise();

      // 應該發送系統日誌事件，但不發送詳細稽核事件
      expect(mockEventEmitter.emit).toHaveBeenCalledWith('audit.system.log', expect.any(Object));
      expect(mockEventEmitter.emit).not.toHaveBeenCalledWith(
        'audit.detailed.log',
        expect.any(Object),
      );
    });
  });

  describe('Error Handling', () => {
    it('should continue execution and emit events even when processing fails', async () => {
      const context = createMockExecutionContext('POST', '/users', mockUser);
      const callHandler = createMockCallHandler('result');

      const result = await interceptor.intercept(context, callHandler).toPromise();

      expect(result).toBe('result');
      expect(mockEventEmitter.emit).toHaveBeenCalledWith('audit.system.log', expect.any(Object));
      expect(mockEventEmitter.emit).toHaveBeenCalledWith('audit.detailed.log', expect.any(Object));
    });
  });
});
