import { Test, TestingModule } from '@nestjs/testing';
import { AuditLogService } from './audit-log.service';
import { PrismaService } from '../../modules/core/prisma/prisma.service';
import { JwtUser } from '../../types/jwt-user.type';
import { Role } from '../../common/enums/role.enum';

describe('AuditLogService', () => {
  let service: AuditLogService;
  let prismaService: PrismaService;

  const mockJwtUser: JwtUser = {
    sub: 'user-id',
    id: 'user-id',
    email: '<EMAIL>',
    role: Role.TENANT_USER,
    tenant_id: 'tenant-id',
    user_type: 'tenant',
    iat: **********,
    exp: **********,
  };

  const mockPrismaService = {
    system_logs: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      groupBy: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuditLogService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: 'EncryptionService',
          useValue: {
            encrypt: jest.fn().mockImplementation((text) => `encrypted_${text}`),
            decrypt: jest.fn().mockImplementation((text) => text.replace('encrypted_', '')),
          },
        },
      ],
    }).compile();

    service = module.get<AuditLogService>(AuditLogService);
    prismaService = module.get<PrismaService>(PrismaService);

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('log', () => {
    it('should create audit log with provided data', async () => {
      const auditData = {
        action: 'TEST_ACTION',
        message: 'Test message',
        target_resource: 'user',
        target_resource_id: 'user-1',
      };

      const context = {
        user: mockJwtUser,
        ip: '127.0.0.1',
        userAgent: 'Test Agent',
        path: '/test',
        method: 'POST',
      };

      mockPrismaService.system_logs.create.mockResolvedValue({});

      await service.log(auditData, context);

      expect(mockPrismaService.system_logs.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          level: 'AUDIT',
          action: 'TEST_ACTION',
          message: 'Test message',
          user_id: mockJwtUser.id,
          tenant_id: mockJwtUser.tenant_id,
          ip: '127.0.0.1',
          target_resource: 'user',
          target_resource_id: 'user-1',
          status: 'SUCCESS',
          path: '/test',
          method: 'POST',
          details: expect.objectContaining({
            userAgent: 'Test Agent',
            timestamp: expect.any(String),
          }),
        }),
      });
    });

    it('should handle errors gracefully without throwing', async () => {
      mockPrismaService.system_logs.create.mockRejectedValue(new Error('Database error'));

      await expect(service.log({ action: 'TEST_ACTION' })).resolves.not.toThrow();
    });

    it('should use default message when not provided', async () => {
      mockPrismaService.system_logs.create.mockResolvedValue({});

      await service.log({ action: 'TEST_ACTION' });

      expect(mockPrismaService.system_logs.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          message: 'TEST_ACTION operation',
        }),
      });
    });
  });

  describe('logUserAction', () => {
    it('should log user action with correct format', async () => {
      mockPrismaService.system_logs.create.mockResolvedValue({});

      await service.logUserAction('login', mockJwtUser, { extra: 'data' });

      expect(mockPrismaService.system_logs.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          action: 'USER_LOGIN',
          user_id: mockJwtUser.id,
          tenant_id: mockJwtUser.tenant_id,
          details: expect.objectContaining({
            extra: 'data',
          }),
        }),
      });
    });
  });

  describe('logResourceAction', () => {
    it('should log resource action with correct format', async () => {
      mockPrismaService.system_logs.create.mockResolvedValue({});

      await service.logResourceAction('create', 'project', 'project-1', mockJwtUser, {
        name: 'Test Project',
      });

      expect(mockPrismaService.system_logs.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          action: 'PROJECT_CREATE',
          target_resource: 'project',
          target_resource_id: 'project-1',
          user_id: mockJwtUser.id,
          tenant_id: mockJwtUser.tenant_id,
          details: expect.objectContaining({
            name: 'Test Project',
          }),
        }),
      });
    });
  });

  describe('logSecurityEvent', () => {
    it('should log security event with warning status by default', async () => {
      mockPrismaService.system_logs.create.mockResolvedValue({});

      await service.logSecurityEvent('suspicious_login', mockJwtUser);

      expect(mockPrismaService.system_logs.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          action: 'SECURITY_SUSPICIOUS_LOGIN',
          status: 'WARNING',
          user_id: mockJwtUser.id,
          tenant_id: mockJwtUser.tenant_id,
        }),
      });
    });

    it('should log security event with custom status', async () => {
      mockPrismaService.system_logs.create.mockResolvedValue({});

      await service.logSecurityEvent('failed_login', mockJwtUser, {}, {}, 'FAILURE');

      expect(mockPrismaService.system_logs.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          action: 'SECURITY_FAILED_LOGIN',
          status: 'FAILURE',
        }),
      });
    });
  });

  describe('logPermissionEvent', () => {
    it('should log permission event with success status by default', async () => {
      mockPrismaService.system_logs.create.mockResolvedValue({});

      await service.logPermissionEvent('access_granted', 'project', mockJwtUser);

      expect(mockPrismaService.system_logs.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          action: 'PERMISSION_ACCESS_GRANTED',
          target_resource: 'project',
          status: 'SUCCESS',
          user_id: mockJwtUser.id,
          tenant_id: mockJwtUser.tenant_id,
        }),
      });
    });
  });

  describe('findLogs', () => {
    it('should query audit logs with filters', async () => {
      const mockLogs = [
        { id: '1', action: 'USER_LOGIN', created_at: new Date() },
        { id: '2', action: 'PROJECT_CREATE', created_at: new Date() },
      ];

      mockPrismaService.system_logs.findMany.mockResolvedValue(mockLogs);
      mockPrismaService.system_logs.count.mockResolvedValue(2);

      const filters = {
        user_id: 'user-1',
        tenant_id: 'tenant-1',
        action: 'USER',
        limit: 10,
        offset: 0,
      };

      const result = await service.findLogs(filters);

      expect(result).toEqual({
        logs: mockLogs,
        total: 2,
        page: 1,
        limit: 10,
      });

      expect(mockPrismaService.system_logs.findMany).toHaveBeenCalledWith({
        where: expect.objectContaining({
          level: 'AUDIT',
          user_id: 'user-1',
          tenant_id: 'tenant-1',
          action: {
            contains: 'USER',
            mode: 'insensitive',
          },
        }),
        orderBy: { created_at: 'desc' },
        take: 10,
        skip: 0,
      });
    });

    it('should handle search query filter', async () => {
      mockPrismaService.system_logs.findMany.mockResolvedValue([]);
      mockPrismaService.system_logs.count.mockResolvedValue(0);

      await service.findLogs({ search_query: 'test' });

      expect(mockPrismaService.system_logs.findMany).toHaveBeenCalledWith({
        where: expect.objectContaining({
          OR: [
            {
              message: {
                contains: 'test',
                mode: 'insensitive',
              },
            },
            {
              action: {
                contains: 'test',
                mode: 'insensitive',
              },
            },
          ],
        }),
        orderBy: { created_at: 'desc' },
        take: 50,
        skip: 0,
      });
    });

    it('should handle date range filters', async () => {
      mockPrismaService.system_logs.findMany.mockResolvedValue([]);
      mockPrismaService.system_logs.count.mockResolvedValue(0);

      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-12-31');

      await service.findLogs({ start_date: startDate, end_date: endDate });

      expect(mockPrismaService.system_logs.findMany).toHaveBeenCalledWith({
        where: expect.objectContaining({
          created_at: {
            gte: startDate,
            lte: endDate,
          },
        }),
        orderBy: { created_at: 'desc' },
        take: 50,
        skip: 0,
      });
    });
  });

  describe('sanitizeDetails', () => {
    it('should redact sensitive fields', async () => {
      mockPrismaService.system_logs.create.mockResolvedValue({});

      const sensitiveData = {
        username: 'testuser',
        password: 'secret123',
        token: 'jwt-token',
        api_key: 'api-key-123',
        normal_field: 'normal_value',
      };

      await service.log({
        action: 'TEST_ACTION',
        details: sensitiveData,
      });

      expect(mockPrismaService.system_logs.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          details: expect.objectContaining({
            username: 'testuser',
            password: '***REDACTED***',
            token: '***REDACTED***',
            api_key: '***REDACTED***',
            normal_field: 'normal_value',
          }),
        }),
      });
    });

    it('should handle nested objects', async () => {
      mockPrismaService.system_logs.create.mockResolvedValue({});

      const nestedData = {
        user: {
          name: 'Test User',
          credentials: {
            password: 'secret',
            api_key: 'key123',
          },
        },
        settings: {
          theme: 'dark',
        },
      };

      await service.log({
        action: 'TEST_ACTION',
        details: nestedData,
      });

      expect(mockPrismaService.system_logs.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          details: expect.objectContaining({
            user: {
              name: 'Test User',
              credentials: '***REDACTED***',
            },
            settings: {
              theme: 'dark',
            },
          }),
        }),
      });
    });
  });

  describe('getAuditStats', () => {
    it('should return audit statistics', async () => {
      const mockActionStats = [
        { action: 'USER_LOGIN', _count: 10 },
        { action: 'PROJECT_CREATE', _count: 5 },
      ];

      const mockStatusStats = [
        { status: 'SUCCESS', _count: 12 },
        { status: 'FAILURE', _count: 3 },
      ];

      const mockResourceStats = [
        { target_resource: 'project', _count: 8 },
        { target_resource: 'user', _count: 7 },
      ];

      mockPrismaService.system_logs.count.mockResolvedValue(15);
      mockPrismaService.system_logs.groupBy
        .mockResolvedValueOnce(mockActionStats)
        .mockResolvedValueOnce(mockStatusStats)
        .mockResolvedValueOnce(mockResourceStats);

      const result = await service.getAuditStats('tenant-1');

      expect(result).toEqual({
        totalLogs: 15,
        logsByAction: {
          USER_LOGIN: 10,
          PROJECT_CREATE: 5,
        },
        logsByStatus: {
          SUCCESS: 12,
          FAILURE: 3,
        },
        logsByResource: {
          project: 8,
          user: 7,
        },
      });
    });

    it('should handle date filters in stats', async () => {
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-12-31');

      mockPrismaService.system_logs.count.mockResolvedValue(0);
      mockPrismaService.system_logs.groupBy.mockResolvedValue([]);

      await service.getAuditStats('tenant-1', startDate, endDate);

      expect(mockPrismaService.system_logs.count).toHaveBeenCalledWith({
        where: expect.objectContaining({
          level: 'AUDIT',
          tenant_id: 'tenant-1',
          created_at: {
            gte: startDate,
            lte: endDate,
          },
        }),
      });
    });
  });
});
