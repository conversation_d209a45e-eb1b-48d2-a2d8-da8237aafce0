import { Test, TestingModule } from '@nestjs/testing';
import { SchedulerRegistry, ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { LogArchivingService } from './log-archiving.service';
import { CommonModule } from '../common.module';
import { PrismaModule } from '../../modules/core/prisma/prisma.module';
import { EncryptionModule } from '../../modules/core/encryption/encryption.module';
import { StorageModule } from '../../modules/core/storage/storage.module';
import { SettingsModule } from '../../modules/admin/settings/settings.module';
import { SettingsService } from '../../modules/admin/settings/settings.service';
import { DEFAULT_ARCHIVING_SETTINGS } from '../../modules/admin/settings/interfaces/archiving-settings.interface';

describe('LogArchivingService Integration', () => {
  let module: TestingModule;
  let service: LogArchivingService;
  let settingsService: SettingsService;
  let schedulerRegistry: SchedulerRegistry;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        EventEmitterModule.forRoot(),
        ScheduleModule.forRoot(),
        PrismaModule,
        EncryptionModule,
        StorageModule,
        SettingsModule,
        CommonModule,
      ],
    }).compile();

    service = module.get<LogArchivingService>(LogArchivingService);
    settingsService = module.get<SettingsService>(SettingsService);
    schedulerRegistry = module.get<SchedulerRegistry>(SchedulerRegistry);
  });

  afterAll(async () => {
    await module.close();
  });

  describe('模組整合測試', () => {
    it('應該正確初始化所有依賴', () => {
      expect(service).toBeDefined();
      expect(settingsService).toBeDefined();
      expect(schedulerRegistry).toBeDefined();
    });

    it('應該能夠讀取歸檔設定', async () => {
      const settings = await settingsService.getArchivingSettings();
      expect(settings).toBeDefined();
      expect(settings.enabled).toBeDefined();
      expect(settings.schedule).toBeDefined();
    });

    it('LogArchivingService應該能夠設定動態排程', async () => {
      // 確保設定為啟用狀態
      const enabledSettings = {
        ...DEFAULT_ARCHIVING_SETTINGS,
        enabled: true,
        schedule: '0 2 * * *',
      };

      // 模擬設定更新
      await service.handleArchivingSettingsUpdated({
        settings: enabledSettings,
        userId: 'test-user',
        timestamp: new Date(),
      });

      // 驗證沒有拋出錯誤
      expect(true).toBe(true);
    });

    it('應該能夠處理歸檔設定關閉', async () => {
      const disabledSettings = {
        ...DEFAULT_ARCHIVING_SETTINGS,
        enabled: false,
      };

      await service.handleArchivingSettingsUpdated({
        settings: disabledSettings,
        userId: 'test-user',
        timestamp: new Date(),
      });

      expect(true).toBe(true);
    });
  });

  describe('排程管理測試', () => {
    it('應該能夠管理排程任務', () => {
      // 測試排程註冊表的基本功能
      expect(schedulerRegistry.addTimeout).toBeDefined();
      expect(schedulerRegistry.deleteTimeout).toBeDefined();
      expect(schedulerRegistry.doesExist).toBeDefined();
    });
  });
});
