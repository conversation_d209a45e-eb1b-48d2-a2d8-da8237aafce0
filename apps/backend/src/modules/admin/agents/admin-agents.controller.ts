import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Get,
  Put,
  Delete,
  Param,
  Query,
  UseGuards,
  Logger,
  HttpCode,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AgentsService } from '../../core/agents/agents.service';
import {
  CreateAgentDto,
  UpdateAgentDto,
  TestAgentDto,
  OptimizePromptDto,
  ExecuteAgentDto,
  AssignToolsToAgentDto,
  AgentToolResponseDto,
} from '../../core/agents/dto/agent.dto';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { CurrentUser } from '../../core/auth/decorators/current-user.decorator';
import { AiAgentScope } from '@prisma/client';
import { CheckPolicies } from '../../../casl/decorators/check-policies.decorator';
import { AppAbility } from '../../../types/models/casl.model';
import { Actions, Subjects } from '@horizai/permissions';
import { JwtUser } from '../../../types/jwt-user.type';
import { PoliciesGuard } from '../../../casl/guards/permission.guard';

@ApiTags('admin/ai/agents')
@UseGuards(JwtAuthGuard, PoliciesGuard)
@Controller('admin/ai/agents')
export class AdminAgentsController {
  private readonly logger = new Logger(AdminAgentsController.name);

  constructor(private readonly agentsService: AgentsService) {}

  @Get()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, 'ai_agents'))
  @ApiOperation({ summary: '系統管理員讀取所有 AI Agent' })
  findAll(
    @Query('scope') scope?: AiAgentScope,
    @Query('tenant_id') tenant_id?: string,
    @Query('workspace_id') workspace_id?: string,
  ) {
    return this.agentsService.findAll(tenant_id, workspace_id, scope);
  }

  @Get(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, 'ai_agents'))
  @ApiOperation({ summary: '系統管理員根據 ID 讀取 AI Agent' })
  async findOne(@Param('id') id: string) {
    return this.agentsService.findOne(id);
  }

  @Post()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.CREATE, 'ai_agents'))
  @ApiOperation({ summary: '系統管理員建立新的系統級 AI Agent' })
  async create(@Body(ValidationPipe) createAgentDto: CreateAgentDto, @CurrentUser() user: JwtUser) {
    // 確保系統管理員只能創建系統級 Agent
    const systemAgentDto = {
      ...createAgentDto,
      scope: 'SYSTEM' as AiAgentScope,
    };
    return this.agentsService.create(systemAgentDto, user.id);
  }

  @Put(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, 'ai_agents'))
  @ApiOperation({ summary: '系統管理員更新 AI Agent' })
  async update(
    @Param('id') id: string,
    @Body(ValidationPipe) updateAgentDto: UpdateAgentDto,
    @CurrentUser() user: JwtUser,
  ) {
    return this.agentsService.update(id, updateAgentDto, user.id);
  }

  @Delete(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.DELETE, 'ai_agents'))
  @ApiOperation({ summary: '系統管理員刪除 AI Agent' })
  async remove(@Param('id') id: string) {
    return this.agentsService.delete(id);
  }

  @Post('test')
  @ApiOperation({ summary: '系統管理員測試 Agent 設定' })
  async testAgent(@Body() dto: TestAgentDto) {
    return await this.agentsService.testAgent(
      dto.agent_id,
      dto.message,
      dto.prompt,
      dto.temperature,
    );
  }

  @Post('optimize-prompt')
  @ApiOperation({ summary: '系統管理員優化提示詞' })
  async optimizePrompt(@Body() dto: OptimizePromptDto) {
    return await this.agentsService.optimizePrompt(dto);
  }

  @Put(':id/status')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, 'ai_agents'))
  @ApiOperation({ summary: '系統管理員更新 Agent 啟用狀態' })
  async updateStatus(@Param('id') id: string, @Body('isEnabled') isEnabled: boolean) {
    return this.agentsService.updateStatus(id, isEnabled);
  }

  @Get(':id/tools')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, 'ai_agents'))
  @ApiOperation({ summary: '系統管理員取得 Agent 的工具列表' })
  @ApiResponse({
    status: 200,
    description: '成功取得 Agent 的工具列表',
    type: [AgentToolResponseDto],
  })
  @ApiResponse({ status: 404, description: '找不到指定的 Agent' })
  async getAgentTools(@Param('id') id: string): Promise<AgentToolResponseDto[]> {
    return this.agentsService.getAgentTools(id);
  }

  @Put(':id/tools')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, 'ai_agents'))
  @ApiOperation({ summary: '系統管理員為 Agent 指派工具' })
  @ApiResponse({ status: 200, description: '成功指派工具給 Agent' })
  @ApiResponse({ status: 400, description: '請求參數錯誤' })
  @ApiResponse({ status: 404, description: '找不到指定的 Agent 或工具' })
  async assignToolsToAgent(
    @Param('id') id: string,
    @Body() assignToolsDto: AssignToolsToAgentDto,
  ): Promise<{ message: string; assignedCount: number }> {
    await this.agentsService.assignToolsToAgent(id, assignToolsDto.toolIds);
    return {
      message: '成功指派工具給 Agent',
      assignedCount: assignToolsDto.toolIds.length,
    };
  }

  @Delete(':id/tools/:toolId')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, 'ai_agents'))
  @ApiOperation({ summary: '系統管理員移除 Agent 的特定工具' })
  @ApiResponse({ status: 204, description: '成功移除工具' })
  @ApiResponse({ status: 404, description: '找不到指定的 Agent 或工具關聯' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async removeToolFromAgent(
    @Param('id') id: string,
    @Param('toolId') toolId: string,
  ): Promise<void> {
    await this.agentsService.removeToolFromAgent(id, toolId);
  }

  @Get('tools/available')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, 'ai_agents'))
  @ApiOperation({ summary: '系統管理員取得所有可用工具' })
  async getAvailableTools(@CurrentUser() user: JwtUser) {
    return this.agentsService.getAvailableTools(user.tenant_id || '', user);
  }

  @Get('status/overview')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, 'ai_agents'))
  @ApiOperation({ summary: '系統管理員取得 Agent 狀態總覽' })
  async getAgentStatus() {
    return this.agentsService.getAgentStatus();
  }

  @Post(':id/execute')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.EXECUTE, 'ai_agents'))
  @HttpCode(200)
  @ApiOperation({ summary: '系統管理員執行 Agent' })
  async execute(@Param('id') id: string, @Body() body: ExecuteAgentDto) {
    try {
      const response = await this.agentsService.execute(id, body);
      return response;
    } catch (error) {
      this.logger.error(`執行 Agent ${id} 失敗:`, error);
      throw error;
    }
  }
} 