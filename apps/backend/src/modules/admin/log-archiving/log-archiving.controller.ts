import { Controller, Post, Get, Query, UseGuards, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { Request } from 'express';
import { JwtAuthGuard } from '../../core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../core/auth/guards/roles.guard';
import { Roles } from '../../core/auth/decorators/roles.decorator';
import { Role } from '../../../common/enums/role.enum';
import { Audit } from '../../../common/decorators/audit.decorator';
import { LogArchivingService } from '../../../common/services/log-archiving.service';

interface RequestWithUser extends Request {
  user?: {
    id: string;
    email: string;
    role: Role;
    tenant_id?: string;
  };
}

@ApiTags('admin/log-archiving')
@Controller('admin/log-archiving')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class LogArchivingController {
  constructor(private readonly logArchivingService: LogArchivingService) {}

  @Post('execute')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '手動執行日誌歸檔' })
  @ApiQuery({
    name: 'tenantId',
    required: false,
    description: '可選的租戶 ID，僅歸檔特定租戶的日誌',
  })
  @Audit({
    action: 'LOG_ARCHIVING_EXECUTE',
    resource: 'system_log',
    description: 'Manually executed log archiving',
    logArgs: true,
  })
  async executeArchiving(@Query('tenantId') tenantId?: string, @Req() req?: RequestWithUser) {
    const result = await this.logArchivingService.executeArchiving(tenantId);

    return {
      success: result.status === 'success',
      data: result,
      message:
        result.status === 'success'
          ? `成功歸檔 ${result.archivedCount} 條日誌記錄`
          : `歸檔失敗: ${result.error}`,
    };
  }

  @Get('statistics')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.TENANT_ADMIN)
  @ApiOperation({ summary: '獲取歸檔統計資訊' })
  @ApiQuery({
    name: 'tenantId',
    required: false,
    description: '可選的租戶 ID，獲取特定租戶的歸檔統計',
  })
  async getArchiveStatistics(@Query('tenantId') tenantId?: string, @Req() req?: RequestWithUser) {
    // 如果是租戶管理員，只能查看自己租戶的統計
    const targetTenantId = req?.user?.role === Role.TENANT_ADMIN ? req.user.tenant_id : tenantId;

    const statistics = await this.logArchivingService.getArchiveStatistics(targetTenantId);

    return {
      success: true,
      data: statistics,
      message: '成功獲取歸檔統計資訊',
    };
  }

  @Post('cleanup')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @ApiOperation({ summary: '清理過期的歸檔檔案' })
  @ApiQuery({
    name: 'tenantId',
    required: false,
    description: '可選的租戶 ID，僅清理特定租戶的過期歸檔',
  })
  @Audit({
    action: 'LOG_ARCHIVING_CLEANUP',
    resource: 'archived_log',
    description: 'Cleaned up expired archive files',
    logArgs: true,
  })
  async cleanupExpiredArchives(@Query('tenantId') tenantId?: string, @Req() req?: RequestWithUser) {
    await this.logArchivingService.cleanupExpiredArchives(tenantId);

    return {
      success: true,
      message: '成功清理過期歸檔檔案',
    };
  }

  @Get('status')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.TENANT_ADMIN)
  @ApiOperation({ summary: '獲取歸檔服務狀態' })
  async getArchivingStatus(@Req() req?: RequestWithUser) {
    // 這裡可以返回歸檔服務的當前狀態
    return {
      success: true,
      data: {
        isRunning: false, // 可以從服務中獲取實際狀態
        lastExecution: null, // 可以從設定中獲取
        nextScheduledExecution: null, // 基於 cron 表達式計算
      },
      message: '成功獲取歸檔服務狀態',
    };
  }
}
