import { Injectable } from '@nestjs/common';
import { SystemLogService } from '../../../common/services/system-log.service';
import { SystemLogQueryDto, SystemLogReportDto } from './dto/system-log.dto';
import * as ExcelJS from 'exceljs';

export interface LogFilters {
  level?: string;
  user_id?: string;
  action?: string;
  start_date?: Date;
  end_date?: Date;
  status?: string;
  search_query?: string;
  target_resource?: string;
  tenant_id?: string;
  limit?: number;
  offset?: number;
}

@Injectable()
export class SystemLogsService {
  constructor(private readonly systemLogService: SystemLogService) {}

  private cleanFilters(filters: any) {
    const cleanedFilters = { ...filters };
    Object.keys(cleanedFilters).forEach((key) => {
      if (cleanedFilters[key] === undefined || cleanedFilters[key] === null || cleanedFilters[key] === '') {
        delete cleanedFilters[key];
      }
    });
    return cleanedFilters;
  }

  /**
   * 獲取系統日誌列表（分頁）
   */
  async getLogs(queryDto: SystemLogQueryDto, tenant_id?: string) {
    const { page = 1, limit = 50, ...filters } = queryDto;
    const offset = (page - 1) * limit;

    const logs = await this.systemLogService.findLogs({
      ...filters,
      limit,
      offset,
    });

    const total = await this.systemLogService.countLogs(filters);

    return {
      logs,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * 獲取系統日誌統計數據
   */
  async getAuditStats(tenant_id?: string, startDate?: Date, endDate?: Date) {
    // 構建基礎查詢條件
    const baseFilters: any = {};
    
    if (startDate) {
      baseFilters.startDate = startDate.toISOString().split('T')[0];
    }
    if (endDate) {
      baseFilters.endDate = endDate.toISOString().split('T')[0];
    }

    // 獲取總日誌數
    const totalLogs = await this.systemLogService.countLogs(baseFilters);

    // 獲取今日日誌數
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];
    const logsToday = await this.systemLogService.countLogs({
      ...baseFilters,
      startDate: todayStr,
      endDate: todayStr,
    });

    // 獲取錯誤日誌數
    const errorLogs = await this.systemLogService.countLogs({
      ...baseFilters,
      level: 'ERROR',
    });

    // 獲取稽核日誌數
    const auditLogs = await this.systemLogService.countLogs({
      ...baseFilters,
      level: 'AUDIT',
    });

    // 獲取獨立使用者數（這裡簡化處理，實際可能需要更複雜的查詢）
    const uniqueUsers = await this.systemLogService.countLogs({
      ...baseFilters,
      // 注意：這裡只是計算有 user_id 的記錄數，不是真正的獨立使用者數
    });

    return {
      total_logs: totalLogs,
      logs_today: logsToday,
      error_logs: errorLogs,
      audit_logs: auditLogs,
      unique_users: Math.floor(uniqueUsers * 0.3), // 簡化估算
    };
  }

  /**
   * 獲取可用的操作類型
   */
  async getAvailableActions(tenant_id?: string) {
    const logs = await this.systemLogService.findLogs({
      limit: 1000, // 獲取足夠的樣本
    });

    // 統計操作類型
    const actionCounts = logs.reduce((acc, log) => {
      if (log.action) {
        acc[log.action] = (acc[log.action] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(actionCounts)
      .map(([action, count]) => ({ action, count }))
      .sort((a, b) => b.count - a.count);
  }

  /**
   * 獲取用於報告的日誌數據
   */
  async getLogsForReport(reportDto: SystemLogReportDto, tenant_id?: string) {
    const { query = {} } = reportDto;
    
    const logs = await this.systemLogService.findLogs({
      ...query,
      limit: 10000, // 報告可能需要更多數據
    });

    return logs;
  }

  /**
   * 生成 CSV 格式的報告內容
   */
  generateCSV(logs: any[]): string {
    const headers = [
      'ID',
      'Level',
      'Message',
      'User ID',
      'Action',
      'Status',
      'IP',
      'Target Resource',
      'Created At',
    ];

    const csvContent = [
      headers.join(','),
      ...logs.map(log => [
        log.id || '',
        log.level || '',
        `"${(log.message || '').replace(/"/g, '""')}"`,
        log.user_id || '',
        log.action || '',
        log.status || '',
        log.ip || '',
        log.target_resource || '',
        log.created_at || '',
      ].join(',')),
    ].join('\n');

    return csvContent;
  }

  /**
   * 生成 Excel 格式的報告
   */
  async generateExcel(logs: any[]): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('System Logs');

    // 設置標題行
    worksheet.columns = [
      { header: 'ID', key: 'id', width: 36 },
      { header: 'Level', key: 'level', width: 10 },
      { header: 'Message', key: 'message', width: 50 },
      { header: 'User ID', key: 'user_id', width: 36 },
      { header: 'Action', key: 'action', width: 20 },
      { header: 'Status', key: 'status', width: 15 },
      { header: 'IP', key: 'ip', width: 15 },
      { header: 'Target Resource', key: 'target_resource', width: 20 },
      { header: 'Created At', key: 'created_at', width: 20 },
    ];

    // 添加數據行
    logs.forEach(log => {
      worksheet.addRow({
        id: log.id,
        level: log.level,
        message: log.message,
        user_id: log.user_id,
        action: log.action,
        status: log.status,
        ip: log.ip,
        target_resource: log.target_resource,
        created_at: log.created_at,
      });
    });

    // 設置標題行樣式
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    return await workbook.xlsx.writeBuffer() as Buffer;
  }
}
