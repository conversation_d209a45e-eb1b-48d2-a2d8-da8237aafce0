import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsBoolean, IsObject, IsEnum } from 'class-validator';

export enum DefaultAiModel {
  GPT_3_5_TURBO = 'gpt-3.5-turbo',
  GPT_4 = 'gpt-4',
  GPT_4_TURBO = 'gpt-4-turbo',
  CLAUDE_3_HAIKU = 'claude-3-haiku',
  CLAUDE_3_SONNET = 'claude-3-sonnet',
  CLAUDE_3_OPUS = 'claude-3-opus',
}

export enum NotificationMethod {
  EMAIL = 'email',
  LINE = 'line',
  IN_APP = 'in-app',
}

export class TenantSettingsDto {
  @ApiProperty({
    description: '預設 AI 模型',
    enum: DefaultAiModel,
    example: DefaultAiModel.GPT_3_5_TURBO,
    required: false,
  })
  @IsOptional()
  @IsEnum(DefaultAiModel)
  defaultAiModel?: DefaultAiModel;

  @ApiProperty({
    description: '通知設定',
    example: {
      methods: ['email', 'in-app'],
      emailNotifications: true,
      lineNotifications: false,
      inAppNotifications: true,
      criticalAlertsOnly: false,
    },
  })
  @IsOptional()
  @IsObject()
  notificationSettings?: {
    methods: NotificationMethod[];
    emailNotifications: boolean;
    lineNotifications: boolean;
    inAppNotifications: boolean;
    criticalAlertsOnly: boolean;
  };

  @ApiProperty({
    description: 'AI 使用設定',
    example: {
      maxTokensPerRequest: 4000,
      enableAutoRetry: true,
      retryAttempts: 3,
      enableFallbackModel: true,
      fallbackModel: 'gpt-3.5-turbo',
    },
  })
  @IsOptional()
  @IsObject()
  aiUsageSettings?: {
    maxTokensPerRequest: number;
    enableAutoRetry: boolean;
    retryAttempts: number;
    enableFallbackModel: boolean;
    fallbackModel?: string;
  };

  @ApiProperty({
    description: '專案管理設定',
    example: {
      defaultProjectTemplate: 'construction',
      autoCreateMilestones: true,
      requireApprovalForTasks: false,
      enableProgressTracking: true,
    },
  })
  @IsOptional()
  @IsObject()
  projectSettings?: {
    defaultProjectTemplate: string;
    autoCreateMilestones: boolean;
    requireApprovalForTasks: boolean;
    enableProgressTracking: boolean;
  };

  @ApiProperty({
    description: '安全設定',
    example: {
      sessionTimeout: 3600,
      requireMfa: false,
      allowedIpRanges: [],
      dataRetentionDays: 365,
    },
  })
  @IsOptional()
  @IsObject()
  securitySettings?: {
    sessionTimeout: number;
    requireMfa: boolean;
    allowedIpRanges: string[];
    dataRetentionDays: number;
  };

  @ApiProperty({
    description: '整合設定',
    example: {
      enableLineIntegration: false,
      enableEmailIntegration: true,
      webhookUrl: null,
      customIntegrations: {},
    },
  })
  @IsOptional()
  @IsObject()
  integrationSettings?: {
    enableLineIntegration: boolean;
    enableEmailIntegration: boolean;
    webhookUrl?: string;
    customIntegrations: Record<string, any>;
  };

  @ApiProperty({
    description: '自訂設定',
    example: {
      customFields: {},
      branding: {
        primaryColor: '#1976d2',
        logo: null,
      },
    },
  })
  @IsOptional()
  @IsObject()
  customSettings?: Record<string, any>;
}

export class UpdateTenantSettingsDto extends TenantSettingsDto {}

export class TenantSettingsResponseDto {
  @ApiProperty({
    description: '租戶 ID',
    example: 'tenant_123',
  })
  tenantId: string;

  @ApiProperty({
    description: '租戶設定',
    type: TenantSettingsDto,
  })
  settings: TenantSettingsDto;

  @ApiProperty({
    description: '最後更新時間',
    example: '2023-12-01T10:30:00Z',
  })
  updatedAt: Date;
}
