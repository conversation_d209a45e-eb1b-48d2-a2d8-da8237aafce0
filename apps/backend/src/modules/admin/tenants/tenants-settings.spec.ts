import { Test, TestingModule } from '@nestjs/testing';
import { TenantsController } from './tenants.controller';
import { TenantsService } from './tenants.service';
import { PrismaService } from '../../core/prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { UpdateTenantSettingsDto } from './dto/tenant-settings.dto';
import { CaslAbilityFactory } from '../../../casl/ability/casl-ability.factory';
import { PermissionCheckerService } from '../../../casl/services/permission-checker.service';
import { Reflector } from '@nestjs/core';

describe('TenantsController - Settings Management', () => {
  let controller: TenantsController;
  let service: TenantsService;
  let prismaService: PrismaService;

  const mockTenant = {
    id: 'tenant-123',
    name: 'Test Tenant',
    settings: {
      defaultAiModel: 'gpt-4',
      notificationSettings: {
        methods: ['email'],
        emailNotifications: true,
        lineNotifications: false,
        inAppNotifications: true,
        criticalAlertsOnly: false,
      },
    },
    updated_at: new Date('2023-12-01T10:30:00Z'),
  };

  const mockPrismaService = {
    tenants: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockCaslAbilityFactory = {
    createForUser: jest.fn().mockReturnValue({
      can: jest.fn().mockReturnValue(true),
    }),
  };

  const mockPermissionCheckerService = {
    checkPermission: jest.fn().mockReturnValue(true),
  };

  const mockReflector = {
    get: jest.fn(),
    getAllAndOverride: jest.fn(),
    getAllAndMerge: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TenantsController],
      providers: [
        TenantsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: CaslAbilityFactory,
          useValue: mockCaslAbilityFactory,
        },
        {
          provide: PermissionCheckerService,
          useValue: mockPermissionCheckerService,
        },
        {
          provide: Reflector,
          useValue: mockReflector,
        },
      ],
    }).compile();

    controller = module.get<TenantsController>(TenantsController);
    service = module.get<TenantsService>(TenantsService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getTenantSettings', () => {
    it('should return tenant settings successfully', async () => {
      mockPrismaService.tenants.findUnique.mockResolvedValue(mockTenant);

      const result = await controller.getTenantSettings('tenant-123');

      expect(result).toEqual({
        tenantId: 'tenant-123',
        settings: mockTenant.settings,
        updatedAt: mockTenant.updated_at,
      });
      expect(mockPrismaService.tenants.findUnique).toHaveBeenCalledWith({
        where: { id: 'tenant-123' },
        select: { id: true, settings: true, updated_at: true },
      });
    });

    it('should return default settings when tenant has no settings', async () => {
      const tenantWithoutSettings = {
        id: 'tenant-123',
        settings: null,
        updated_at: new Date('2023-12-01T10:30:00Z'),
      };

      mockPrismaService.tenants.findUnique.mockResolvedValue(tenantWithoutSettings);

      const result = await controller.getTenantSettings('tenant-123');

      expect(result.settings).toHaveProperty('defaultAiModel', 'gpt-3.5-turbo');
      expect(result.settings).toHaveProperty('notificationSettings');
      expect(result.settings).toHaveProperty('aiUsageSettings');
      expect(result.settings).toHaveProperty('projectSettings');
      expect(result.settings).toHaveProperty('securitySettings');
      expect(result.settings).toHaveProperty('integrationSettings');
    });

    it('should throw NotFoundException when tenant does not exist', async () => {
      mockPrismaService.tenants.findUnique.mockResolvedValue(null);

      await expect(controller.getTenantSettings('non-existent')).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateTenantSettings', () => {
    it('should update tenant settings successfully', async () => {
      const updateDto: UpdateTenantSettingsDto = {
        defaultAiModel: 'gpt-4-turbo' as any,
        notificationSettings: {
          methods: ['email', 'line'] as any,
          emailNotifications: true,
          lineNotifications: true,
          inAppNotifications: true,
          criticalAlertsOnly: false,
        },
      };

      const existingTenant = {
        id: 'tenant-123',
        settings: {
          defaultAiModel: 'gpt-3.5-turbo',
          projectSettings: {
            autoCreateMilestones: true,
          },
        },
      };

      const updatedTenant = {
        id: 'tenant-123',
        settings: {
          defaultAiModel: 'gpt-4-turbo',
          projectSettings: {
            autoCreateMilestones: true,
          },
          notificationSettings: updateDto.notificationSettings,
        },
        updated_at: new Date('2023-12-01T11:00:00Z'),
      };

      mockPrismaService.tenants.findUnique.mockResolvedValue(existingTenant);
      mockPrismaService.tenants.update.mockResolvedValue(updatedTenant);

      const result = await controller.updateTenantSettings('tenant-123', updateDto);

      expect(result).toEqual({
        tenantId: 'tenant-123',
        settings: updatedTenant.settings,
        updatedAt: updatedTenant.updated_at,
      });

      expect(mockPrismaService.tenants.update).toHaveBeenCalledWith({
        where: { id: 'tenant-123' },
        data: {
          settings: {
            defaultAiModel: 'gpt-4-turbo',
            projectSettings: {
              autoCreateMilestones: true,
            },
            notificationSettings: updateDto.notificationSettings,
          },
        },
        select: { id: true, settings: true, updated_at: true },
      });
    });

    it('should throw NotFoundException when tenant does not exist', async () => {
      const updateDto: UpdateTenantSettingsDto = {
        defaultAiModel: 'gpt-4' as any,
      };

      mockPrismaService.tenants.findUnique.mockResolvedValue(null);

      await expect(controller.updateTenantSettings('non-existent', updateDto)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should handle database errors gracefully', async () => {
      const updateDto: UpdateTenantSettingsDto = {
        defaultAiModel: 'gpt-4' as any,
      };

      mockPrismaService.tenants.findUnique.mockResolvedValue(mockTenant);
      mockPrismaService.tenants.update.mockRejectedValue(new Error('Database error'));

      await expect(controller.updateTenantSettings('tenant-123', updateDto)).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe('resetTenantSettings', () => {
    it('should reset tenant settings to default values', async () => {
      const resetTenant = {
        id: 'tenant-123',
        settings: {
          defaultAiModel: 'gpt-3.5-turbo',
          notificationSettings: {
            methods: ['email', 'in-app'],
            emailNotifications: true,
            lineNotifications: false,
            inAppNotifications: true,
            criticalAlertsOnly: false,
          },
          aiUsageSettings: {
            maxTokensPerRequest: 4000,
            enableAutoRetry: true,
            retryAttempts: 3,
            enableFallbackModel: true,
            fallbackModel: 'gpt-3.5-turbo',
          },
          projectSettings: {
            defaultProjectTemplate: 'construction',
            autoCreateMilestones: true,
            requireApprovalForTasks: false,
            enableProgressTracking: true,
          },
          securitySettings: {
            sessionTimeout: 3600,
            requireMfa: false,
            allowedIpRanges: [],
            dataRetentionDays: 365,
          },
          integrationSettings: {
            enableLineIntegration: false,
            enableEmailIntegration: true,
            webhookUrl: null,
            customIntegrations: {},
          },
          customSettings: {},
        },
        updated_at: new Date('2023-12-01T12:00:00Z'),
      };

      mockPrismaService.tenants.update.mockResolvedValue(resetTenant);

      const result = await controller.resetTenantSettings('tenant-123');

      expect(result).toEqual({
        tenantId: 'tenant-123',
        settings: resetTenant.settings,
        updatedAt: resetTenant.updated_at,
      });

      expect(mockPrismaService.tenants.update).toHaveBeenCalledWith({
        where: { id: 'tenant-123' },
        data: { settings: expect.objectContaining({ defaultAiModel: 'gpt-3.5-turbo' }) },
        select: { id: true, settings: true, updated_at: true },
      });
    });

    it('should throw NotFoundException when tenant does not exist', async () => {
      const prismaError = new Error('Record not found');
      (prismaError as any).code = 'P2025';
      mockPrismaService.tenants.update.mockRejectedValue(prismaError);

      await expect(controller.resetTenantSettings('non-existent')).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});

describe('TenantsService - Settings Management Integration', () => {
  let service: TenantsService;
  let prismaService: PrismaService;

  const mockPrismaService = {
    tenants: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TenantsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<TenantsService>(TenantsService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Settings validation and merging', () => {
    it('should merge partial settings updates correctly', async () => {
      const existingSettings = {
        defaultAiModel: 'gpt-3.5-turbo',
        notificationSettings: {
          methods: ['email'],
          emailNotifications: true,
          lineNotifications: false,
          inAppNotifications: true,
          criticalAlertsOnly: false,
        },
        projectSettings: {
          defaultProjectTemplate: 'construction',
          autoCreateMilestones: true,
        },
      };

      const updateData = {
        defaultAiModel: 'gpt-4',
        notificationSettings: {
          methods: ['email', 'line'],
          lineNotifications: true,
        },
      };

      const existingTenant = {
        id: 'tenant-123',
        settings: existingSettings,
      };

      const expectedMergedSettings = {
        defaultAiModel: 'gpt-4',
        notificationSettings: {
          methods: ['email', 'line'],
          lineNotifications: true,
        },
        projectSettings: {
          defaultProjectTemplate: 'construction',
          autoCreateMilestones: true,
        },
      };

      mockPrismaService.tenants.findUnique.mockResolvedValue(existingTenant);
      mockPrismaService.tenants.update.mockResolvedValue({
        id: 'tenant-123',
        settings: expectedMergedSettings,
        updated_at: new Date(),
      });

      await service.updateTenantSettings('tenant-123', updateData);

      expect(mockPrismaService.tenants.update).toHaveBeenCalledWith({
        where: { id: 'tenant-123' },
        data: { settings: expectedMergedSettings },
        select: { id: true, settings: true, updated_at: true },
      });
    });
  });
});
