import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { LlmService } from './services/llm.service';
import { LangChainLlmService } from './services/langchain-llm.service';
import { EmbeddingService } from './services/embedding.service';
import { PrismaModule } from '@/modules/core/prisma/prisma.module';
import { AiModelsService } from '../models/configuration/models/ai-models.service';
import { AiKeysService } from '../models/configuration/keys/ai-keys.service';
import { AiPriceCrawlerService } from '../models/configuration/pricing/ai-price-crawler.service';
import { AiPricingService } from '../models/configuration/pricing/ai-pricing.service';
import { AiProviderFactory } from '../models/core/providers/factory';
import { AiErrorHandlerService } from '../models/core/monitoring/ai-error-handler.service';
import { AiMonitoringService } from '../models/core/monitoring/ai-monitoring.service';
import { AiIntegrationService } from '../models/core/monitoring/ai-integration.service';
import { EncryptionModule } from '@/modules/core/encryption/encryption.module';

@Module({
  imports: [PrismaModule, EncryptionModule, HttpModule, EventEmitterModule],
  providers: [
    LlmService,
    LangChainLlmService,
    EmbeddingService,
    AiModelsService,
    AiKeysService,
    AiPriceCrawlerService,
    AiPricingService,
    AiProviderFactory,
    AiErrorHandlerService,
    AiMonitoringService,
    AiIntegrationService,
  ],
  exports: [
    LlmService,
    LangChainLlmService,
    EmbeddingService,
    AiModelsService,
    AiKeysService,
    AiErrorHandlerService,
    AiMonitoringService,
    AiIntegrationService,
  ],
})
export class LlmModule {}
