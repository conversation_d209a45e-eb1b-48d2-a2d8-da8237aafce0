import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import OpenAI from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { AiKeysService } from '../../models/configuration/keys/ai-keys.service';
import { AiAgentProviderType } from '@prisma/client';

export interface EmbeddingOptions {
  providerType: AiAgentProviderType;
  model?: string;
  keyId: string;
  dimensions?: number;
}

export interface EmbeddingResponse {
  embedding: number[];
  usage: {
    tokens: number;
  };
}

/**
 * 嵌入生成服務
 *
 * 提供統一的介面來生成文本嵌入向量，支援多個 AI 供應商：
 * - OpenAI (text-embedding-ada-002, text-embedding-3-small, text-embedding-3-large)
 * - Google Gemini (text-embedding-004)
 * - 未來可擴展支援其他供應商
 */
@Injectable()
export class EmbeddingService {
  private readonly logger = new Logger(EmbeddingService.name);

  // 預設嵌入模型配置
  private readonly defaultModels = {
    [AiAgentProviderType.OPENAI]: 'text-embedding-3-small',
    [AiAgentProviderType.GOOGLE_GEMINI]: 'text-embedding-004',
    [AiAgentProviderType.ANTHROPIC]: null, // Anthropic 目前不提供嵌入服務
    [AiAgentProviderType.CLAUDE]: null,
  };

  constructor(private readonly aiKeysService: AiKeysService) {}

  /**
   * 生成文本嵌入向量
   *
   * @param text 需要生成嵌入的文本
   * @param options 嵌入選項配置
   * @returns 嵌入向量和使用統計
   */
  async generateEmbedding(text: string, options: EmbeddingOptions): Promise<EmbeddingResponse> {
    // 驗證輸入
    this.validateInput(text, options);

    // 獲取解密的 API 金鑰
    const apiKey = await this.aiKeysService.getDecryptedKey(options.keyId);
    if (!apiKey) {
      throw new BadRequestException(`API key not found for keyId: ${options.keyId}`);
    }

    // 選擇模型
    const model = options.model || this.defaultModels[options.providerType];
    if (!model) {
      throw new BadRequestException(
        `No embedding model available for provider: ${options.providerType}`,
      );
    }

    try {
      switch (options.providerType) {
        case AiAgentProviderType.OPENAI:
          return await this.generateOpenAIEmbedding(text, model, apiKey, options.dimensions);
        case AiAgentProviderType.GOOGLE_GEMINI:
          return await this.generateGeminiEmbedding(text, model, apiKey);
        default:
          throw new BadRequestException(`Unsupported embedding provider: ${options.providerType}`);
      }
    } catch (error) {
      this.logger.error(`Failed to generate embedding with ${options.providerType}:`, error);
      throw new BadRequestException(`Embedding generation failed: ${error.message}`);
    }
  }

  /**
   * 批量生成嵌入向量
   *
   * @param texts 文本陣列
   * @param options 嵌入選項配置
   * @returns 嵌入向量陣列和使用統計
   */
  async generateBatchEmbeddings(
    texts: string[],
    options: EmbeddingOptions,
  ): Promise<{ embeddings: number[][]; usage: { tokens: number } }> {
    if (texts.length === 0) {
      return { embeddings: [], usage: { tokens: 0 } };
    }

    // 對於大批量，分批處理
    const batchSize = 100; // 調整為適當的批量大小
    const embeddings: number[][] = [];
    let totalTokens = 0;

    for (let i = 0; i < texts.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize);
      const batchPromises = batch.map((text) => this.generateEmbedding(text, options));

      const batchResults = await Promise.all(batchPromises);

      embeddings.push(...batchResults.map((result) => result.embedding));
      totalTokens += batchResults.reduce((sum, result) => sum + result.usage.tokens, 0);
    }

    return {
      embeddings,
      usage: { tokens: totalTokens },
    };
  }

  /**
   * 獲取指定供應商的可用嵌入模型
   */
  getAvailableModels(providerType: AiAgentProviderType): string[] {
    switch (providerType) {
      case AiAgentProviderType.OPENAI:
        return ['text-embedding-3-small', 'text-embedding-3-large', 'text-embedding-ada-002'];
      case AiAgentProviderType.GOOGLE_GEMINI:
        return ['text-embedding-004', 'embedding-001'];
      default:
        return [];
    }
  }

  /**
   * 獲取嵌入向量的維度
   */
  getEmbeddingDimension(providerType: AiAgentProviderType, model: string): number {
    if (providerType === AiAgentProviderType.OPENAI) {
      switch (model) {
        case 'text-embedding-3-small':
          return 1536;
        case 'text-embedding-3-large':
          return 3072;
        case 'text-embedding-ada-002':
          return 1536;
        default:
          return 1536;
      }
    }

    if (providerType === AiAgentProviderType.GOOGLE_GEMINI) {
      return 768; // Gemini 嵌入模型通常是 768 維
    }

    return 1536; // 預設維度
  }

  // --- 私有方法 ---

  private validateInput(text: string, options: EmbeddingOptions): void {
    if (!text || text.trim().length === 0) {
      throw new BadRequestException('Text cannot be empty');
    }

    if (text.length > 8000) {
      throw new BadRequestException('Text is too long. Maximum length is 8000 characters');
    }

    if (!options.keyId) {
      throw new BadRequestException('Key ID is required');
    }

    if (!options.providerType) {
      throw new BadRequestException('Provider type is required');
    }
  }

  private async generateOpenAIEmbedding(
    text: string,
    model: string,
    apiKey: string,
    dimensions?: number,
  ): Promise<EmbeddingResponse> {
    const openai = new OpenAI({ apiKey });

    const params: any = {
      model,
      input: text,
    };

    // 只有支援的模型才能設置 dimensions
    if (dimensions && (model === 'text-embedding-3-small' || model === 'text-embedding-3-large')) {
      params.dimensions = dimensions;
    }

    const response = await openai.embeddings.create(params);

    return {
      embedding: response.data[0].embedding,
      usage: {
        tokens: response.usage.total_tokens,
      },
    };
  }

  private async generateGeminiEmbedding(
    text: string,
    model: string,
    apiKey: string,
  ): Promise<EmbeddingResponse> {
    const gemini = new GoogleGenerativeAI(apiKey);
    const embeddingModel = gemini.getGenerativeModel({ model });

    const result = await embeddingModel.embedContent(text);

    return {
      embedding: result.embedding.values,
      usage: {
        tokens: Math.ceil(text.length / 4), // 估算 token 數量
      },
    };
  }
}
