import { Injectable, Logger } from '@nestjs/common';
import { ChatOpenAI } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { LlmService } from './llm.service';
import { AiModelsService } from '../../models/configuration/models/ai-models.service';
import { AiKeysService } from '../../models/configuration/keys/ai-keys.service';
import { AiAgentProviderType } from '@prisma/client';

/**
 * Service that creates LangChain-compatible LLM instances using our provider abstraction
 */
@Injectable()
export class LangChainLlmService {
  private readonly logger = new Logger(LangChainLlmService.name);

  constructor(
    private readonly llmService: LlmService,
    private readonly aiModelsService: AiModelsService,
    private readonly aiKeysService: AiKeysService,
  ) {}

  /**
   * Create a LangChain LLM instance based on model configuration
   */
  async createLangChainLlm(
    tenantId: string,
    modelId?: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
    },
  ): Promise<ChatOpenAI | ChatAnthropic | ChatGoogleGenerativeAI> {
    this.logger.debug(`Creating LangChain LLM for tenant: ${tenantId}, model: ${modelId}`);

    try {
      // 1. Get model configuration
      let modelConfig;
      if (modelId) {
        try {
          modelConfig = await this.aiModelsService.findOne(modelId);
        } catch (error) {
          this.logger.warn(`Model ID ${modelId} not found, using default model`);
        }
      }

      // If no model specified or not found, use default
      if (!modelConfig) {
        const allModels = await this.aiModelsService.findAll();
        const enabledModels = allModels.filter((model) => model.is_enabled);

        if (enabledModels.length === 0) {
          throw new Error('No enabled AI models available');
        }

        modelConfig = enabledModels[0];
        this.logger.debug(`Using default model: ${modelConfig.provider}/${modelConfig.model_name}`);
      }

      // 2. Get API key
      const tenantKeys = await this.aiKeysService.findAll(tenantId);
      const matchingKey = tenantKeys.find(
        (key) => key.provider === modelConfig.provider && key.is_enabled,
      );

      if (!matchingKey) {
        throw new Error(
          `No API key found for provider ${modelConfig.provider} in tenant ${tenantId}`,
        );
      }

      const decryptedApiKey = await this.aiKeysService.getDecryptedKey(matchingKey.id);

      // 3. Create LangChain LLM instance
      const temperature = options?.temperature ?? 0.7;
      const maxTokens = options?.maxTokens ?? 2000;

      switch (modelConfig.provider) {
        case AiAgentProviderType.OPENAI:
          return new ChatOpenAI({
            apiKey: decryptedApiKey,
            modelName: modelConfig.model_name,
            temperature,
            maxTokens,
            ...(matchingKey.api_url && { configuration: { baseURL: matchingKey.api_url } }),
          });

        case AiAgentProviderType.ANTHROPIC:
        case AiAgentProviderType.CLAUDE:
          return new ChatAnthropic({
            apiKey: decryptedApiKey,
            model: modelConfig.model_name,
            temperature,
            maxTokens,
            ...(matchingKey.api_url && { baseURL: matchingKey.api_url }),
          });

        case AiAgentProviderType.GOOGLE_GEMINI:
        case AiAgentProviderType.GEMINI:
          return new ChatGoogleGenerativeAI({
            apiKey: decryptedApiKey,
            model: modelConfig.model_name,
            temperature,
            maxOutputTokens: maxTokens,
          });

        case AiAgentProviderType.OPENAI_COMPATIBLE:
          if (!matchingKey.api_url) {
            throw new Error('OpenAI compatible model requires API URL');
          }
          return new ChatOpenAI({
            apiKey: decryptedApiKey,
            modelName: modelConfig.model_name,
            temperature,
            maxTokens,
            configuration: { baseURL: matchingKey.api_url },
          });

        default:
          throw new Error(`Unsupported provider: ${modelConfig.provider}`);
      }
    } catch (error) {
      this.logger.error(`Failed to create LangChain LLM: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Test if we can create an LLM for the given configuration
   */
  async testLlmCreation(tenantId: string, modelId?: string): Promise<boolean> {
    try {
      const llm = await this.createLangChainLlm(tenantId, modelId);
      return !!llm;
    } catch (error) {
      this.logger.warn(`LLM creation test failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Get model information without creating the LLM instance
   */
  async getModelInfo(tenantId: string, modelId?: string) {
    let modelConfig;
    if (modelId) {
      modelConfig = await this.aiModelsService.findOne(modelId);
    } else {
      const allModels = await this.aiModelsService.findAll();
      const enabledModels = allModels.filter((model) => model.is_enabled);
      modelConfig = enabledModels[0];
    }

    const tenantKeys = await this.aiKeysService.findAll(tenantId);
    const matchingKey = tenantKeys.find(
      (key) => key.provider === modelConfig.provider && key.is_enabled,
    );

    return {
      model: modelConfig,
      hasApiKey: !!matchingKey,
      provider: modelConfig.provider,
      modelName: modelConfig.model_name,
    };
  }
}
