import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { GoogleGenerativeAI, Part } from '@google/generative-ai';
import {
  ILlmService,
  LlmExecuteOptions,
  LlmGetModelsOptions,
  AiMessage,
  AiResponse,
} from '../interfaces/llm-service.interface';
import { AiAgentProviderType } from '@prisma/client';
import { AiErrorMapper } from '../../models/core/exceptions/ai-service.exceptions';
import { AiKeysService } from '../../models/configuration/keys/ai-keys.service';

@Injectable()
export class LlmService implements ILlmService {
  private readonly logger = new Logger(LlmService.name);

  constructor(private readonly aiKeysService: AiKeysService) {}

  async execute(messages: AiMessage[], options: LlmExecuteOptions): Promise<AiResponse> {
    const apiKey = await this.aiKeysService.getDecryptedKey(options.keyId);
    if (!apiKey) {
      throw new BadRequestException(`API key not found for keyId: ${options.keyId}`);
    }

    try {
      switch (options.providerType) {
        case AiAgentProviderType.OPENAI:
          return await this.executeOpenAI(messages, { ...options, apiKey });
        case AiAgentProviderType.ANTHROPIC:
          return await this.executeClaude(messages, { ...options, apiKey });
        case AiAgentProviderType.GOOGLE_GEMINI:
          return await this.executeGemini(messages, { ...options, apiKey });
        default:
          throw new Error(`Unsupported provider type: ${options.providerType}`);
      }
    } catch (error) {
      this.logger.error(`LLM execution failed for provider ${options.providerType}:`, error);
      throw AiErrorMapper.mapError(error, options.providerType.toString());
    }
  }

  async getAvailableModels(options: LlmGetModelsOptions): Promise<string[]> {
    const { provider, apiKey, apiUrl } = options;

    try {
      if (provider === 'google-gemini') {
        this.logger.warn(
          'Google Gemini SDK does not support listing models. Returning empty list.',
        );
        return [];
      }

      const openai = new OpenAI({
        apiKey,
        baseURL: apiUrl,
      });
      const models = await openai.models.list();
      return models.data.map((model) => model.id);
    } catch (error) {
      this.logger.error(`Failed to get available models for provider ${provider}:`, error);
      throw AiErrorMapper.mapError(error, provider);
    }
  }

  // --- Private Execution Methods ---

  private async executeOpenAI(
    messages: AiMessage[],
    options: LlmExecuteOptions & { apiKey: string },
  ): Promise<AiResponse> {
    const { model, apiKey, temperature, maxTokens, responseFormat, apiUrl } = options;
    const openai = new OpenAI({ apiKey, baseURL: apiUrl });
    const processedMessages = this.convertToOpenAIMessages(messages);

    const completion = await openai.chat.completions.create({
      model,
      messages: processedMessages,
      temperature: temperature ?? 0.7,
      max_tokens: maxTokens ?? 2048,
      response_format: responseFormat ? { type: responseFormat } : undefined,
    });

    return {
      content: completion.choices[0].message.content,
      usage: {
        inputTokens: completion.usage?.prompt_tokens ?? 0,
        outputTokens: completion.usage?.completion_tokens ?? 0,
      },
    };
  }

  private convertToOpenAIMessages(
    messages: AiMessage[],
  ): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
    return messages.map((msg) => {
      // OpenAI's SDK has distinct types for message content parts.
      // We need to ensure our generic AiMessage format is correctly mapped.
      if (typeof msg.content === 'string') {
        return { role: msg.role, content: msg.content };
      }

      // Handle complex content (e.g., for vision)
      const contentParts: OpenAI.Chat.Completions.ChatCompletionContentPart[] = msg.content.map(
        (part) => {
          if (part.type === 'text') {
            return { type: 'text', text: part.text };
          }
          if (part.type === 'image_url') {
            return { type: 'image_url', image_url: { url: part.image_url.url } };
          }
          // This should not happen if AiMessage is constructed correctly
          throw new BadRequestException(
            `Unsupported OpenAI content part type: ${(part as any).type}`,
          );
        },
      );

      return { role: msg.role as 'user', content: contentParts };
    });
  }

  private async executeClaude(
    messages: AiMessage[],
    options: LlmExecuteOptions & { apiKey: string },
  ): Promise<AiResponse> {
    const { model, apiKey, temperature, maxTokens, systemPrompt: optionsSystemPrompt } = options;
    const anthropic = new Anthropic({ apiKey });
    const { systemPrompt: messagesSystemPrompt, userMessages } =
      this.separateSystemPrompt(messages);
    const processedMessages = this.convertToClaudeMessages(userMessages);

    const systemPrompt = optionsSystemPrompt || messagesSystemPrompt;

    const response = await anthropic.messages.create({
      model,
      system: systemPrompt,
      messages: processedMessages,
      temperature: temperature ?? 0.7,
      max_tokens: maxTokens ?? 4096,
    });

    return {
      content: this.extractResponseContent(response.content),
      usage: {
        inputTokens: response.usage.input_tokens,
        outputTokens: response.usage.output_tokens,
      },
    };
  }

  private separateSystemPrompt(messages: AiMessage[]): {
    systemPrompt?: string;
    userMessages: AiMessage[];
  } {
    if (messages.length > 0 && messages[0].role === 'system') {
      return {
        systemPrompt: messages[0].content as string,
        userMessages: messages.slice(1),
      };
    }
    return { userMessages: messages };
  }

  private convertToClaudeMessages(messages: AiMessage[]): Anthropic.Messages.MessageParam[] {
    return messages.map((msg) => {
      if (typeof msg.content === 'string') {
        return {
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
        };
      }

      // Handle complex content for Claude
      const contentBlocks: Anthropic.ContentBlockParam[] = msg.content.map((part) => {
        if (part.type === 'text') {
          return { type: 'text', text: part.text };
        }
        if (part.type === 'image_url') {
          const match = part.image_url.url.match(/data:(image\/\w+);base64,(.*)/);
          if (match) {
            return {
              type: 'image',
              source: {
                type: 'base64',
                media_type: match[1] as `image/${'jpeg' | 'png' | 'gif' | 'webp'}`,
                data: match[2],
              },
            };
          }
          throw new BadRequestException(
            'Invalid image_url format for Claude. Expected base64 data URL.',
          );
        }
        throw new BadRequestException(
          `Unsupported Claude content part type: ${(part as any).type}`,
        );
      });

      return {
        role: msg.role as 'user' | 'assistant',
        content: contentBlocks,
      };
    });
  }

  private extractResponseContent(content: Anthropic.Messages.ContentBlock[]): string {
    return content.map((block) => ('text' in block ? block.text : '')).join('');
  }

  private async executeGemini(
    messages: AiMessage[],
    options: LlmExecuteOptions & { apiKey: string },
  ): Promise<AiResponse> {
    const { model, apiKey, temperature, maxTokens } = options;
    const gemini = new GoogleGenerativeAI(apiKey);
    const geminiModel = gemini.getGenerativeModel({
      model,
      generationConfig: {
        temperature: temperature ?? 0.7,
        maxOutputTokens: maxTokens ?? 8192,
      },
    });

    const { systemInstruction, history, lastMessageContent } = this.prepareGeminiMessages(messages);

    const chat = geminiModel.startChat({
      history,
      systemInstruction: systemInstruction
        ? { role: 'system', parts: [{ text: systemInstruction }] }
        : undefined,
    });

    const result = await chat.sendMessageStream(lastMessageContent);

    // Iterating through the stream to accumulate the full response text
    let responseText = '';
    for await (const chunk of result.stream) {
      responseText += chunk.text();
    }

    // To get input token count, we need to construct the full `contents` array
    const historyContents = history.map((h) => ({ role: h.role, parts: h.parts }));
    const lastMessageForTokenCount = { role: 'user', parts: lastMessageContent };
    const fullContentsForTokenCount = [...historyContents, lastMessageForTokenCount];

    // Calculate input tokens
    const inputUsage = await geminiModel.countTokens({ contents: fullContentsForTokenCount });

    // Calculate output tokens by counting the response content
    let outputTokens = 0;
    try {
      const outputUsage = await geminiModel.countTokens(responseText);
      outputTokens = outputUsage.totalTokens;
    } catch (error) {
      // Fallback: estimate output tokens based on response text length
      // Rough approximation: 1 token ≈ 3-4 characters for English text
      outputTokens = Math.ceil(responseText.length / 3.5);
      this.logger.warn(
        `Failed to count output tokens for Gemini response, using estimated count: ${outputTokens}. Error: ${error.message}`,
      );
    }

    return {
      content: responseText,
      usage: {
        inputTokens: inputUsage.totalTokens,
        outputTokens: outputTokens,
      },
    };
  }

  private prepareGeminiMessages(messages: AiMessage[]): {
    systemInstruction: string | undefined;
    history: Array<{ role: 'user' | 'model'; parts: Part[] }>;
    lastMessageContent: Part[];
  } {
    let systemInstruction: string | undefined;
    const history: Array<{ role: 'user' | 'model'; parts: Part[] }> = [];
    let lastMessageContent: Part[] = [];

    const allMessages = [...messages]; // Create a mutable copy

    if (allMessages.length > 0 && allMessages[0].role === 'system') {
      systemInstruction = allMessages.shift()?.content as string;
    }

    // Process all but the last message for history
    for (const msg of allMessages.slice(0, -1)) {
      history.push({
        role: this.convertToGeminiRole(msg.role),
        parts: this.convertToGeminiParts(msg.content),
      });
    }

    // Process the last message for the final sendMessage call
    if (allMessages.length > 0) {
      const lastMsg = allMessages[allMessages.length - 1];
      lastMessageContent = this.convertToGeminiParts(lastMsg.content);
    }

    return { systemInstruction, history, lastMessageContent };
  }

  private convertToGeminiRole(role: 'user' | 'assistant' | 'system'): 'user' | 'model' {
    return role === 'assistant' ? 'model' : 'user';
  }

  private convertToGeminiParts(
    content:
      | string
      | Array<{ type: 'text'; text: string } | { type: 'image_url'; image_url: { url: string } }>,
  ): Part[] {
    if (typeof content === 'string') {
      return [{ text: content }];
    }

    return content.map((part) => {
      if (part.type === 'text') {
        return { text: part.text };
      }
      if (part.type === 'image_url') {
        // Assuming base64 data URL
        const match = part.image_url.url.match(/data:(image\/\w+);base64,(.*)/);
        if (match) {
          return {
            inlineData: {
              mimeType: match[1],
              data: match[2],
            },
          };
        }
      }
      throw new BadRequestException('Unsupported Gemini content part format.');
    });
  }
}
