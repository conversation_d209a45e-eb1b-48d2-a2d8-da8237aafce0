import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateModelDto {
  @ApiProperty({ description: 'AI Provider' })
  @IsString()
  @IsNotEmpty()
  provider: string;

  @ApiProperty({ description: 'API 使用的模型名稱' })
  @IsString()
  @IsNotEmpty()
  model_name: string;

  @ApiProperty({ description: '顯示名稱' })
  @IsString()
  @IsNotEmpty()
  display_name: string;

  @ApiPropertyOptional({ description: '是否啟用' })
  @IsBoolean()
  @IsOptional()
  is_enabled?: boolean;

  @ApiPropertyOptional({ description: '輸入 tokens 每 1k 的價格' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  input_price_per_1k_tokens?: number;

  @ApiPropertyOptional({ description: '輸出 tokens 每 1k 的價格' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  output_price_per_1k_tokens?: number;

  @ApiPropertyOptional({ description: '貨幣' })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiPropertyOptional({ description: 'Context Window 大小 (tokens)' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  context_window_tokens?: number;

  @ApiPropertyOptional({ description: '備註' })
  @IsString()
  @IsOptional()
  notes?: string;
}

export class UpdateModelDto {
  @ApiPropertyOptional({ description: '顯示名稱' })
  @IsString()
  @IsOptional()
  display_name?: string;

  @ApiPropertyOptional({ description: '是否啟用' })
  @IsBoolean()
  @IsOptional()
  is_enabled?: boolean;

  @ApiPropertyOptional({ description: '輸入 tokens 每 1k 的價格' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  input_price_per_1k_tokens?: number;

  @ApiPropertyOptional({ description: '輸出 tokens 每 1k 的價格' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  output_price_per_1k_tokens?: number;

  @ApiPropertyOptional({ description: '貨幣' })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiPropertyOptional({ description: 'Context Window 大小 (tokens)' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  context_window_tokens?: number;

  @ApiPropertyOptional({ description: '備註' })
  @IsString()
  @IsOptional()
  notes?: string;
}

export class ModelResponseDto {
  @ApiProperty({ description: 'Model ID' })
  id: string;

  @ApiProperty({ description: 'AI Provider' })
  provider: string;

  @ApiProperty({ description: 'API 使用的模型名稱' })
  model_name: string;

  @ApiProperty({ description: '顯示名稱' })
  display_name: string;

  @ApiProperty({ description: '是否啟用' })
  is_enabled: boolean;

  @ApiProperty({ description: '輸入 tokens 每 1k 的價格' })
  input_price_per_1k_tokens: number;

  @ApiProperty({ description: '輸出 tokens 每 1k 的價格' })
  output_price_per_1k_tokens: number;

  @ApiProperty({ description: '貨幣' })
  currency: string;

  @ApiProperty({ description: '價格最後更新時間' })
  price_last_updated_at: Date;

  @ApiPropertyOptional({ description: 'Context Window 大小 (tokens)' })
  context_window_tokens?: number;

  @ApiPropertyOptional({ description: '備註' })
  notes?: string;

  @ApiProperty({ description: '建立時間' })
  created_at: Date;

  @ApiProperty({ description: '更新時間' })
  updated_at: Date;
}

export class FetchPricingDto {
  @ApiProperty({ description: '價格來源網址' })
  @IsString()
  @IsNotEmpty()
  url: string;

  @ApiProperty({ description: '模型名稱 (API 用的 model name，如 gpt-4-turbo)' })
  @IsString()
  @IsNotEmpty()
  model_name: string;
}

export class FetchPricingResponseDto {
  @ApiProperty({ description: '輸入價格，每 1k tokens (USD)' })
  input_price_per_1k_tokens: number;

  @ApiProperty({ description: '輸出價格，每 1k tokens (USD)' })
  output_price_per_1k_tokens: number;
}
