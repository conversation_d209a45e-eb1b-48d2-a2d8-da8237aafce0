import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import {
  CreatePriceSourceDto,
  UpdatePriceSourceDto,
  PriceSourceResponseDto,
} from './dto/price-source.dto';
import { v4 as uuidv4 } from 'uuid';
import { AiPriceCrawlerService } from './ai-price-crawler.service';

@Injectable()
export class PriceSourcesService {
  private readonly logger = new Logger(PriceSourcesService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly priceCrawlerService: AiPriceCrawlerService,
  ) {}

  /**
   * 取得所有價格來源
   */
  async findAll(): Promise<PriceSourceResponseDto[]> {
    try {
      const priceSources = await this.prisma.ai_price_sources.findMany({
        orderBy: [{ provider: 'asc' }, { created_at: 'desc' }],
      });

      return priceSources.map((source) => ({
        id: source.id,
        provider: source.provider,
        url: source.url,
        created_at: source.created_at,
        updated_at: source.updated_at,
      }));
    } catch (error) {
      this.logger.error(`取得價格來源失敗: ${error.message}`, error.stack);
      throw new BadRequestException('取得價格來源失敗');
    }
  }

  /**
   * 根據 ID 取得單一價格來源
   */
  async findOne(id: string): Promise<PriceSourceResponseDto> {
    try {
      const priceSource = await this.prisma.ai_price_sources.findUnique({
        where: { id },
      });

      if (!priceSource) {
        throw new NotFoundException(`找不到 ID 為 ${id} 的價格來源`);
      }

      return {
        id: priceSource.id,
        provider: priceSource.provider,
        url: priceSource.url,
        created_at: priceSource.created_at,
        updated_at: priceSource.updated_at,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`取得價格來源失敗: ${error.message}`, error.stack);
      throw new BadRequestException('取得價格來源失敗');
    }
  }

  /**
   * 建立新的價格來源
   */
  async create(createDto: CreatePriceSourceDto): Promise<PriceSourceResponseDto> {
    try {
      // 檢查是否已存在相同的 provider
      const existingSource = await this.prisma.ai_price_sources.findFirst({
        where: { provider: createDto.provider },
      });

      if (existingSource) {
        throw new BadRequestException(`供應商 ${createDto.provider} 的價格來源已存在`);
      }

      // 驗證 URL 格式
      await this.validatePriceSourceUrl(createDto.url);

      const now = new Date();
      const priceSource = await this.prisma.ai_price_sources.create({
        data: {
          id: uuidv4(),
          provider: createDto.provider,
          url: createDto.url,
          created_at: now,
          updated_at: now,
        },
      });

      this.logger.log(`成功建立價格來源: ${createDto.provider} - ${createDto.url}`);

      return {
        id: priceSource.id,
        provider: priceSource.provider,
        url: priceSource.url,
        created_at: priceSource.created_at,
        updated_at: priceSource.updated_at,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`建立價格來源失敗: ${error.message}`, error.stack);
      throw new BadRequestException('建立價格來源失敗');
    }
  }

  /**
   * 更新價格來源
   */
  async update(id: string, updateDto: UpdatePriceSourceDto): Promise<PriceSourceResponseDto> {
    try {
      // 檢查價格來源是否存在
      const existingSource = await this.prisma.ai_price_sources.findUnique({
        where: { id },
      });

      if (!existingSource) {
        throw new NotFoundException(`找不到 ID 為 ${id} 的價格來源`);
      }

      // 如果更新 provider，檢查是否與其他記錄衝突
      if (updateDto.provider && updateDto.provider !== existingSource.provider) {
        const conflictingSource = await this.prisma.ai_price_sources.findFirst({
          where: {
            provider: updateDto.provider,
            id: { not: id },
          },
        });

        if (conflictingSource) {
          throw new BadRequestException(`供應商 ${updateDto.provider} 的價格來源已存在`);
        }
      }

      // 驗證新的 URL（如果有提供）
      if (updateDto.url) {
        await this.validatePriceSourceUrl(updateDto.url);
      }

      const updateData: any = { updated_at: new Date() };
      if (updateDto.provider) updateData.provider = updateDto.provider;
      if (updateDto.url) updateData.url = updateDto.url;

      const updatedSource = await this.prisma.ai_price_sources.update({
        where: { id },
        data: updateData,
      });

      this.logger.log(`成功更新價格來源: ${id}`);

      return {
        id: updatedSource.id,
        provider: updatedSource.provider,
        url: updatedSource.url,
        created_at: updatedSource.created_at,
        updated_at: updatedSource.updated_at,
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`更新價格來源失敗: ${error.message}`, error.stack);
      throw new BadRequestException('更新價格來源失敗');
    }
  }

  /**
   * 刪除價格來源
   */
  async remove(id: string): Promise<{ success: boolean; message: string }> {
    try {
      const existingSource = await this.prisma.ai_price_sources.findUnique({
        where: { id },
      });

      if (!existingSource) {
        throw new NotFoundException(`找不到 ID 為 ${id} 的價格來源`);
      }

      await this.prisma.ai_price_sources.delete({
        where: { id },
      });

      this.logger.log(`成功刪除價格來源: ${id} (${existingSource.provider})`);

      return {
        success: true,
        message: `成功刪除 ${existingSource.provider} 的價格來源`,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`刪除價格來源失敗: ${error.message}`, error.stack);
      throw new BadRequestException('刪除價格來源失敗');
    }
  }

  /**
   * 根據供應商取得價格來源
   */
  async findByProvider(provider: string): Promise<PriceSourceResponseDto | null> {
    try {
      const priceSource = await this.prisma.ai_price_sources.findFirst({
        where: { provider },
      });

      if (!priceSource) {
        return null;
      }

      return {
        id: priceSource.id,
        provider: priceSource.provider,
        url: priceSource.url,
        created_at: priceSource.created_at,
        updated_at: priceSource.updated_at,
      };
    } catch (error) {
      this.logger.error(`根據供應商取得價格來源失敗: ${error.message}`, error.stack);
      throw new BadRequestException('取得價格來源失敗');
    }
  }

  /**
   * 測試價格來源 URL 的可用性
   */
  async testPriceSource(id: string): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      const priceSource = await this.findOne(id);

      // 使用價格爬取服務測試 URL
      const isValid = await this.validatePriceSourceUrl(priceSource.url);

      if (isValid) {
        return {
          success: true,
          message: `價格來源 ${priceSource.provider} 可正常訪問`,
          details: {
            provider: priceSource.provider,
            url: priceSource.url,
            tested_at: new Date(),
          },
        };
      } else {
        return {
          success: false,
          message: `價格來源 ${priceSource.provider} 無法訪問`,
          details: {
            provider: priceSource.provider,
            url: priceSource.url,
            tested_at: new Date(),
          },
        };
      }
    } catch (error) {
      this.logger.error(`測試價格來源失敗: ${error.message}`, error.stack);
      return {
        success: false,
        message: `測試失敗: ${error.message}`,
      };
    }
  }

  /**
   * 從所有價格來源同步價格資訊
   */
  async syncAllPrices(): Promise<{
    success: boolean;
    message: string;
    details: { source: string; result: string }[];
  }> {
    try {
      const priceSources = await this.findAll();
      const results: { source: string; result: string }[] = [];

      // 目前主要依賴 llmpricecheck.com，但保留擴展性
      try {
        const models = await this.priceCrawlerService.fetchAllModelPrices();
        results.push({
          source: 'llmpricecheck.com',
          result: `成功獲取 ${models.length} 個模型價格`,
        });
      } catch (error) {
        results.push({
          source: 'llmpricecheck.com',
          result: `獲取失敗: ${error.message}`,
        });
      }

      // 未來可以添加其他價格來源的同步邏輯
      for (const source of priceSources) {
        if (source.provider !== 'llmpricecheck') {
          results.push({
            source: `${source.provider} (${source.url})`,
            result: '暫不支援自動同步，需手動實現',
          });
        }
      }

      this.logger.log(`完成價格同步，處理了 ${results.length} 個來源`);

      return {
        success: true,
        message: `完成價格同步，處理了 ${results.length} 個來源`,
        details: results,
      };
    } catch (error) {
      this.logger.error(`同步價格失敗: ${error.message}`, error.stack);
      return {
        success: false,
        message: `同步失敗: ${error.message}`,
        details: [],
      };
    }
  }

  /**
   * 初始化預設價格來源
   */
  async initializeDefaultSources(): Promise<{
    success: boolean;
    message: string;
    created: number;
  }> {
    try {
      const defaultSources = [
        {
          provider: 'llmpricecheck',
          url: 'https://llmpricecheck.com',
        },
        {
          provider: 'openai',
          url: 'https://openai.com/pricing',
        },
        {
          provider: 'anthropic',
          url: 'https://docs.anthropic.com/claude/docs/models-overview',
        },
        {
          provider: 'google-gemini',
          url: 'https://ai.google.dev/pricing',
        },
      ];

      let createdCount = 0;

      for (const source of defaultSources) {
        const existing = await this.prisma.ai_price_sources.findFirst({
          where: { provider: source.provider },
        });

        if (!existing) {
          await this.create(source);
          createdCount++;
          this.logger.log(`建立預設價格來源: ${source.provider}`);
        }
      }

      return {
        success: true,
        message: `成功初始化，建立了 ${createdCount} 個預設價格來源`,
        created: createdCount,
      };
    } catch (error) {
      this.logger.error(`初始化預設價格來源失敗: ${error.message}`, error.stack);
      throw new BadRequestException('初始化預設價格來源失敗');
    }
  }

  /**
   * 驗證價格來源 URL 的有效性
   */
  private async validatePriceSourceUrl(url: string): Promise<boolean> {
    try {
      // 基本 URL 格式驗證
      new URL(url);

      // 嘗試訪問 URL（設定超時）
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超時

      const response = await fetch(url, {
        method: 'HEAD',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // 檢查回應狀態
      if (response.ok) {
        this.logger.debug(`價格來源 URL 驗證成功: ${url}`);
        return true;
      } else {
        this.logger.warn(`價格來源 URL 回應異常: ${url}, 狀態: ${response.status}`);
        return false;
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        this.logger.warn(`價格來源 URL 驗證超時: ${url}`);
      } else {
        this.logger.warn(`價格來源 URL 驗證失敗: ${url}, 錯誤: ${error.message}`);
      }
      return false;
    }
  }
}
