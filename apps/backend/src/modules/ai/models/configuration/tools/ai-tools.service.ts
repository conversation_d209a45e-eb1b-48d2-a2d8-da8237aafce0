import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import {
  CreateAiToolDto,
  UpdateAiToolDto,
  AiToolQueryDto,
  AiToolResponseDto,
} from './dto/ai-tools.dto';
import { AiToolScope } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class AiToolsService {
  private readonly logger = new Logger(AiToolsService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 查詢所有工具（支援篩選）
   */
  async findAll(query: AiToolQueryDto): Promise<AiToolResponseDto[]> {
    const where: any = {};

    // 範疇篩選
    if (query.scope) {
      where.scope = query.scope;
    }

    // 租戶篩選
    if (query.tenantId) {
      where.tenant_id = query.tenantId;
    }

    // 工作空間篩選
    if (query.workspaceId) {
      where.workspace_id = query.workspaceId;
    }

    // 啟用狀態篩選
    if (query.isEnabled !== undefined) {
      where.is_enabled = query.isEnabled;
    }

    // 搜尋關鍵字
    if (query.search) {
      where.OR = [
        { name: { contains: query.search, mode: 'insensitive' } },
        { description: { contains: query.search, mode: 'insensitive' } },
        { key: { contains: query.search, mode: 'insensitive' } },
      ];
    }

    const tools = await this.prisma.ai_tools.findMany({
      where,
      orderBy: [
        { scope: 'asc' }, // 系統級優先
        { updated_at: 'desc' },
      ],
    });

    return tools;
  }

  /**
   * 根據 ID 查詢單一工具
   */
  async findOne(id: string): Promise<AiToolResponseDto> {
    const tool = await this.prisma.ai_tools.findUnique({
      where: { id },
    });

    if (!tool) {
      throw new NotFoundException(`找不到 ID 為 ${id} 的 AI 工具`);
    }

    return tool;
  }

  /**
   * 根據 key 查詢工具
   */
  async findByKey(key: string): Promise<AiToolResponseDto | null> {
    const tool = await this.prisma.ai_tools.findUnique({
      where: { key },
    });

    return tool;
  }

  /**
   * 建立新工具
   */
  async create(data: CreateAiToolDto, userId?: string): Promise<AiToolResponseDto> {
    // 檢查 key 是否已存在
    const existingTool = await this.findByKey(data.key);
    if (existingTool) {
      throw new ConflictException(`工具標識符 "${data.key}" 已存在`);
    }

    // 驗證範疇與租戶/工作空間的一致性
    this.validateScopeConsistency(data.scope, data.tenantId, data.workspaceId);

    const now = new Date();
    const tool = await this.prisma.ai_tools.create({
      data: {
        id: uuidv4(),
        key: data.key,
        name: data.name,
        description: data.description,
        input_schema: data.inputSchema ? JSON.parse(JSON.stringify(data.inputSchema)) : null,
        scope: data.scope,
        is_enabled: data.isEnabled !== undefined ? data.isEnabled : true,
        tenant_id: data.tenantId || null,
        workspace_id: data.workspaceId || null,
        created_by: userId || data.createdBy || null,
        created_at: now,
        updated_at: now,
      },
    });

    this.logger.log(`建立 AI 工具: ${tool.key} (${tool.name})`);
    return tool;
  }

  /**
   * 更新工具
   */
  async update(id: string, data: UpdateAiToolDto): Promise<AiToolResponseDto> {
    const existingTool = await this.findOne(id);

    // 如果更新範疇，驗證一致性
    if (data.scope) {
      this.validateScopeConsistency(
        data.scope,
        data.tenantId !== undefined ? data.tenantId : existingTool.tenant_id || undefined,
        data.workspaceId !== undefined ? data.workspaceId : existingTool.workspace_id || undefined,
      );
    }

    const updateData: any = {};
    if (data.name !== undefined) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.inputSchema !== undefined) {
      updateData.input_schema = data.inputSchema
        ? JSON.parse(JSON.stringify(data.inputSchema))
        : null;
    }
    if (data.scope !== undefined) updateData.scope = data.scope;
    if (data.isEnabled !== undefined) updateData.is_enabled = data.isEnabled;
    if (data.tenantId !== undefined) updateData.tenant_id = data.tenantId || null;
    if (data.workspaceId !== undefined) updateData.workspace_id = data.workspaceId || null;
    if (data.updatedBy !== undefined) updateData.updated_by = data.updatedBy;

    updateData.updated_at = new Date();

    const tool = await this.prisma.ai_tools.update({
      where: { id },
      data: updateData,
    });

    this.logger.log(`更新 AI 工具: ${tool.key} (${tool.name})`);
    return tool;
  }

  /**
   * 刪除工具
   */
  async remove(id: string): Promise<void> {
    const tool = await this.findOne(id);

    // 檢查是否有 Bot 正在使用此工具
    const agentsUsingToolCount = await this.prisma.ai_agent_tools.count({
      where: { ai_tool_id: id },
    });

    if (agentsUsingToolCount > 0) {
      throw new ConflictException(
        `無法刪除 AI 工具 "${tool.name}"，因為目前有 ${agentsUsingToolCount} 個 AI Agent 正在使用此工具。請先解除關聯或刪除這些 Agent。`,
      );
    }

    await this.prisma.ai_tools.delete({
      where: { id },
    });

    this.logger.log(`刪除 AI 工具: ${tool.key} (${tool.name})`);
  }

  /**
   * 取得可用於指定租戶/工作空間的工具列表
   */
  async getAvailableTools(tenantId?: string, workspaceId?: string): Promise<AiToolResponseDto[]> {
    const where: any = {
      is_enabled: true,
      OR: [
        // 系統級工具
        { scope: AiToolScope.SYSTEM },
        // 租戶級工具
        ...(tenantId ? [{ scope: AiToolScope.TENANT, tenant_id: tenantId }] : []),
        // 工作空間級工具
        ...(workspaceId ? [{ scope: AiToolScope.WORKSPACE, workspace_id: workspaceId }] : []),
      ],
    };

    const tools = await this.prisma.ai_tools.findMany({
      where,
      orderBy: [
        { scope: 'asc' }, // 系統級優先
        { name: 'asc' },
      ],
    });

    return tools;
  }

  /**
   * 驗證範疇與租戶/工作空間的一致性
   */
  private validateScopeConsistency(
    scope: AiToolScope,
    tenantId?: string,
    workspaceId?: string,
  ): void {
    switch (scope) {
      case AiToolScope.SYSTEM:
        if (tenantId || workspaceId) {
          throw new BadRequestException('系統級工具不應設定租戶 ID 或工作空間 ID');
        }
        break;
      case AiToolScope.TENANT:
        if (!tenantId) {
          throw new BadRequestException('租戶級工具必須設定租戶 ID');
        }
        if (workspaceId) {
          throw new BadRequestException('租戶級工具不應設定工作空間 ID');
        }
        break;
      case AiToolScope.WORKSPACE:
        if (!workspaceId) {
          throw new BadRequestException('工作空間級工具必須設定工作空間 ID');
        }
        // 工作空間級工具可以選擇性地設定租戶 ID（用於額外的安全檢查）
        break;
    }
  }
}
