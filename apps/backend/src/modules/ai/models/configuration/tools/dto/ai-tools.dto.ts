import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsNotEmpty, IsOptional, IsString, IsObject } from 'class-validator';
import { AiToolScope } from '@prisma/client';

export class CreateAiToolDto {
  @ApiProperty({ description: '工具的程式化標識符', example: 'file_reader' })
  @IsString()
  @IsNotEmpty()
  key: string;

  @ApiProperty({ description: '工具顯示名稱', example: '檔案讀取工具' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({ description: '工具描述' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: '輸入參數的 JSON Schema',
    example: {
      type: 'object',
      properties: {
        file_path: { type: 'string', description: '檔案路徑' },
      },
      required: ['file_path'],
    },
  })
  @IsObject()
  @IsOptional()
  inputSchema?: Record<string, any>;

  @ApiProperty({
    description: '工具範疇',
    enum: AiToolScope,
    example: AiToolScope.SYSTEM,
  })
  @IsEnum(AiToolScope)
  scope: AiToolScope;

  @ApiPropertyOptional({ description: '是否啟用', default: true })
  @IsBoolean()
  @IsOptional()
  isEnabled?: boolean;

  @ApiPropertyOptional({ description: '租戶 ID（若為租戶級工具）' })
  @IsString()
  @IsOptional()
  tenantId?: string;

  @ApiPropertyOptional({ description: '工作空間 ID（若為工作空間級工具）' })
  @IsString()
  @IsOptional()
  workspaceId?: string;

  @ApiPropertyOptional({ description: '建立者 ID' })
  @IsString()
  @IsOptional()
  createdBy?: string;
}

export class UpdateAiToolDto {
  @ApiPropertyOptional({ description: '工具顯示名稱' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({ description: '工具描述' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ description: '輸入參數的 JSON Schema' })
  @IsObject()
  @IsOptional()
  inputSchema?: Record<string, any>;

  @ApiPropertyOptional({ description: '工具範疇', enum: AiToolScope })
  @IsEnum(AiToolScope)
  @IsOptional()
  scope?: AiToolScope;

  @ApiPropertyOptional({ description: '是否啟用' })
  @IsBoolean()
  @IsOptional()
  isEnabled?: boolean;

  @ApiPropertyOptional({ description: '租戶 ID（若為租戶級工具）' })
  @IsString()
  @IsOptional()
  tenantId?: string;

  @ApiPropertyOptional({ description: '工作空間 ID（若為工作空間級工具）' })
  @IsString()
  @IsOptional()
  workspaceId?: string;

  @ApiPropertyOptional({ description: '更新者 ID' })
  @IsString()
  @IsOptional()
  updatedBy?: string;
}

export class AiToolQueryDto {
  @ApiPropertyOptional({ description: '工具範疇篩選', enum: AiToolScope })
  @IsEnum(AiToolScope)
  @IsOptional()
  scope?: AiToolScope;

  @ApiPropertyOptional({ description: '租戶 ID 篩選' })
  @IsString()
  @IsOptional()
  tenantId?: string;

  @ApiPropertyOptional({ description: '工作空間 ID 篩選' })
  @IsString()
  @IsOptional()
  workspaceId?: string;

  @ApiPropertyOptional({ description: '是否啟用篩選' })
  @IsBoolean()
  @IsOptional()
  isEnabled?: boolean;

  @ApiPropertyOptional({ description: '搜尋關鍵字（名稱或描述）' })
  @IsString()
  @IsOptional()
  search?: string;
}

export class AiToolResponseDto {
  @ApiProperty({ description: '工具 ID' })
  id: string;

  @ApiProperty({ description: '工具的程式化標識符' })
  key: string;

  @ApiProperty({ description: '工具顯示名稱' })
  name: string;

  @ApiPropertyOptional({ description: '工具描述' })
  description?: string | null;

  @ApiPropertyOptional({ description: '輸入參數的 JSON Schema' })
  input_schema?: any;

  @ApiProperty({ description: '工具範疇', enum: AiToolScope })
  scope: AiToolScope;

  @ApiProperty({ description: '是否啟用' })
  is_enabled: boolean;

  @ApiPropertyOptional({ description: '租戶 ID' })
  tenant_id?: string | null;

  @ApiPropertyOptional({ description: '工作空間 ID' })
  workspace_id?: string | null;

  @ApiProperty({ description: '建立時間' })
  created_at: Date;

  @ApiProperty({ description: '更新時間' })
  updated_at: Date;

  @ApiPropertyOptional({ description: '建立者 ID' })
  created_by?: string | null;

  @ApiPropertyOptional({ description: '更新者 ID' })
  updated_by?: string | null;
}
