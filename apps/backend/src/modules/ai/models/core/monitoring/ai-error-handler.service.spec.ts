import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { AiErrorHandlerService } from './ai-error-handler.service';
import {
  BaseAiException,
  AiCircuitBreakerOpenException,
  AiApiKeyException,
} from '../exceptions/ai-service.exceptions';

describe('AiErrorHandlerService', () => {
  let service: AiErrorHandlerService;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn((key: string, defaultValue?: any) => {
        const config = {
          AI_OPENAI_CONCURRENCY_LIMIT: 10,
          AI_ANTHROPIC_CONCURRENCY_LIMIT: 5,
          AI_GOOGLE_CONCURRENCY_LIMIT: 8,
        };
        return config[key] || defaultValue;
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AiErrorHandlerService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<AiErrorHandlerService>(AiErrorHandlerService);
    configService = module.get(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('executeWithErrorHandling', () => {
    it('should execute operation successfully', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');

      const result = await service.executeWithErrorHandling(
        mockOperation,
        'openai',
        'gpt-3.5-turbo',
      );

      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('should retry on retryable errors', async () => {
      const mockOperation = jest
        .fn()
        .mockRejectedValueOnce(new Error('ECONNRESET'))
        .mockRejectedValueOnce(new Error('ETIMEDOUT'))
        .mockResolvedValue('success');

      const result = await service.executeWithErrorHandling(
        mockOperation,
        'openai',
        'gpt-3.5-turbo',
      );

      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(3);
    });

    it('should not retry on non-retryable errors', async () => {
      const nonRetryableError = new AiApiKeyException('Invalid API key', 'openai');

      const mockOperation = jest.fn().mockRejectedValue(nonRetryableError);

      await expect(
        service.executeWithErrorHandling(mockOperation, 'openai', 'gpt-3.5-turbo'),
      ).rejects.toThrow(nonRetryableError);

      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('should throw circuit breaker exception when circuit is open', async () => {
      // 先觸發足夠的失敗來開啟斷路器
      const mockOperation = jest.fn().mockRejectedValue(new Error('Service unavailable'));

      // 執行多次失敗操作來觸發斷路器
      for (let i = 0; i < 6; i++) {
        try {
          await service.executeWithErrorHandling(mockOperation, 'openai', 'gpt-3.5-turbo');
        } catch (error) {
          // 忽略錯誤，我們只是想觸發斷路器
        }
      }

      // 現在斷路器應該已經開啟，下一次調用應該立即失敗
      await expect(
        service.executeWithErrorHandling(mockOperation, 'openai', 'gpt-3.5-turbo'),
      ).rejects.toThrow();
    });
  });

  describe('executeWithFallback', () => {
    it('should use fallback when primary operation fails', async () => {
      const primaryOperation = jest.fn().mockRejectedValue(new Error('Primary failed'));
      const fallbackOperation = jest.fn().mockResolvedValue('fallback success');

      const result = await service.executeWithFallback(
        primaryOperation,
        fallbackOperation,
        'openai',
        'gpt-3.5-turbo',
      );

      expect(result).toBe('fallback success');
      expect(primaryOperation).toHaveBeenCalled();
      expect(fallbackOperation).toHaveBeenCalled();
    });

    it('should return primary result when primary operation succeeds', async () => {
      const primaryOperation = jest.fn().mockResolvedValue('primary success');
      const fallbackOperation = jest.fn().mockResolvedValue('fallback success');

      const result = await service.executeWithFallback(
        primaryOperation,
        fallbackOperation,
        'openai',
        'gpt-3.5-turbo',
      );

      expect(result).toBe('primary success');
      expect(primaryOperation).toHaveBeenCalled();
      expect(fallbackOperation).not.toHaveBeenCalled();
    });

    it('should throw error when both primary and fallback fail', async () => {
      const primaryOperation = jest.fn().mockRejectedValue(new Error('Primary failed'));
      const fallbackOperation = jest.fn().mockRejectedValue(new Error('Fallback failed'));

      await expect(
        service.executeWithFallback(primaryOperation, fallbackOperation, 'openai', 'gpt-3.5-turbo'),
      ).rejects.toThrow();

      expect(primaryOperation).toHaveBeenCalled();
      expect(fallbackOperation).toHaveBeenCalled();
    });
  });

  describe('executeBatch', () => {
    it('should execute all operations successfully', async () => {
      const operations = [
        jest.fn().mockResolvedValue('result1'),
        jest.fn().mockResolvedValue('result2'),
        jest.fn().mockResolvedValue('result3'),
      ];

      const results = await service.executeBatch(operations, 'openai', 'gpt-3.5-turbo', {
        maxConcurrency: 2,
      });

      expect(results).toHaveLength(3);
      expect(results.every((r) => r.success)).toBe(true);
      expect(results[0].data).toBe('result1');
      expect(results[1].data).toBe('result2');
      expect(results[2].data).toBe('result3');
    });

    it('should handle mixed success and failure', async () => {
      const operations = [
        jest.fn().mockResolvedValue('success'),
        jest.fn().mockRejectedValue(new Error('failed')),
        jest.fn().mockResolvedValue('success2'),
      ];

      const results = await service.executeBatch(operations, 'openai', 'gpt-3.5-turbo', {
        maxConcurrency: 2,
        failFast: false,
      });

      expect(results).toHaveLength(3);
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(false);
      expect(results[2].success).toBe(true);
    });

    it('should fail fast when failFast is true', async () => {
      const operations = [
        jest.fn().mockResolvedValue('success'),
        jest.fn().mockRejectedValue(new Error('failed')),
        jest.fn().mockResolvedValue('success2'),
      ];

      await expect(
        service.executeBatch(operations, 'openai', 'gpt-3.5-turbo', {
          maxConcurrency: 2,
          failFast: true,
        }),
      ).rejects.toThrow();
    });
  });

  describe('circuit breaker management', () => {
    it('should reset circuit breaker manually', () => {
      const result = service.resetCircuitBreaker('openai', 'gpt-3.5-turbo');
      expect(result).toBe(true);
    });

    it('should get circuit breaker status', () => {
      const status = service.getCircuitBreakerStatus('openai', 'gpt-3.5-turbo');
      expect(status).toBeDefined();
      expect(status?.state).toBeDefined();
      expect(status?.isOpen).toBeDefined();
      expect(status?.isHalfOpen).toBeDefined();
    });

    it('should get health report', () => {
      const report = service.getHealthReport();
      expect(report).toBeDefined();
      expect(typeof report).toBe('object');
    });
  });

  describe('concurrency limits', () => {
    it('should set concurrency limit', () => {
      service.setConcurrencyLimit('openai', 15);
      // 測試是否正確設置了限制（這需要通過後續的操作來驗證）
    });
  });

  describe('cleanup', () => {
    it('should cleanup resources', async () => {
      await expect(service.cleanup()).resolves.not.toThrow();
    });
  });
});
