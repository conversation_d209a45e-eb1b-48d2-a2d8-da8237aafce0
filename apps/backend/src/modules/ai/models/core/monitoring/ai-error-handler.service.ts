import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import retry from 'async-retry';
import CircuitBreaker from 'opossum';
import {
  BaseAiException,
  AiErrorMapper,
  AiCircuitBreakerOpenException,
  AiServiceDegradedException,
  AiConcurrencyLimitException,
} from '../exceptions/ai-service.exceptions';

/**
 * 重試策略配置
 */
interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitterFactor: number;
  retryCondition?: (error: any) => boolean;
}

/**
 * 斷路器狀態
 */
type CircuitBreakerState = 'closed' | 'open' | 'half-open';

/**
 * 斷路器配置
 */
interface CircuitBreakerConfig {
  timeout: number;
  errorThresholdPercentage: number;
  resetTimeout: number;
  rollingCountTimeout: number;
  rollingCountBuckets: number;
  name?: string;
  group?: string;
}

/**
 * 服務狀態記錄
 */
interface ServiceHealth {
  provider: string;
  model?: string;
  state: CircuitBreakerState;
  failureCount: number;
  successCount: number;
  lastFailureTime: number;
  lastSuccessTime: number;
  activeRequests: number;
  circuitBreaker?: CircuitBreaker;
}

/**
 * 重試結果
 */
interface RetryResult<T> {
  success: boolean;
  data?: T;
  error?: BaseAiException;
  attempts: number;
  totalDuration: number;
}

/**
 * Fallback 策略配置
 */
interface FallbackConfig {
  enabled: boolean;
  fallbackProvider?: string;
  fallbackModel?: string;
  degradedService?: boolean;
}

@Injectable()
export class AiErrorHandlerService {
  private readonly logger = new Logger(AiErrorHandlerService.name);
  private readonly serviceHealthMap = new Map<string, ServiceHealth>();
  private readonly concurrencyLimits = new Map<string, number>();
  private readonly circuitBreakers = new Map<string, CircuitBreaker>();

  // 預設配置
  private readonly defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2,
    jitterFactor: 0.1,
    retryCondition: (error: any) => {
      // 只對可重試的錯誤進行重試
      if (error instanceof BaseAiException) {
        return error.retryable;
      }
      // 對於網路錯誤、超時等進行重試
      return (
        ['ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND', 'EAI_AGAIN'].includes(error.code) ||
        error.message?.includes('timeout') ||
        error.message?.includes('network') ||
        (error.status >= 500 && error.status < 600)
      );
    },
  };

  private readonly defaultCircuitBreakerConfig: CircuitBreakerConfig = {
    timeout: 30000, // 30 秒超時
    errorThresholdPercentage: 50, // 50% 錯誤率觸發斷路器
    resetTimeout: 60000, // 1 分鐘後嘗試半開
    rollingCountTimeout: 60000, // 滾動視窗 1 分鐘
    rollingCountBuckets: 10, // 10 個時間桶
  };

  private readonly defaultFallbackConfig: FallbackConfig = {
    enabled: true,
    degradedService: false,
  };

  constructor(private readonly configService: ConfigService) {
    this.initializeConfig();
  }

  /**
   * 初始化配置
   */
  private initializeConfig(): void {
    // 設定並發限制
    this.concurrencyLimits.set('openai', this.configService.get('AI_OPENAI_CONCURRENCY_LIMIT', 10));
    this.concurrencyLimits.set(
      'anthropic',
      this.configService.get('AI_ANTHROPIC_CONCURRENCY_LIMIT', 5),
    );
    this.concurrencyLimits.set('google', this.configService.get('AI_GOOGLE_CONCURRENCY_LIMIT', 8));

    this.logger.log('AI 錯誤處理服務已初始化');
  }

  /**
   * 執行帶有錯誤處理的 AI 操作
   */
  async executeWithErrorHandling<T>(
    operation: () => Promise<T>,
    provider: string,
    model?: string,
    customRetryConfig?: Partial<RetryConfig>,
  ): Promise<T> {
    const serviceKey = this.getServiceKey(provider, model);
    const circuitBreaker = this.getOrCreateCircuitBreaker(serviceKey, provider, model);

    // 檢查並發限制
    await this.checkConcurrencyLimit(serviceKey, provider);

    try {
      // 增加活躍請求計數
      this.incrementActiveRequests(serviceKey, provider, model);

      // 使用斷路器執行操作
      const result = await circuitBreaker.fire(async () => {
        return await this.executeWithRetry(operation, provider, model, customRetryConfig);
      });

      // 記錄成功
      this.recordSuccess(serviceKey, provider, model);

      return result.data!;
    } catch (error) {
      // 記錄失敗
      this.recordFailure(serviceKey, provider, model, error);
      throw error;
    } finally {
      // 減少活躍請求計數
      this.decrementActiveRequests(serviceKey, provider, model);
    }
  }

  /**
   * 帶重試邏輯的執行 - 使用 async-retry
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    provider: string,
    model?: string,
    customRetryConfig?: Partial<RetryConfig>,
  ): Promise<RetryResult<T>> {
    const config = { ...this.defaultRetryConfig, ...customRetryConfig };
    const startTime = Date.now();

    try {
      const data = await retry(
        async (bail, attempt) => {
          try {
            this.logger.debug(
              `AI 操作嘗試 ${attempt}/${config.maxRetries + 1} - ${provider}/${model || 'default'}`,
            );
            return await operation();
          } catch (error) {
            // 轉換為 AI 特定異常
            const aiError =
              error instanceof BaseAiException
                ? error
                : AiErrorMapper.mapError(error, provider, model);

            this.logger.warn(
              `AI 操作失敗 (嘗試 ${attempt}/${config.maxRetries + 1}): ${aiError.message}`,
              {
                provider,
                model,
                error: aiError.name,
                retryable: aiError.retryable,
              },
            );

            // 檢查是否應該重試
            if (!config.retryCondition?.(aiError) || !aiError.retryable) {
              this.logger.error(`不可重試的錯誤，停止重試: ${aiError.message}`);
              bail(aiError);
              return;
            }

            throw aiError;
          }
        },
        {
          retries: config.maxRetries,
          factor: config.backoffMultiplier,
          minTimeout: config.baseDelay,
          maxTimeout: config.maxDelay,
          randomize: true, // 使用隨機化避免驚群效應
          onRetry: (error: any, attempt) => {
            this.logger.warn(`重試第 ${attempt} 次，錯誤: ${error?.message || error}`);
          },
        },
      );

      return {
        success: true,
        data,
        attempts: 1, // async-retry 不提供實際嘗試次數，這裡簡化處理
        totalDuration: Date.now() - startTime,
      };
    } catch (error) {
      const aiError =
        error instanceof BaseAiException ? error : AiErrorMapper.mapError(error, provider, model);

      return {
        success: false,
        error: aiError,
        attempts: config.maxRetries + 1,
        totalDuration: Date.now() - startTime,
      };
    }
  }

  /**
   * 獲取或建立斷路器
   */
  private getOrCreateCircuitBreaker(
    serviceKey: string,
    provider: string,
    model?: string,
  ): CircuitBreaker {
    if (!this.circuitBreakers.has(serviceKey)) {
      const config = {
        ...this.defaultCircuitBreakerConfig,
        name: serviceKey,
        group: provider,
      };

      const circuitBreaker = new CircuitBreaker(async (operation: () => Promise<any>) => {
        return await operation();
      }, config);

      // 設定事件監聽器
      circuitBreaker.on('open', () => {
        this.logger.error(`斷路器開啟: ${serviceKey}`);
        const health = this.getServiceHealth(serviceKey, provider, model);
        health.state = 'open';
      });

      circuitBreaker.on('halfOpen', () => {
        this.logger.warn(`斷路器半開: ${serviceKey}`);
        const health = this.getServiceHealth(serviceKey, provider, model);
        health.state = 'half-open';
      });

      circuitBreaker.on('close', () => {
        this.logger.log(`斷路器關閉: ${serviceKey}`);
        const health = this.getServiceHealth(serviceKey, provider, model);
        health.state = 'closed';
      });

      circuitBreaker.on('failure', (error) => {
        this.logger.warn(`斷路器記錄失敗: ${serviceKey} - ${error.message}`);
      });

      circuitBreaker.on('success', () => {
        this.logger.debug(`斷路器記錄成功: ${serviceKey}`);
      });

      circuitBreaker.on('timeout', () => {
        this.logger.error(`斷路器超時: ${serviceKey}`);
      });

      circuitBreaker.on('reject', () => {
        this.logger.warn(`斷路器拒絕請求: ${serviceKey}`);
      });

      this.circuitBreakers.set(serviceKey, circuitBreaker);

      // 同時更新健康狀態
      const health = this.getServiceHealth(serviceKey, provider, model);
      health.circuitBreaker = circuitBreaker;
    }

    return this.circuitBreakers.get(serviceKey)!;
  }

  /**
   * 檢查並發限制
   */
  private async checkConcurrencyLimit(serviceKey: string, provider: string): Promise<void> {
    const health = this.getServiceHealth(serviceKey, provider);
    const limit = this.concurrencyLimits.get(provider) || 10;

    if (health.activeRequests >= limit) {
      throw new AiConcurrencyLimitException(
        `AI 服務並發請求超限 (${health.activeRequests}/${limit})`,
        provider,
      );
    }
  }

  /**
   * 記錄成功
   */
  private recordSuccess(serviceKey: string, provider: string, model?: string): void {
    const health = this.getServiceHealth(serviceKey, provider, model);
    health.successCount++;
    health.lastSuccessTime = Date.now();
    health.failureCount = Math.max(0, health.failureCount - 1); // 成功時減少失敗計數
  }

  /**
   * 記錄失敗
   */
  private recordFailure(
    serviceKey: string,
    provider: string,
    model: string | undefined,
    error: any,
  ): void {
    const health = this.getServiceHealth(serviceKey, provider, model);
    health.failureCount++;
    health.lastFailureTime = Date.now();
  }

  /**
   * 增加活躍請求計數
   */
  private incrementActiveRequests(serviceKey: string, provider: string, model?: string): void {
    const health = this.getServiceHealth(serviceKey, provider, model);
    health.activeRequests++;
  }

  /**
   * 減少活躍請求計數
   */
  private decrementActiveRequests(serviceKey: string, provider: string, model?: string): void {
    const health = this.getServiceHealth(serviceKey, provider, model);
    health.activeRequests = Math.max(0, health.activeRequests - 1);
  }

  /**
   * 獲取服務健康狀態
   */
  private getServiceHealth(serviceKey: string, provider: string, model?: string): ServiceHealth {
    if (!this.serviceHealthMap.has(serviceKey)) {
      this.serviceHealthMap.set(serviceKey, {
        provider,
        model,
        state: 'closed',
        failureCount: 0,
        successCount: 0,
        lastFailureTime: 0,
        lastSuccessTime: Date.now(),
        activeRequests: 0,
      });
    }
    return this.serviceHealthMap.get(serviceKey)!;
  }

  /**
   * 生成服務鍵
   */
  private getServiceKey(provider: string, model?: string): string {
    return model ? `${provider}:${model}` : provider;
  }

  /**
   * 獲取服務健康狀態報告
   */
  getHealthReport(): Record<string, ServiceHealth & { circuitBreakerStats?: any }> {
    const report: Record<string, ServiceHealth & { circuitBreakerStats?: any }> = {};

    this.serviceHealthMap.forEach((health, key) => {
      const circuitBreaker = this.circuitBreakers.get(key);
      report[key] = {
        ...health,
        circuitBreakerStats: circuitBreaker
          ? {
              state: circuitBreaker.opened
                ? 'open'
                : circuitBreaker.halfOpen
                  ? 'half-open'
                  : 'closed',
              stats: circuitBreaker.stats,
            }
          : undefined,
      };
    });

    return report;
  }

  /**
   * 手動重置斷路器
   */
  resetCircuitBreaker(provider: string, model?: string): boolean {
    const serviceKey = this.getServiceKey(provider, model);
    const circuitBreaker = this.circuitBreakers.get(serviceKey);
    const health = this.serviceHealthMap.get(serviceKey);

    if (circuitBreaker && health) {
      circuitBreaker.close();
      health.state = 'closed';
      health.failureCount = 0;
      health.successCount = 0;
      this.logger.log(`手動重置斷路器: ${serviceKey}`);
      return true;
    }

    return false;
  }

  /**
   * 設定並發限制
   */
  setConcurrencyLimit(provider: string, limit: number): void {
    this.concurrencyLimits.set(provider, limit);
    this.logger.log(`設定 ${provider} 並發限制為 ${limit}`);
  }

  /**
   * 執行服務降級 - 增強版本
   */
  async executeWithFallback<T>(
    primaryOperation: () => Promise<T>,
    fallbackOperation: () => Promise<T>,
    provider: string,
    model?: string,
    fallbackConfig?: Partial<FallbackConfig>,
  ): Promise<T> {
    const config = { ...this.defaultFallbackConfig, ...fallbackConfig };

    if (!config.enabled) {
      return await this.executeWithErrorHandling(primaryOperation, provider, model);
    }

    try {
      return await this.executeWithErrorHandling(primaryOperation, provider, model);
    } catch (primaryError) {
      this.logger.warn(`主要服務失敗，使用備用方案: ${provider}/${model || 'default'}`, {
        error: primaryError.message,
      });

      try {
        const result = await fallbackOperation();

        if (config.degradedService) {
          this.logger.warn('服務已降級運行');
          // 可以在這裡發送降級警告事件
        }

        return result;
      } catch (fallbackError) {
        this.logger.error('備用方案也失敗了', {
          primaryError: primaryError.message,
          fallbackError: fallbackError.message,
        });

        // 拋出原始錯誤，因為它通常更有意義
        throw AiErrorMapper.mapError(primaryError, provider, model);
      }
    }
  }

  /**
   * 批量執行操作 - 新增功能
   */
  async executeBatch<T>(
    operations: Array<() => Promise<T>>,
    provider: string,
    model?: string,
    options?: {
      maxConcurrency?: number;
      failFast?: boolean;
      retryConfig?: Partial<RetryConfig>;
    },
  ): Promise<Array<{ success: boolean; data?: T; error?: Error }>> {
    const { maxConcurrency = 5, failFast = false, retryConfig } = options || {};
    const results: Array<{ success: boolean; data?: T; error?: Error }> = [];

    // 分批處理操作
    for (let i = 0; i < operations.length; i += maxConcurrency) {
      const batch = operations.slice(i, i + maxConcurrency);

      const batchPromises = batch.map(async (operation) => {
        try {
          const data = await this.executeWithErrorHandling(operation, provider, model, retryConfig);
          return { success: true, data };
        } catch (error) {
          if (failFast) {
            throw error;
          }
          return { success: false, error: error as Error };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return results;
  }

  /**
   * 獲取斷路器狀態
   */
  getCircuitBreakerStatus(
    provider: string,
    model?: string,
  ): {
    state: string;
    stats: any;
    isOpen: boolean;
    isHalfOpen: boolean;
  } | null {
    const serviceKey = this.getServiceKey(provider, model);
    const circuitBreaker = this.circuitBreakers.get(serviceKey);

    if (!circuitBreaker) {
      return null;
    }

    return {
      state: circuitBreaker.opened ? 'open' : circuitBreaker.halfOpen ? 'half-open' : 'closed',
      stats: circuitBreaker.stats,
      isOpen: circuitBreaker.opened,
      isHalfOpen: circuitBreaker.halfOpen,
    };
  }

  /**
   * 清理資源
   */
  async cleanup(): Promise<void> {
    this.logger.log('清理 AI 錯誤處理服務資源...');

    // 關閉所有斷路器
    this.circuitBreakers.forEach((circuitBreaker, key) => {
      circuitBreaker.shutdown();
      this.logger.debug(`已關閉斷路器: ${key}`);
    });

    this.circuitBreakers.clear();
    this.serviceHealthMap.clear();

    this.logger.log('AI 錯誤處理服務資源清理完成');
  }
}
