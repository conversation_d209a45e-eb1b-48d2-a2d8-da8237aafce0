import { Injectable } from '@nestjs/common';
import { BaseAiProvider } from './base/base.provider';
import { PrismaClient, AiAgentProviderType } from '@prisma/client';
import { OpenAiProvider } from './implementations/openai.provider';
import { <PERSON><PERSON><PERSON>ider } from './implementations/claude.provider';
import { GeminiProvider } from './implementations/gemini.provider';
import { OpenAiCompatibleProvider } from './implementations/openai-compatible.provider';

@Injectable()
export class AiProviderFactory {
  createProvider(
    providerType: AiAgentProviderType,
    apiKey: string,
    apiUrl?: string,
  ): BaseAiProvider {
    switch (providerType) {
      case AiAgentProviderType.OPENAI:
        return new OpenAiProvider(apiKey, apiUrl);
      case AiAgentProviderType.CLAUDE:
      case AiAgentProviderType.ANTHROPIC:
        return new ClaudeProvider(apiKey, apiUrl);
      case AiAgentProviderType.GEMINI:
      case AiAgentProviderType.GOOGLE_GEMINI:
        return new GeminiProvider(apiKey, apiUrl);
      case AiAgentProviderType.OPENAI_COMPATIBLE:
        return new OpenAiCompatibleProvider(apiKey, apiUrl!);
      default:
        throw new Error(`Unsupported provider type: ${providerType}`);
    }
  }
}

export { AiAgentProviderType };
