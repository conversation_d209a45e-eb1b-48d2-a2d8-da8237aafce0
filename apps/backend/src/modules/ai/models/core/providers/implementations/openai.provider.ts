import { OpenAI } from 'openai';
import {
  BaseAiProvider,
  AiMessage,
  AiExecuteOptions,
  AiResponse,
  VisionAnalysisOptions,
} from '../base/base.provider';
import { AiErrorMapper } from '../../exceptions/ai-service.exceptions';
import { Logger } from '@nestjs/common';

export class OpenAiProvider extends BaseAiProvider {
  private openai: OpenAI;
  private readonly providerName = 'openai';

  constructor(apiKey: string, apiUrl?: string) {
    super(apiKey, new Logger(OpenAiProvider.name), apiUrl);
    this.openai = new OpenAI({
      apiKey,
      baseURL: apiUrl,
    });
  }

  private convertToOpenAIMessage(
    message: AiMessage,
  ): OpenAI.Chat.Completions.ChatCompletionMessageParam {
    // 處理字串內容
    if (typeof message.content === 'string') {
      return {
        role: message.role as 'system' | 'user' | 'assistant',
        content: message.content,
      };
    }

    // 處理陣列內容（多模態訊息）
    if (Array.isArray(message.content)) {
      // 系統訊息不支援陣列內容，轉換為字串
      if (message.role === 'system') {
        const textContent = message.content
          .filter((item) => item.type === 'text' && item.text)
          .map((item) => item.text)
          .join(' ');
        return {
          role: 'system',
          content: textContent || '',
        };
      }

      // 助手訊息也不支援陣列內容，轉換為字串
      if (message.role === 'assistant') {
        const textContent = message.content
          .filter((item) => item.type === 'text' && item.text)
          .map((item) => item.text)
          .join(' ');
        return {
          role: 'assistant',
          content: textContent || '',
        };
      }

      // 只有用戶訊息支援陣列內容（多模態）
      const contentParts: OpenAI.Chat.Completions.ChatCompletionContentPart[] = message.content.map(
        (item) => {
          if (item.type === 'text') {
            return {
              type: 'text',
              text: item.text || '',
            };
          } else if (item.type === 'image_url' && item.image_url) {
            return {
              type: 'image_url',
              image_url: {
                url: item.image_url.url,
                detail: item.image_url.detail || 'auto',
              },
            };
          }
          return {
            type: 'text',
            text: '',
          };
        },
      );

      return {
        role: 'user',
        content: contentParts,
      };
    }

    // 後備處理
    return {
      role: message.role as 'system' | 'user' | 'assistant',
      content: String(message.content),
    };
  }

  async _execute(messages: AiMessage[], options: AiExecuteOptions): Promise<AiResponse> {
    const { model, temperature, maxTokens, responseFormat } = options;

    try {
      const response = await this.openai.chat.completions.create({
        model: model,
        messages: messages.map((msg) => this.convertToOpenAIMessage(msg)),
        temperature: temperature,
        max_tokens: maxTokens,
        response_format: responseFormat === 'JSON' ? { type: 'json_object' } : undefined,
      });

      const responseContent = response.choices?.[0]?.message?.content;
      if (!responseContent) {
        throw new Error('無效的 OpenAI API 回應格式');
      }

      return {
        content: responseContent,
        usage: {
          inputTokens: response.usage?.prompt_tokens ?? 0,
          outputTokens: response.usage?.completion_tokens ?? 0,
        },
      };
    } catch (error) {
      this.logger.error(`OpenAI API error: ${error.message}`, error.stack);
      throw AiErrorMapper.mapError(error, 'openai', model);
    }
  }

  async getAvailableModels(): Promise<string[]> {
    try {
      this.logger.log('Fetching available models from OpenAI API');
      const models = await this.openai.models.list();
      const modelIds = models.data.map((model) => model.id);
      this.logger.log(`Successfully fetched ${modelIds.length} models from OpenAI API`);
      return modelIds;
    } catch (error) {
      this.logger.error(`Failed to fetch OpenAI models: ${error.message}`, error.stack);
      throw AiErrorMapper.mapError(error, 'openai');
    }
  }

  async executeVisionAnalysis(options: VisionAnalysisOptions): Promise<AiResponse> {
    try {
      // 確保使用支援視覺的模型
      const visionModel =
        options.model.includes('vision') || options.model.includes('gpt-4')
          ? options.model
          : 'gpt-4-vision-preview';

      const response = await this.openai.chat.completions.create({
        model: visionModel,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: options.prompt,
              },
              {
                type: 'image_url',
                image_url: {
                  url: options.imageUrl,
                  detail: options.detail || 'auto',
                },
              },
            ],
          },
        ],
        temperature: options.temperature,
        max_tokens: options.maxTokens,
        response_format: options.responseFormat === 'JSON' ? { type: 'json_object' } : undefined,
      });

      if (!response.choices?.[0]?.message?.content) {
        throw new Error('Invalid OpenAI vision API response format');
      }

      return {
        content: response.choices[0].message.content,
        usage: {
          inputTokens: response.usage?.prompt_tokens || 0,
          outputTokens: response.usage?.completion_tokens || 0,
        },
      };
    } catch (error) {
      this.logger.error(`OpenAI Vision API error: ${error.message}`, error.stack);
      throw new Error(`OpenAI Vision API error: ${error.message}`);
    }
  }
}
