import { IsString, IsOptional, IsEnum, IsBoolean, IsNumber, IsArray, ValidateNested, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { RequirementType, Priority, RequirementCategory } from '../types/requirement-parsing.types';

export class ParseRequirementDto {
  @ApiProperty({ description: '要解析的文檔內容' })
  @IsString()
  content: string;

  @ApiPropertyOptional({ description: '文檔標題' })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({ description: '文檔 ID' })
  @IsOptional()
  @IsString()
  documentId?: string;

  @ApiPropertyOptional({ description: '語言代碼', default: 'zh-TW' })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiPropertyOptional({ description: '是否包含實體提取', default: true })
  @IsOptional()
  @IsBoolean()
  includeEntities?: boolean;

  @ApiPropertyOptional({ description: '是否包含依存關係分析', default: true })
  @IsOptional()
  @IsBoolean()
  includeDependencies?: boolean;

  @ApiPropertyOptional({ description: '是否包含情感分析', default: true })
  @IsOptional()
  @IsBoolean()
  includeSentiment?: boolean;

  @ApiPropertyOptional({ description: '是否包含關鍵字提取', default: true })
  @IsOptional()
  @IsBoolean()
  includeKeywords?: boolean;

  @ApiPropertyOptional({ description: '是否包含主題分析', default: false })
  @IsOptional()
  @IsBoolean()
  includeTopics?: boolean;

  @ApiPropertyOptional({ description: '信心度閾值', minimum: 0, maximum: 1, default: 0.5 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  confidenceThreshold?: number;

  @ApiPropertyOptional({ description: '最大需求數量', minimum: 1, default: 100 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxRequirements?: number;
}

export class RequirementEntityDto {
  @ApiProperty({ description: '實體文本' })
  @IsString()
  text: string;

  @ApiProperty({ description: '實體標籤' })
  @IsString()
  label: string;

  @ApiProperty({ description: '開始位置' })
  @IsNumber()
  start: number;

  @ApiProperty({ description: '結束位置' })
  @IsNumber()
  end: number;

  @ApiProperty({ description: '信心度', minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence: number;
}

export class RequirementConstraintDto {
  @ApiProperty({ description: '約束類型' })
  @IsString()
  type: string;

  @ApiProperty({ description: '約束描述' })
  @IsString()
  description: string;

  @ApiPropertyOptional({ description: '約束值' })
  @IsOptional()
  value?: string | number;

  @ApiPropertyOptional({ description: '單位' })
  @IsOptional()
  @IsString()
  unit?: string;
}

export class AcceptanceCriterionDto {
  @ApiProperty({ description: '驗收標準 ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: '驗收標準描述' })
  @IsString()
  description: string;

  @ApiProperty({ description: '驗收標準類型' })
  @IsString()
  type: string;

  @ApiProperty({ description: '是否可測試' })
  @IsBoolean()
  testable: boolean;
}

export class ParsedRequirementDto {
  @ApiProperty({ description: '需求 ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: '需求類型', enum: RequirementType })
  @IsEnum(RequirementType)
  type: RequirementType;

  @ApiProperty({ description: '需求標題' })
  @IsString()
  title: string;

  @ApiProperty({ description: '需求描述' })
  @IsString()
  description: string;

  @ApiProperty({ description: '優先級', enum: Priority })
  @IsEnum(Priority)
  priority: Priority;

  @ApiProperty({ description: '需求類別', enum: RequirementCategory })
  @IsEnum(RequirementCategory)
  category: RequirementCategory;

  @ApiProperty({ description: '提取的實體', type: [RequirementEntityDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RequirementEntityDto)
  entities: RequirementEntityDto[];

  @ApiProperty({ description: '依賴關係' })
  @IsArray()
  @IsString({ each: true })
  dependencies: string[];

  @ApiProperty({ description: '約束條件', type: [RequirementConstraintDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RequirementConstraintDto)
  constraints: RequirementConstraintDto[];

  @ApiProperty({ description: '驗收標準', type: [AcceptanceCriterionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AcceptanceCriterionDto)
  acceptanceCriteria: AcceptanceCriterionDto[];

  @ApiProperty({ description: '信心度', minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence: number;

  @ApiProperty({ description: '原始文本' })
  @IsString()
  sourceText: string;

  @ApiProperty({ description: '提取時間' })
  extractedAt: Date;
}

export class ParsingStatisticsDto {
  @ApiProperty({ description: '總句子數' })
  @IsNumber()
  totalSentences: number;

  @ApiProperty({ description: '總詞彙數' })
  @IsNumber()
  totalTokens: number;

  @ApiProperty({ description: '找到的需求數量' })
  @IsNumber()
  requirementsFound: number;

  @ApiProperty({ description: '平均信心度', minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  averageConfidence: number;

  @ApiProperty({ description: '處理時間（毫秒）' })
  @IsNumber()
  processingTime: number;

  @ApiProperty({ description: '提取的實體數量' })
  @IsNumber()
  entitiesExtracted: number;
}

export class ParsingErrorDto {
  @ApiProperty({ description: '錯誤類型' })
  @IsString()
  type: string;

  @ApiProperty({ description: '錯誤訊息' })
  @IsString()
  message: string;

  @ApiPropertyOptional({ description: '錯誤位置' })
  @IsOptional()
  location?: {
    line: number;
    column: number;
    text: string;
  };

  @ApiProperty({ description: '嚴重性等級' })
  @IsString()
  severity: 'error' | 'warning' | 'info';
}

export class ParsingWarningDto {
  @ApiProperty({ description: '警告類型' })
  @IsString()
  type: string;

  @ApiProperty({ description: '警告訊息' })
  @IsString()
  message: string;

  @ApiPropertyOptional({ description: '建議' })
  @IsOptional()
  @IsString()
  suggestion?: string;

  @ApiPropertyOptional({ description: '警告位置' })
  @IsOptional()
  location?: {
    line: number;
    column: number;
    text: string;
  };
}

export class RequirementParsingResultDto {
  @ApiProperty({ description: '解析的需求', type: [ParsedRequirementDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ParsedRequirementDto)
  requirements: ParsedRequirementDto[];

  @ApiProperty({ description: '解析統計', type: ParsingStatisticsDto })
  @ValidateNested()
  @Type(() => ParsingStatisticsDto)
  statistics: ParsingStatisticsDto;

  @ApiProperty({ description: '錯誤列表', type: [ParsingErrorDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ParsingErrorDto)
  errors: ParsingErrorDto[];

  @ApiProperty({ description: '警告列表', type: [ParsingWarningDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ParsingWarningDto)
  warnings: ParsingWarningDto[];
} 