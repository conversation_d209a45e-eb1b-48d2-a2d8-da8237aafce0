import { Controller, Post, Body, HttpCode, HttpStatus, Logger, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { RequirementParserService } from './services/requirement-parser.service';
import {
  ParseRequirementDto,
  RequirementParsingResultDto
} from './dto/requirement-parsing.dto';
import {
  RequirementDocument,
  RequirementParsingOptions
} from './types/requirement-parsing.types';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { v4 as uuidv4 } from 'uuid';

@ApiTags('NLP Requirements Parsing')
@Controller('ai/nlp')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class NLPController {
  private readonly logger = new Logger(NLPController.name);

  constructor(
    private readonly requirementParser: RequirementParserService
  ) {}

  @Post('parse-requirements')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '解析需求文檔',
    description: '使用 NLP 技術從自然語言文檔中提取結構化的需求信息'
  })
  @ApiResponse({
    status: 200,
    description: '需求解析成功',
    type: RequirementParsingResultDto
  })
  @ApiResponse({
    status: 400,
    description: '請求參數錯誤'
  })
  @ApiResponse({
    status: 401,
    description: '未授權訪問'
  })
  @ApiResponse({
    status: 500,
    description: '服務器內部錯誤'
  })
  async parseRequirements(
    @Body() parseDto: ParseRequirementDto
  ): Promise<RequirementParsingResultDto> {
    this.logger.log(`Parsing requirements for document: ${parseDto.title || 'Untitled'}`);

    try {
      // 構建需求文檔對象
      const document: RequirementDocument = {
        id: parseDto.documentId || uuidv4(),
        title: parseDto.title || 'Untitled Document',
        content: parseDto.content,
        metadata: {
          language: parseDto.language || 'zh-TW',
          createdAt: new Date().toISOString()
        }
      };

      // 構建解析選項
      const options: RequirementParsingOptions = {
        language: parseDto.language || 'zh-TW',
        includeEntities: parseDto.includeEntities !== false,
        includeDependencies: parseDto.includeDependencies !== false,
        includeSentiment: parseDto.includeSentiment !== false,
        includeKeywords: parseDto.includeKeywords !== false,
        includeTopics: parseDto.includeTopics || false,
        confidenceThreshold: parseDto.confidenceThreshold || 0.5,
        maxRequirements: parseDto.maxRequirements || 100
      };

      // 執行需求解析
      const result = await this.requirementParser.parseRequirements(document, options);

      this.logger.log(`Requirements parsing completed: ${result.requirements.length} requirements found`);

      // 轉換為 DTO 格式
      return {
        requirements: result.requirements.map(req => ({
          id: req.id,
          type: req.type,
          title: req.title,
          description: req.description,
          priority: req.priority,
          category: req.category,
          entities: req.entities.map(entity => ({
            text: entity.text,
            label: entity.label,
            start: entity.start,
            end: entity.end,
            confidence: entity.confidence
          })),
          dependencies: req.dependencies,
          constraints: req.constraints.map(constraint => ({
            type: constraint.type,
            description: constraint.description,
            value: constraint.value,
            unit: constraint.unit
          })),
          acceptanceCriteria: req.acceptanceCriteria.map(criterion => ({
            id: criterion.id,
            description: criterion.description,
            type: criterion.type,
            testable: criterion.testable
          })),
          confidence: req.confidence,
          sourceText: req.sourceText,
          extractedAt: req.extractedAt
        })),
        statistics: {
          totalSentences: result.statistics.totalSentences,
          totalTokens: result.statistics.totalTokens,
          requirementsFound: result.statistics.requirementsFound,
          averageConfidence: result.statistics.averageConfidence,
          processingTime: result.statistics.processingTime,
          entitiesExtracted: result.statistics.entitiesExtracted
        },
        errors: result.errors.map(error => ({
          type: error.type,
          message: error.message,
          location: error.location,
          severity: error.severity
        })),
        warnings: result.warnings.map(warning => ({
          type: warning.type,
          message: warning.message,
          suggestion: warning.suggestion,
          location: warning.location
        }))
      };

    } catch (error) {
      this.logger.error('Error parsing requirements:', error);
      throw error;
    }
  }

  @Post('analyze-text')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '分析文本',
    description: '對文本進行基礎的 NLP 分析，包括分詞、實體識別、情感分析等'
  })
  @ApiResponse({
    status: 200,
    description: '文本分析成功'
  })
  async analyzeText(
    @Body() body: { text: string; options?: any }
  ): Promise<any> {
    this.logger.log('Analyzing text with NLP processor');

    try {
      const nlpProcessor = this.requirementParser['nlpProcessor'];
      const result = await nlpProcessor.processText(body.text, body.options || {});

      return {
        success: true,
        data: result,
        metadata: {
          processedAt: new Date().toISOString(),
          textLength: body.text.length
        }
      };

    } catch (error) {
      this.logger.error('Error analyzing text:', error);
      throw error;
    }
  }

  @Post('extract-entities')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '提取實體',
    description: '從文本中提取命名實體，如人名、地點、組織、時間等'
  })
  @ApiResponse({
    status: 200,
    description: '實體提取成功'
  })
  async extractEntities(
    @Body() body: { text: string }
  ): Promise<any> {
    this.logger.log('Extracting entities from text');

    try {
      const nlpProcessor = this.requirementParser['nlpProcessor'];
      const result = await nlpProcessor.processText(body.text, {
        includeEntities: true,
        includeDependencies: false,
        includeSentiment: false,
        includeKeywords: false,
        includeTopics: false
      });

      return {
        success: true,
        entities: result.entities,
        metadata: {
          processedAt: new Date().toISOString(),
          entitiesCount: result.entities.length
        }
      };

    } catch (error) {
      this.logger.error('Error extracting entities:', error);
      throw error;
    }
  }

  @Post('analyze-sentiment')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '情感分析',
    description: '分析文本的情感極性和主觀性'
  })
  @ApiResponse({
    status: 200,
    description: '情感分析成功'
  })
  async analyzeSentiment(
    @Body() body: { text: string }
  ): Promise<any> {
    this.logger.log('Analyzing sentiment of text');

    try {
      const nlpProcessor = this.requirementParser['nlpProcessor'];
      const result = await nlpProcessor.processText(body.text, {
        includeEntities: false,
        includeDependencies: false,
        includeSentiment: true,
        includeKeywords: false,
        includeTopics: false
      });

      return {
        success: true,
        sentiment: result.sentiment,
        metadata: {
          processedAt: new Date().toISOString(),
          textLength: body.text.length
        }
      };

    } catch (error) {
      this.logger.error('Error analyzing sentiment:', error);
      throw error;
    }
  }

  @Post('extract-keywords')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '關鍵字提取',
    description: '從文本中提取重要的關鍵字和短語'
  })
  @ApiResponse({
    status: 200,
    description: '關鍵字提取成功'
  })
  async extractKeywords(
    @Body() body: { text: string; maxKeywords?: number }
  ): Promise<any> {
    this.logger.log('Extracting keywords from text');

    try {
      const nlpProcessor = this.requirementParser['nlpProcessor'];
      const result = await nlpProcessor.processText(body.text, {
        includeEntities: false,
        includeDependencies: false,
        includeSentiment: false,
        includeKeywords: true,
        includeTopics: false
      });

      const maxKeywords = body.maxKeywords || 20;
      const keywords = result.keywords.slice(0, maxKeywords);

      return {
        success: true,
        keywords,
        metadata: {
          processedAt: new Date().toISOString(),
          keywordsCount: keywords.length,
          totalKeywordsFound: result.keywords.length
        }
      };

    } catch (error) {
      this.logger.error('Error extracting keywords:', error);
      throw error;
    }
  }
} 