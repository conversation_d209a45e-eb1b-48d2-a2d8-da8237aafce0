import { Test, TestingModule } from '@nestjs/testing';
import { RAGIngestionService } from './rag-ingestion.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as fs from 'fs';
import * as path from 'path';

// Mock dependencies
jest.mock('fs');
jest.mock('llamaindex', () => ({
  Document: jest.fn().mockImplementation((data) => ({
    ...data,
    getText: jest.fn().mockReturnValue(data.text),
    metadata: {},
  })),
  VectorStoreIndex: {
    fromDocuments: jest.fn().mockResolvedValue({}),
  },
  SimpleDirectoryReader: jest.fn().mockImplementation(() => ({
    loadData: jest.fn().mockResolvedValue([
      {
        metadata: { file_name: 'test.pdf', filename: 'test.pdf' },
        getText: jest.fn().mockReturnValue('Test PDF content'),
      },
    ]),
  })),
  OpenAI: jest.fn(),
  Settings: {
    llm: null,
    embedModel: {
      getTextEmbedding: jest.fn().mockResolvedValue(new Array(1536).fill(0.1)),
    },
  },
}));

describe('RAGIngestionService', () => {
  let service: RAGIngestionService;
  let prismaService: jest.Mocked<PrismaService>;
  let mockFs: jest.Mocked<typeof fs>;

  const mockPrismaService = {
    vector_documents: {
      create: jest.fn(),
      findMany: jest.fn(),
    },
    vector_chunks: {
      create: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RAGIngestionService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<RAGIngestionService>(RAGIngestionService);
    prismaService = module.get(PrismaService);
    mockFs = fs as jest.Mocked<typeof fs>;

    // Reset all mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('handleFileUpload', () => {
    const mockEvent = {
      filePath: '/uploads/test.pdf',
      tenantId: 'tenant-123',
      fileId: 'file-456',
      workspaceId: 'workspace-789',
      fileName: 'test.pdf',
      mimeType: 'application/pdf',
    };

    beforeEach(() => {
      mockFs.existsSync.mockReturnValue(true);
      mockFs.readFileSync.mockReturnValue('Test file content');
      mockPrismaService.vector_documents.create.mockResolvedValue({
        id: 'doc-123',
        tenant_id: mockEvent.tenantId,
        file_id: mockEvent.fileId,
        workspace_id: mockEvent.workspaceId,
        content: 'Test file content',
        metadata: {},
        embedding: [],
        created_at: new Date(),
        updated_at: new Date(),
      });
      mockPrismaService.vector_chunks.create.mockResolvedValue({
        id: 'chunk-123',
        document_id: 'doc-123',
        tenant_id: mockEvent.tenantId,
        chunk_index: 0,
        content: 'Test file content',
        metadata: {},
        embedding: [],
        created_at: new Date(),
        updated_at: new Date(),
      });
    });

    it('should process file upload event successfully', async () => {
      await service.handleFileUpload(mockEvent);

      expect(mockFs.existsSync).toHaveBeenCalledWith(mockEvent.filePath);
      expect(mockPrismaService.vector_documents.create).toHaveBeenCalled();
      expect(mockPrismaService.vector_chunks.create).toHaveBeenCalled();
    });

    it('should throw error if file does not exist', async () => {
      mockFs.existsSync.mockReturnValue(false);

      await expect(service.handleFileUpload(mockEvent)).rejects.toThrow(
        'File not found: /uploads/test.pdf',
      );

      expect(mockPrismaService.vector_documents.create).not.toHaveBeenCalled();
    });

    it('should handle PDF files correctly', async () => {
      const pdfEvent = { ...mockEvent, fileName: 'test.pdf', mimeType: 'application/pdf' };

      await service.handleFileUpload(pdfEvent);

      expect(mockPrismaService.vector_documents.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          tenant_id: mockEvent.tenantId,
          file_id: mockEvent.fileId,
          workspace_id: mockEvent.workspaceId,
          content: expect.any(String),
          metadata: expect.objectContaining({
            tenant_id: mockEvent.tenantId,
            file_id: mockEvent.fileId,
            workspace_id: mockEvent.workspaceId,
            file_name: mockEvent.fileName,
            mime_type: mockEvent.mimeType,
          }),
          embedding: expect.any(Array),
        }),
      });
    });

    it('should handle text files correctly', async () => {
      const textEvent = { ...mockEvent, fileName: 'test.txt', mimeType: 'text/plain' };

      await service.handleFileUpload(textEvent);

      expect(mockFs.readFileSync).toHaveBeenCalledWith(mockEvent.filePath, 'utf-8');
      expect(mockPrismaService.vector_documents.create).toHaveBeenCalled();
    });

    it('should create chunks for large documents', async () => {
      const longContent = 'A '.repeat(2000); // Create content longer than chunk size
      mockFs.readFileSync.mockReturnValue(longContent);

      await service.handleFileUpload(mockEvent);

      expect(mockPrismaService.vector_chunks.create).toHaveBeenCalled();
    });

    it('should include correct metadata in documents', async () => {
      await service.handleFileUpload(mockEvent);

      const createCall = mockPrismaService.vector_documents.create.mock.calls[0][0];
      expect(createCall.data.metadata).toEqual(
        expect.objectContaining({
          tenant_id: mockEvent.tenantId,
          file_id: mockEvent.fileId,
          workspace_id: mockEvent.workspaceId,
          file_name: mockEvent.fileName,
          mime_type: mockEvent.mimeType,
          chunk_index: 0,
          created_at: expect.any(String),
        }),
      );
    });
  });

  describe('searchSimilarDocuments', () => {
    const mockQuery = 'test query';
    const mockTenantId = 'tenant-123';
    const mockWorkspaceId = 'workspace-789';

    beforeEach(() => {
      mockPrismaService.vector_documents.findMany.mockResolvedValue([
        {
          id: 'doc-1',
          content: 'Test document 1',
          embedding: new Array(1536).fill(0.1),
          metadata: {},
        },
        {
          id: 'doc-2',
          content: 'Test document 2',
          embedding: new Array(1536).fill(0.2),
          metadata: {},
        },
      ]);
    });

    it('should search for similar documents', async () => {
      const results = await service.searchSimilarDocuments(
        mockQuery,
        mockTenantId,
        mockWorkspaceId,
      );

      expect(mockPrismaService.vector_documents.findMany).toHaveBeenCalledWith({
        where: {
          tenant_id: mockTenantId,
          workspace_id: mockWorkspaceId,
        },
        take: 10,
      });

      expect(results).toHaveLength(2);
      expect(results[0]).toHaveProperty('similarity');
    });

    it('should limit results based on limit parameter', async () => {
      await service.searchSimilarDocuments(mockQuery, mockTenantId, mockWorkspaceId, 5);

      expect(mockPrismaService.vector_documents.findMany).toHaveBeenCalledWith({
        where: {
          tenant_id: mockTenantId,
          workspace_id: mockWorkspaceId,
        },
        take: 5,
      });
    });

    it('should handle search without workspace filter', async () => {
      await service.searchSimilarDocuments(mockQuery, mockTenantId);

      expect(mockPrismaService.vector_documents.findMany).toHaveBeenCalledWith({
        where: {
          tenant_id: mockTenantId,
          workspace_id: undefined,
        },
        take: 10,
      });
    });
  });

  describe('splitTextIntoChunks', () => {
    it('should split text into chunks of specified size', () => {
      const longText = 'This is a long text. '.repeat(100);
      const chunks = service['splitTextIntoChunks'](longText, 100);

      expect(chunks.length).toBeGreaterThan(1);
      chunks.forEach((chunk) => {
        expect(chunk.length).toBeLessThanOrEqual(150); // Some tolerance for sentence boundaries
      });
    });

    it('should return single chunk for short text', () => {
      const shortText = 'This is a short text.';
      const chunks = service['splitTextIntoChunks'](shortText, 1000);

      expect(chunks).toHaveLength(1);
      expect(chunks[0]).toBe(shortText);
    });

    it('should handle empty text', () => {
      const chunks = service['splitTextIntoChunks']('', 1000);

      expect(chunks).toHaveLength(1);
      expect(chunks[0]).toBe('');
    });
  });

  describe('calculateCosineSimilarity', () => {
    it('should calculate cosine similarity correctly', () => {
      const vectorA = [1, 0, 0];
      const vectorB = [0, 1, 0];
      const similarity = service['calculateCosineSimilarity'](vectorA, vectorB);

      expect(similarity).toBe(0);
    });

    it('should return 1 for identical vectors', () => {
      const vector = [1, 2, 3];
      const similarity = service['calculateCosineSimilarity'](vector, vector);

      expect(similarity).toBe(1);
    });

    it('should return 0 for vectors of different lengths', () => {
      const vectorA = [1, 2, 3];
      const vectorB = [1, 2];
      const similarity = service['calculateCosineSimilarity'](vectorA, vectorB);

      expect(similarity).toBe(0);
    });
  });
});
