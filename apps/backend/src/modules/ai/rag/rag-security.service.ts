import { Injectable, Logger, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '@/modules/core/prisma/prisma.service';

/**
 * RAG 安全服務
 * 負責驗證租戶隔離、權限檢查和安全防護
 */
@Injectable()
export class RAGSecurityService {
  private readonly logger = new Logger(RAGSecurityService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 驗證租戶對文件的存取權限
   */
  async validateDocumentAccess(
    documentId: string,
    tenantId: string,
    workspaceId?: string,
  ): Promise<boolean> {
    try {
      const document = await this.prisma.vector_documents.findUnique({
        where: { id: documentId },
        select: {
          tenant_id: true,
          workspace_id: true,
        },
      });

      if (!document) {
        this.logger.warn(`Document not found: ${documentId}`);
        return false;
      }

      // 檢查租戶隔離
      if (document.tenant_id !== tenantId) {
        this.logger.warn(
          `Tenant isolation violation: document ${documentId} belongs to tenant ${document.tenant_id}, but accessed by ${tenantId}`,
        );
        return false;
      }

      // 如果指定了工作區，也要驗證工作區權限
      if (workspaceId && document.workspace_id !== workspaceId) {
        this.logger.warn(
          `Workspace isolation violation: document ${documentId} belongs to workspace ${document.workspace_id}, but accessed by ${workspaceId}`,
        );
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(`Error validating document access: ${error.message}`);
      throw error;
    }
  }

  /**
   * 驗證檔案上傳的安全性
   */
  async validateFileUpload(
    filePath: string,
    tenantId: string,
    fileId: string,
    workspaceId?: string,
  ): Promise<void> {
    // 檢查檔案路徑安全性
    if (this.containsPathTraversal(filePath)) {
      throw new BadRequestException('Invalid file path detected');
    }

    // 檢查租戶是否存在且活躍
    await this.validateTenantExists(tenantId);

    // 如果指定了工作區，檢查工作區是否屬於該租戶
    if (workspaceId) {
      await this.validateWorkspaceAccess(workspaceId, tenantId);
    }

    // 檢查檔案是否已存在
    const existingDocument = await this.prisma.vector_documents.findFirst({
      where: {
        file_id: fileId,
        tenant_id: tenantId,
      },
    });

    if (existingDocument) {
      this.logger.warn(`File ${fileId} already exists for tenant ${tenantId}`);
      throw new BadRequestException('File already indexed');
    }
  }

  /**
   * 驗證搜尋查詢的安全性
   */
  validateSearchQuery(query: string, tenantId: string): void {
    if (!query || query.trim().length === 0) {
      throw new BadRequestException('Search query cannot be empty');
    }

    if (!tenantId || tenantId.trim().length === 0) {
      throw new BadRequestException('Tenant ID is required');
    }

    // 檢查查詢長度限制
    if (query.length > 1000) {
      throw new BadRequestException('Search query too long');
    }

    // 檢查潛在的惡意查詢模式
    if (this.containsSuspiciousPatterns(query)) {
      this.logger.warn(`Suspicious search query detected from tenant ${tenantId}: ${query}`);
      throw new BadRequestException('Invalid search query');
    }
  }

  /**
   * 批量驗證文件的租戶隔離
   */
  async validateBulkDocumentAccess(
    documentIds: string[],
    tenantId: string,
    workspaceId?: string,
  ): Promise<boolean> {
    try {
      const documents = await this.prisma.vector_documents.findMany({
        where: {
          id: { in: documentIds },
        },
        select: {
          id: true,
          tenant_id: true,
          workspace_id: true,
        },
      });

      // 檢查是否所有文件都存在
      if (documents.length !== documentIds.length) {
        this.logger.warn(`Some documents not found in bulk validation`);
        return false;
      }

      // 檢查所有文件的租戶隔離
      const violationCount = documents.filter((doc) => {
        if (doc.tenant_id !== tenantId) return true;
        if (workspaceId && doc.workspace_id !== workspaceId) return true;
        return false;
      }).length;

      if (violationCount > 0) {
        this.logger.warn(
          `Bulk validation failed: ${violationCount} documents violate tenant isolation`,
        );
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(`Error in bulk document validation: ${error.message}`);
      throw error;
    }
  }

  /**
   * 記錄安全事件
   */
  async logSecurityEvent(
    eventType: 'access_violation' | 'suspicious_query' | 'unauthorized_access',
    tenantId: string,
    details: Record<string, any>,
  ): Promise<void> {
    try {
      this.logger.warn(`Security event: ${eventType} for tenant ${tenantId}`, details);

      // 可以在這裡添加到系統日誌表的邏輯
      // await this.prisma.security_logs.create({
      //   data: {
      //     event_type: eventType,
      //     tenant_id: tenantId,
      //     details: details,
      //     timestamp: new Date(),
      //   },
      // });
    } catch (error) {
      this.logger.error(`Failed to log security event: ${error.message}`);
    }
  }

  /**
   * 檢查路徑遍歷攻擊
   */
  private containsPathTraversal(filePath: string): boolean {
    const suspiciousPatterns = ['../', '..\\', '../', '..\\\\'];
    return suspiciousPatterns.some((pattern) => filePath.includes(pattern));
  }

  /**
   * 檢查可疑的查詢模式
   */
  private containsSuspiciousPatterns(query: string): boolean {
    const suspiciousPatterns = [
      /\$\w+/, // 變數注入
      /<script/i, // XSS 攻擊
      /union\s+select/i, // SQL 注入
      /javascript:/i, // JavaScript 協議
      /data:/i, // Data URL
    ];

    return suspiciousPatterns.some((pattern) => pattern.test(query));
  }

  /**
   * 驗證租戶是否存在且活躍
   */
  private async validateTenantExists(tenantId: string): Promise<void> {
    const tenant = await this.prisma.tenants.findUnique({
      where: { id: tenantId },
      select: { id: true, status: true },
    });

    if (!tenant) {
      throw new ForbiddenException('Tenant not found');
    }

    if (tenant.status !== 'ACTIVE') {
      throw new ForbiddenException('Tenant is not active');
    }
  }

  /**
   * 驗證工作區存取權限
   */
  private async validateWorkspaceAccess(workspaceId: string, tenantId: string): Promise<void> {
    const workspace = await this.prisma.workspaces.findUnique({
      where: { id: workspaceId },
      select: { tenant_id: true },
    });

    if (!workspace) {
      throw new ForbiddenException('Workspace not found');
    }

    if (workspace.tenant_id !== tenantId) {
      throw new ForbiddenException('Workspace does not belong to tenant');
    }
  }
}
