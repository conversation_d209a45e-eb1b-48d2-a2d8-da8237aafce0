import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { RAGIngestionService } from './rag-ingestion.service';
import { RAGSecurityService } from './rag-security.service';
import { DocumentProcessorService } from './services/document-processor.service';
import { PrismaModule } from '@/modules/core/prisma/prisma.module';
import { EncryptionModule } from '@/modules/core/encryption/encryption.module';
import { LlmModule } from '../llm/llm.module';

@Module({
  imports: [PrismaModule, EncryptionModule, EventEmitterModule, LlmModule],
  providers: [RAGIngestionService, RAGSecurityService, DocumentProcessorService],
  exports: [RAGIngestionService, RAGSecurityService, DocumentProcessorService],
})
export class RAGModule {}
