import { Module } from '@nestjs/common';
import { PrismaModule } from '../prisma/prisma.module';
import { EncryptionModule } from '../encryption/encryption.module';
import { AuthModule } from '../auth/auth.module';
import { LlmModule } from '../../ai/llm/llm.module';
import { AgentsController } from './agents.controller';
import { AgentsService } from './agents.service';
import { UsageTrackingModule } from '../usage-tracking/usage-tracking.module';
import { CaslModule } from '../../../casl/casl.module';
import { RAGModule } from '../../ai/rag/rag.module';
import { TasksModule } from '../../workspace/tasks/tasks.module';
import { ProgressModule } from '../../workspace/progress/progress.module';
import { NotificationModule } from '../../notification/notification.module';
import { ConfigModule } from '@nestjs/config';
import { ProjectsModule } from '../../workspace/projects/projects.module';
// Import refactored services
import { AgentExecutorService } from './services/agent-executor.service';
import { AgentToolsService } from './services/agent-tools.service';
import { AgentStatusService } from './services/agent-status.service';
import { AgentPromptService } from './services/agent-prompt.service';
import { ToolExecutorService } from './tools/core/tool-executor.service';
// UsageTrackingService moved to core/usage-tracking module
import { ToolsModule } from './tools/tools.module';
import { ModelsModule } from '../../ai/models/models.module';

@Module({
  imports: [
    PrismaModule,
    EncryptionModule,
    AuthModule,
    LlmModule,
    CaslModule,
    RAGModule,
    TasksModule,
    ProgressModule,
    NotificationModule,
    ConfigModule,
    ProjectsModule,
    ToolsModule,
    UsageTrackingModule,
    ModelsModule,
  ],
  controllers: [AgentsController],
  providers: [
    AgentsService,
    // Refactored services
    AgentExecutorService,
    AgentToolsService,
    AgentStatusService,
    AgentPromptService,
    // Legacy services
    ToolExecutorService,
    // UsageTrackingService moved to core/usage-tracking module
  ],
  exports: [AgentsService],
})
export class AgentsModule {}
