import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  Is<PERSON><PERSON>ber,
  IsEnum,
  Min,
  <PERSON>,
  IsBoolean,
  IsNotEmpty,
  IsArray,
  IsObject,
  IsUUID,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { AiAgentProviderType, AiAgentResponseFormat, AiAgentScope } from '@prisma/client';
import { AiMessage } from '@/modules/ai/llm/interfaces/llm-service.interface';

export class CreateAgentDto {
  @ApiProperty({ description: 'Agent 名稱' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({ description: 'Agent 描述' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Agent 範圍', enum: AiAgentScope })
  @IsEnum(AiAgentScope)
  scope: AiAgentScope;

  @ApiProperty({ description: 'AI Provider 類型', enum: AiAgentProviderType })
  @IsEnum(AiAgentProviderType)
  provider_type: AiAgentProviderType;

  @ApiProperty({ description: 'AI Model ID' })
  @IsString()
  @IsNotEmpty()
  model_id: string;

  @ApiProperty({ description: 'API Key ID' })
  @IsString()
  @IsNotEmpty()
  key_id: string;

  @ApiPropertyOptional({ description: 'Provider 配置覆蓋' })
  @IsObject()
  @IsOptional()
  provider_config_override?: Record<string, any>;

  @ApiPropertyOptional({ description: '系統提示詞' })
  @IsString()
  @IsOptional()
  system_prompt?: string;

  @ApiPropertyOptional({ description: '溫度', minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  @IsOptional()
  @Type(() => Number)
  temperature?: number;

  @ApiPropertyOptional({ description: '最大輸出 Token 數' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  max_tokens?: number;

  @ApiPropertyOptional({ description: '回應格式', enum: AiAgentResponseFormat })
  @IsEnum(AiAgentResponseFormat)
  @IsOptional()
  response_format?: AiAgentResponseFormat;

  @ApiPropertyOptional({ description: '是否啟用' })
  @IsBoolean()
  @IsOptional()
  is_enabled?: boolean;

  @ApiPropertyOptional({ description: '是否為範本' })
  @IsBoolean()
  @IsOptional()
  is_template?: boolean;

  @ApiPropertyOptional({ description: '場景' })
  @IsString()
  @IsOptional()
  scene?: string;

  @ApiProperty({ description: '租戶 ID' })
  @IsString()
  @IsNotEmpty()
  tenant_id: string;

  @ApiPropertyOptional({ description: '工作區 ID' })
  @IsString()
  @IsOptional()
  workspace_id?: string;
}

export class UpdateAgentDto {
  @ApiPropertyOptional({ description: 'Agent 名稱' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({ description: 'Agent 描述' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ description: 'AI Model ID' })
  @IsString()
  @IsOptional()
  model_id?: string;

  @ApiPropertyOptional({ description: 'API Key ID' })
  @IsString()
  @IsOptional()
  key_id?: string;

  @ApiPropertyOptional({ description: 'Provider 配置覆蓋' })
  @IsObject()
  @IsOptional()
  provider_config_override?: Record<string, any>;

  @ApiPropertyOptional({ description: '系統提示詞' })
  @IsString()
  @IsOptional()
  system_prompt?: string;

  @ApiPropertyOptional({ description: '溫度', minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  @IsOptional()
  @Type(() => Number)
  temperature?: number;

  @ApiPropertyOptional({ description: '最大輸出 Token 數' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  max_tokens?: number;

  @ApiPropertyOptional({ description: '回應格式', enum: AiAgentResponseFormat })
  @IsEnum(AiAgentResponseFormat)
  @IsOptional()
  response_format?: AiAgentResponseFormat;

  @ApiPropertyOptional({ description: '是否啟用' })
  @IsBoolean()
  @IsOptional()
  is_enabled?: boolean;

  @ApiPropertyOptional({ description: '是否為範本' })
  @IsBoolean()
  @IsOptional()
  is_template?: boolean;

  @ApiPropertyOptional({ description: '場景' })
  @IsString()
  @IsOptional()
  scene?: string;

  @ApiPropertyOptional({ description: '租戶 ID' })
  @IsString()
  @IsOptional()
  tenant_id?: string;

  @ApiPropertyOptional({ description: '工作區 ID' })
  @IsString()
  @IsOptional()
  workspace_id?: string;
}

export class TestAgentDto {
  @ApiProperty({ description: 'Agent ID' })
  @IsUUID()
  @IsNotEmpty()
  agent_id: string;

  @ApiProperty({ description: '測試訊息' })
  @IsString()
  message: string;

  @ApiProperty({ description: '覆寫提示詞' })
  @IsString()
  @IsOptional()
  prompt?: string;

  @ApiProperty({ description: '覆寫溫度' })
  @IsNumber()
  @Min(0)
  @Max(2)
  @IsOptional()
  @Type(() => Number)
  temperature?: number;
}

export class OptimizePromptDto {
  @ApiProperty({ description: 'Agent ID' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'AI Provider',
    enum: ['openai', 'claude', 'openai-compatible'],
  })
  @IsEnum(['openai', 'claude', 'openai-compatible'])
  provider: string;

  @ApiProperty({ description: 'API 金鑰' })
  @IsString()
  api_key: string;

  @ApiProperty({ description: 'API 端點 (僅 OpenAI Compatible 需要)' })
  @IsString()
  @IsOptional()
  api_url?: string;

  @ApiProperty({ description: '要優化的提示詞' })
  @IsString()
  prompt: string;

  @ApiProperty({ description: '場景描述' })
  @IsString()
  @IsOptional()
  scene?: string;

  @ApiProperty({ description: '優化需求' })
  @IsString()
  @IsOptional()
  requirement?: string;
}

export class ExecuteAgentDto {
  @IsArray()
  messages: AiMessage[];

  @IsNumber()
  @IsOptional()
  temperature?: number;
}

export class AssignToolsToAgentDto {
  @ApiProperty({
    description: '要指派給 Agent 的工具 ID 列表',
    type: [String],
    example: ['tool-id-1', 'tool-id-2'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  toolIds: string[];
}

export class AgentToolResponseDto {
  @ApiProperty({ description: '工具 ID' })
  id: string;

  @ApiProperty({ description: '工具標識符' })
  key: string;

  @ApiProperty({ description: '工具名稱' })
  name: string;

  @ApiProperty({ description: '工具描述' })
  description: string;

  @ApiProperty({ description: '是否啟用' })
  is_enabled: boolean;

  @ApiProperty({ description: '指派時間' })
  assigned_at: Date;
}
