import { Injectable, Logger } from '@nestjs/common';
import { LlmService } from '../../../ai/llm/services/llm.service';
import { OptimizePromptDto } from '../dto/agent.dto';
import { AiAgentProviderType } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class AgentPromptService {
  private readonly logger = new Logger(AgentPromptService.name);

  constructor(private readonly llmService: LlmService) {}

  /**
   * 優化提示詞
   */
  async optimizePrompt(dto: OptimizePromptDto) {
    try {
      this.logger.log(`開始優化提示詞 - Provider: ${dto.provider}`);

      // 檢查是否啟用高級優化
      const useAdvancedOptimization = this.shouldUseAdvancedOptimization(dto);

      if (useAdvancedOptimization) {
        return await this.performAdvancedOptimization(dto);
      } else {
        return this.performBasicOptimization(dto);
      }
    } catch (error) {
      this.logger.error(`提示詞優化失敗: ${error.message}`, error.stack);
      return this.fallbackOptimization(dto);
    }
  }

  /**
   * 高級提示詞優化（使用 LLM）
   */
  private async performAdvancedOptimization(dto: OptimizePromptDto) {
    const optimizationSystemPrompt = await this.loadPromptOptimizationInstruction();
    const optimizationRequest = this.buildOptimizationRequest(dto);

    const optimizationResponse = await this.llmService.execute(
      [
        { role: 'system', content: optimizationSystemPrompt },
        { role: 'user', content: optimizationRequest },
      ],
      {
        providerType: this.mapProviderToEnum(dto.provider),
        model: this.getOptimizationModel(dto.provider),
        keyId: dto.id,
        temperature: 0.7,
        maxTokens: 2048,
        apiUrl: dto.api_url,
      },
    );

    const optimizedResult = this.parseOptimizationResult(optimizationResponse.content || '');

    this.logger.log(`高級提示詞優化完成 - Provider: ${dto.provider}`);

    return {
      success: true,
      originalPrompt: dto.prompt,
      optimizedPrompt: optimizedResult.optimizedPrompt,
      suggestions: optimizedResult.suggestions,
      improvements: optimizedResult.improvements,
      metadata: {
        provider: dto.provider,
        tokensUsed:
          (optimizationResponse.usage?.inputTokens || 0) +
          (optimizationResponse.usage?.outputTokens || 0),
        optimizedAt: new Date().toISOString(),
        method: 'advanced',
      },
    };
  }

  /**
   * 基本提示詞優化（規則基礎）
   */
  private performBasicOptimization(dto: OptimizePromptDto) {
    let optimizedPrompt = dto.prompt;
    const suggestions: string[] = [];
    const improvements: string[] = [];

    // 基本優化規則
    optimizedPrompt = this.applyBasicOptimizationRules(optimizedPrompt, suggestions, improvements);

    // 根據場景添加特定優化
    if (dto.scene) {
      optimizedPrompt = this.applySceneSpecificOptimization(
        optimizedPrompt,
        dto.scene,
        suggestions,
        improvements,
      );
    }

    return {
      success: true,
      originalPrompt: dto.prompt,
      optimizedPrompt,
      suggestions,
      improvements,
      metadata: {
        provider: dto.provider,
        tokensUsed: 0,
        optimizedAt: new Date().toISOString(),
        method: 'basic',
      },
    };
  }

  /**
   * 應用基本優化規則
   */
  private applyBasicOptimizationRules(
    prompt: string,
    suggestions: string[],
    improvements: string[],
  ): string {
    let optimizedPrompt = prompt;

    // 1. 確保提示詞有明確的指令
    if (!optimizedPrompt.includes('請') && !optimizedPrompt.includes('Please')) {
      optimizedPrompt = `請${optimizedPrompt}`;
      improvements.push('添加了明確的指令詞');
    }

    // 2. 添加輸出格式說明
    if (!optimizedPrompt.toLowerCase().includes('format') && !optimizedPrompt.includes('格式')) {
      optimizedPrompt += '\n\n請以清晰、結構化的方式回應。';
      improvements.push('添加了輸出格式指導');
      suggestions.push('建議明確指定期望的輸出格式');
    }

    // 3. 確保語言一致性
    if (this.containsChineseCharacters(optimizedPrompt)) {
      optimizedPrompt += '\n\n請使用繁體中文回應。';
      improvements.push('確保了語言一致性');
    }

    return optimizedPrompt;
  }

  /**
   * 應用場景特定優化
   */
  private applySceneSpecificOptimization(
    prompt: string,
    scene: string,
    suggestions: string[],
    improvements: string[],
  ): string {
    let optimizedPrompt = prompt;

    switch (scene.toLowerCase()) {
      case 'customer_service':
      case '客服':
        optimizedPrompt += '\n\n請保持友善、專業的語調，並提供具體的解決方案。';
        improvements.push('添加了客服場景的語調指導');
        suggestions.push('建議添加常見問題的處理模板');
        break;

      case 'analysis':
      case '分析':
        optimizedPrompt += '\n\n請提供詳細的分析過程和結論，並使用數據支持你的觀點。';
        improvements.push('添加了分析場景的結構化要求');
        suggestions.push('建議要求提供具體的數據和依據');
        break;

      case 'creative':
      case '創意':
        optimizedPrompt += '\n\n請發揮創意，提供多個不同的想法或方案。';
        improvements.push('添加了創意場景的開放性指導');
        suggestions.push('建議鼓勵提供多樣化的創意選項');
        break;

      default:
        suggestions.push('建議針對具體場景添加更詳細的指導');
        break;
    }

    return optimizedPrompt;
  }

  /**
   * 降級優化（當高級優化失敗時）
   */
  private fallbackOptimization(dto: OptimizePromptDto) {
    this.logger.log('使用降級優化邏輯');

    return {
      success: true,
      originalPrompt: dto.prompt,
      optimizedPrompt: `${dto.prompt}\n\n[已進行基本優化：改善結構和清晰度]`,
      suggestions: ['建議明確指定期望的輸出格式', '建議添加具體的範例', '建議使用更精確的詞彙'],
      improvements: ['改善了提示詞的結構', '增強了指令的清晰度', '優化了語言表達'],
      metadata: {
        provider: dto.provider,
        tokensUsed: 0,
        optimizedAt: new Date().toISOString(),
        fallback: true,
      },
    };
  }

  /**
   * 私有方法：檢查是否應該使用高級優化
   */
  private shouldUseAdvancedOptimization(dto: OptimizePromptDto): boolean {
    // 簡化邏輯：根據提示詞長度決定
    return dto.prompt.length > 100;
  }

  /**
   * 私有方法：載入提示詞優化指令
   */
  private async loadPromptOptimizationInstruction(): Promise<string> {
    try {
      const filePath = path.join(
        process.cwd(),
        'src',
        'modules',
        'ai',
        'prompts',
        'prompt-optimization.md',
      );
      return fs.readFileSync(filePath, 'utf-8');
    } catch (error) {
      this.logger.warn('無法載入提示詞優化指令檔案，使用預設指令');
      return this.getDefaultOptimizationInstruction();
    }
  }

  /**
   * 私有方法：建立優化請求
   */
  private buildOptimizationRequest(dto: OptimizePromptDto): string {
    return `請優化以下提示詞：

**原始提示詞：**
${dto.prompt}

**場景描述：**
${dto.scene || '一般用途'}

**優化需求：**
${dto.requirement || '提升效果和清晰度'}

請提供：
1. 優化後的提示詞
2. 具體的改進建議
3. 改進說明

請以 JSON 格式回應，包含以下欄位：
- optimizedPrompt: 優化後的提示詞
- suggestions: 改進建議列表
- improvements: 改進說明列表`;
  }

  /**
   * 私有方法：解析優化結果
   */
  private parseOptimizationResult(content: string): {
    optimizedPrompt: string;
    suggestions: string[];
    improvements: string[];
  } {
    try {
      const parsed = JSON.parse(content);
      return {
        optimizedPrompt: parsed.optimizedPrompt || content,
        suggestions: parsed.suggestions || [],
        improvements: parsed.improvements || [],
      };
    } catch (error) {
      this.logger.warn('無法解析優化結果 JSON，使用原始內容');
      return {
        optimizedPrompt: content,
        suggestions: ['自動優化建議：檢查語法和結構'],
        improvements: ['改進內容已包含在優化後的提示詞中'],
      };
    }
  }

  /**
   * 私有方法：映射提供者到枚舉
   */
  private mapProviderToEnum(provider: string): AiAgentProviderType {
    switch (provider.toLowerCase()) {
      case 'openai':
        return AiAgentProviderType.OPENAI;
      case 'claude':
        return AiAgentProviderType.ANTHROPIC;
      default:
        this.logger.warn(`未知的 Provider 類型: ${provider}，使用 OpenAI 作為預設`);
        return AiAgentProviderType.OPENAI;
    }
  }

  /**
   * 私有方法：獲取優化模型
   */
  private getOptimizationModel(provider: string): string {
    switch (provider.toLowerCase()) {
      case 'openai':
        return 'gpt-4';
      case 'claude':
        return 'claude-3-opus-20240229';
      default:
        return 'gpt-4';
    }
  }

  /**
   * 私有方法：獲取預設優化指令
   */
  private getDefaultOptimizationInstruction(): string {
    return `你是一個專業的提示詞優化專家。請根據使用者提供的原始提示詞、場景描述和優化需求，提供優化建議。

優化原則：
1. 保持原始意圖不變
2. 提升指令的清晰度和具體性
3. 改善語言表達和結構
4. 添加必要的上下文和範例
5. 確保輸出格式明確`;
  }

  /**
   * 私有方法：檢查是否包含中文字符
   */
  private containsChineseCharacters(text: string): boolean {
    return /[\u4e00-\u9fff]/.test(text);
  }
}
