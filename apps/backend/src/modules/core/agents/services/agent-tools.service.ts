import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { v4 as uuidv4 } from 'uuid';
import { JwtUser } from '../../../../types/jwt-user.type';

@Injectable()
export class AgentToolsService {
  private readonly logger = new Logger(AgentToolsService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 獲取 Agent 的工具列表
   */
  async getAgentTools(agentId: string): Promise<any[]> {
    try {
      const agentTools = await this.prisma.ai_agent_tools.findMany({
        where: {
          ai_agent_id: agentId,
        },
        include: {
          ai_tool: true,
        },
      });

      return agentTools.map((agentTool) => ({
        id: agentTool.ai_tool.id,
        key: agentTool.ai_tool.key,
        name: agentTool.ai_tool.name,
        description: agentTool.ai_tool.description,
        is_enabled: agentTool.is_enabled,
        assigned_at: agentTool.created_at,
      }));
    } catch (error) {
      this.logger.warn(`Failed to get tools for agent ${agentId}: ${error.message}`);
      return [];
    }
  }

  /**
   * 為 Agent 指派工具
   */
  async assignToolsToAgent(agentId: string, toolIds: string[]): Promise<void> {
    try {
      // 驗證 Agent 存在
      await this.validateAgentExists(agentId);

      const existingAssignments = await this.prisma.ai_agent_tools.findMany({
        where: { ai_agent_id: agentId },
        select: { ai_tool_id: true },
      });

      const existingToolIds = existingAssignments.map((assignment) => assignment.ai_tool_id);

      const toolsToAdd = toolIds.filter((toolId) => !existingToolIds.includes(toolId));

      if (toolsToAdd.length > 0) {
        const assignmentData = toolsToAdd.map((toolId) => ({
          id: uuidv4(),
          ai_agent_id: agentId,
          ai_tool_id: toolId,
          is_enabled: true,
          created_at: new Date(),
          updated_at: new Date(),
        }));

        await this.prisma.ai_agent_tools.createMany({
          data: assignmentData,
          skipDuplicates: true,
        });
      }

      this.logger.log(`已為 Agent ${agentId} 指派 ${toolsToAdd.length} 個新工具`);
    } catch (error) {
      this.logger.error(`Failed to assign tools to agent ${agentId}:`, error);
      throw error;
    }
  }

  /**
   * 從 Agent 移除工具
   */
  async removeToolFromAgent(agentId: string, toolId: string): Promise<void> {
    try {
      await this.validateAgentExists(agentId);

      const deletedAssignment = await this.prisma.ai_agent_tools.deleteMany({
        where: {
          ai_agent_id: agentId,
          ai_tool_id: toolId,
        },
      });

      if (deletedAssignment.count === 0) {
        throw new NotFoundException(
          `Tool assignment not found for Agent ${agentId} and Tool ${toolId}`,
        );
      }

      this.logger.log(`已從 Agent ${agentId} 移除工具 ${toolId}`);
    } catch (error) {
      this.logger.error(`Failed to remove tool from agent ${agentId}:`, error);
      throw error;
    }
  }

  /**
   * 獲取 Agent 工具配置
   */
  async getAgentToolConfigs(
    agentId: string,
    tenantId: string,
  ): Promise<
    Array<{
      id: string;
      key: string;
      name: string;
      description?: string;
      inputSchema?: Record<string, any>;
      isEnabled: boolean;
      config?: Record<string, any>;
    }>
  > {
    try {
      const agentTools = await this.prisma.ai_agent_tools.findMany({
        where: {
          ai_agent_id: agentId,
          ai_tool: {
            OR: [{ scope: 'SYSTEM' }, { scope: 'TENANT', tenant_id: tenantId }],
          },
        },
        include: {
          ai_tool: true,
        },
      });

      return agentTools.map((agentTool) => ({
        id: agentTool.ai_tool.id,
        key: agentTool.ai_tool.key,
        name: agentTool.ai_tool.name,
        description: agentTool.ai_tool.description || undefined,
        inputSchema: (agentTool.ai_tool.input_schema as Record<string, any>) || undefined,
        isEnabled: agentTool.is_enabled,
        config: (agentTool.config as Record<string, any>) || undefined,
      }));
    } catch (error) {
      this.logger.warn(`Failed to get tool configs for agent ${agentId}:`, error);
      return [];
    }
  }

  /**
   * 獲取可用工具
   */
  async getAvailableTools(
    tenantId: string,
    user?: JwtUser,
  ): Promise<Array<{ name: string; description: string }>> {
    try {
      // 查詢資料庫中可用的工具
      const availableTools = await this.prisma.ai_tools.findMany({
        where: {
          OR: [{ scope: 'SYSTEM' }, { scope: 'TENANT', tenant_id: tenantId }],
          is_enabled: true,
        },
        select: {
          key: true,
          name: true,
          description: true,
        },
        orderBy: {
          name: 'asc',
        },
      });

      // 轉換為前端需要的格式
      const toolList = availableTools.map((tool) => ({
        name: tool.key,
        description: tool.description || tool.name,
      }));

      this.logger.debug(`返回 ${toolList.length} 個可用工具給租戶 ${tenantId}`);
      return toolList;
    } catch (error) {
      this.logger.error(`Failed to get available tools for tenant ${tenantId}:`, error);

      // 如果資料庫查詢失敗，返回預設工具列表作為後備方案
      const fallbackTools = [
        {
          name: 'SearchTool',
          description: '搜尋和查找資訊的工具',
        },
        {
          name: 'CalculatorTool',
          description: '執行數學計算的工具',
        },
        {
          name: 'TextAnalysisTool',
          description: '分析和處理文本的工具',
        },
      ];

      this.logger.warn(`使用後備工具列表，返回 ${fallbackTools.length} 個預設工具`);
      return fallbackTools;
    }
  }

  /**
   * 私有方法：驗證 Agent 存在
   */
  private async validateAgentExists(agentId: string): Promise<void> {
    const agent = await this.prisma.ai_agents.findUnique({
      where: { id: agentId },
      select: { id: true },
    });

    if (!agent) {
      throw new NotFoundException(`Agent with ID ${agentId} not found`);
    }
  }
}
