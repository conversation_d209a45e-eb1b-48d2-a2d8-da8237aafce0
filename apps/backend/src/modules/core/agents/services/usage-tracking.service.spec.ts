import { Test, TestingModule } from '@nestjs/testing';
import { UsageTrackingService } from './usage-tracking.service';
import { PrismaService } from '../../core/prisma/prisma.service';
import { AiModelsService } from '../../ai/models/configuration/models/ai-models.service';
import { Decimal } from '@prisma/client/runtime/library';

describe('UsageTrackingService', () => {
  let service: UsageTrackingService;
  let prismaService: jest.Mocked<PrismaService>;
  let aiModelsService: jest.Mocked<AiModelsService>;

  const mockModel = {
    id: 'model-123',
    provider: 'openai',
    model_name: 'gpt-4',
    input_price_per_1k_tokens: new Decimal('0.03'),
    output_price_per_1k_tokens: new Decimal('0.06'),
    is_enabled: true,
  };

  const mockUsageData = {
    tenantId: 'tenant-123',
    userId: 'user-123',
    botId: 'bot-123',
    featureKey: 'agent_execution',
    apiKeyId: 'key-123',
    provider: 'openai',
    modelName: 'gpt-4',
    inputTokens: 1000,
    outputTokens: 500,
    callCount: 1,
    isSuccess: true,
    requestTimestamp: new Date(),
    responseTimestamp: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsageTrackingService,
        {
          provide: PrismaService,
          useValue: {
            ai_usage_logs: {
              create: jest.fn(),
              aggregate: jest.fn(),
              groupBy: jest.fn(),
            },
            ai_models: {
              findFirst: jest.fn(),
            },
            ai_global_settings: {
              findFirst: jest.fn(),
            },
          },
        },
        {
          provide: AiModelsService,
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<UsageTrackingService>(UsageTrackingService);
    prismaService = module.get(PrismaService) as jest.Mocked<PrismaService>;
    aiModelsService = module.get(AiModelsService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('logUsage', () => {
    beforeEach(() => {
      (prismaService.ai_models.findFirst as jest.Mock).mockResolvedValue(mockModel);
      (prismaService.ai_usage_logs.create as jest.Mock).mockResolvedValue({} as any);
    });

    it('should successfully log usage with cost calculation', async () => {
      await service.logUsage(mockUsageData);

      expect(prismaService.ai_models.findFirst).toHaveBeenCalledWith({
        where: {
          provider: 'openai',
          model_name: 'gpt-4',
          is_enabled: true,
        },
      });

      expect(prismaService.ai_usage_logs.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          tenant_id: 'tenant-123',
          user_id: 'user-123',
          bot_id: 'bot-123',
          feature_key: 'agent_execution',
          api_key_id: 'key-123',
          provider: 'openai',
          model_name: 'gpt-4',
          input_tokens: 1000,
          output_tokens: 500,
          call_count: 1,
          estimated_cost: expect.any(Decimal),
          is_success: true,
        }),
      });
    });

    it('should calculate cost correctly', async () => {
      await service.logUsage(mockUsageData);

      const createCall = (prismaService.ai_usage_logs.create as jest.Mock).mock.calls[0][0];
      const estimatedCost = Number(createCall.data.estimated_cost);

      // 計算預期成本: (1000/1000 * 0.03) + (500/1000 * 0.06) = 0.03 + 0.03 = 0.06
      expect(estimatedCost).toBeCloseTo(0.06, 4);
    });

    it('should handle model not found', async () => {
      (prismaService.ai_models.findFirst as jest.Mock).mockResolvedValue(null);

      await service.logUsage(mockUsageData);

      const createCall = (prismaService.ai_usage_logs.create as jest.Mock).mock.calls[0][0];
      const estimatedCost = Number(createCall.data.estimated_cost);

      expect(estimatedCost).toBe(0);
    });

    it('should handle database errors gracefully', async () => {
      (prismaService.ai_models.findFirst as jest.Mock).mockRejectedValue(
        new Error('Database error'),
      );

      // Should not throw error
      await expect(service.logUsage(mockUsageData)).resolves.not.toThrow();
    });

    it('should handle usage log creation errors gracefully', async () => {
      (prismaService.ai_usage_logs.create as jest.Mock).mockRejectedValue(
        new Error('Insert error'),
      );

      // Should not throw error
      await expect(service.logUsage(mockUsageData)).resolves.not.toThrow();
    });

    it('should use default call count when not provided', async () => {
      const dataWithoutCallCount = { ...mockUsageData };
      delete (dataWithoutCallCount as any).callCount;

      await service.logUsage(dataWithoutCallCount);

      const createCall = (prismaService.ai_usage_logs.create as jest.Mock).mock.calls[0][0];
      expect(createCall.data.call_count).toBe(1);
    });
  });

  describe('checkQuotaLimits', () => {
    const mockMonthlyUsage = {
      _sum: {
        call_count: 100,
        input_tokens: 10000,
        output_tokens: 5000,
        estimated_cost: new Decimal('15.50'),
      },
    };

    const mockGlobalSettings = {
      global_monthly_quota_calls: new Decimal('1000'),
      global_monthly_quota_tokens: new Decimal('50000'),
    };

    beforeEach(() => {
      (prismaService.ai_usage_logs.aggregate as jest.Mock).mockResolvedValue(mockMonthlyUsage);
      (prismaService.ai_global_settings.findFirst as jest.Mock).mockResolvedValue(
        mockGlobalSettings,
      );
    });

    it('should return within limits when usage is below quota', async () => {
      const result = await service.checkQuotaLimits('tenant-123');

      expect(result.withinLimits).toBe(true);
      expect(result.monthlyUsage.totalCalls).toBe(100);
      expect(result.monthlyUsage.totalTokens).toBe(15000);
      expect(result.monthlyUsage.totalCost).toBe(15.5);
      expect(result.limits.monthlyCalls).toBe(1000);
      expect(result.limits.monthlyTokens).toBe(50000);
    });

    it('should return exceeds limits when calls quota is exceeded', async () => {
      const highUsage = {
        _sum: {
          call_count: 1500,
          input_tokens: 10000,
          output_tokens: 5000,
          estimated_cost: new Decimal('15.50'),
        },
      };
      (prismaService.ai_usage_logs.aggregate as jest.Mock).mockResolvedValue(highUsage);

      const result = await service.checkQuotaLimits('tenant-123');

      expect(result.withinLimits).toBe(false);
      expect(result.monthlyUsage.totalCalls).toBe(1500);
    });

    it('should return exceeds limits when tokens quota is exceeded', async () => {
      const highTokenUsage = {
        _sum: {
          call_count: 100,
          input_tokens: 40000,
          output_tokens: 20000,
          estimated_cost: new Decimal('15.50'),
        },
      };
      (prismaService.ai_usage_logs.aggregate as jest.Mock).mockResolvedValue(highTokenUsage);

      const result = await service.checkQuotaLimits('tenant-123');

      expect(result.withinLimits).toBe(false);
      expect(result.monthlyUsage.totalTokens).toBe(60000);
    });

    it('should handle null usage data', async () => {
      const nullUsage = {
        _sum: {
          call_count: null,
          input_tokens: null,
          output_tokens: null,
          estimated_cost: null,
        },
      };
      (prismaService.ai_usage_logs.aggregate as jest.Mock).mockResolvedValue(nullUsage);

      const result = await service.checkQuotaLimits('tenant-123');

      expect(result.monthlyUsage.totalCalls).toBe(0);
      expect(result.monthlyUsage.totalTokens).toBe(0);
      expect(result.monthlyUsage.totalCost).toBe(0);
    });

    it('should handle null global settings', async () => {
      (prismaService.ai_global_settings.findFirst as jest.Mock).mockResolvedValue(null);

      const result = await service.checkQuotaLimits('tenant-123');

      expect(result.limits.monthlyCalls).toBe(0);
      expect(result.limits.monthlyTokens).toBe(0);
    });

    it('should handle database errors gracefully', async () => {
      (prismaService.ai_usage_logs.aggregate as jest.Mock).mockRejectedValue(
        new Error('Database error'),
      );

      await expect(service.checkQuotaLimits('tenant-123')).rejects.toThrow('Database error');
    });

    it('should use correct date range for monthly usage', async () => {
      await service.checkQuotaLimits('tenant-123');

      const aggregateCall = (prismaService.ai_usage_logs.aggregate as jest.Mock).mock.calls[0][0];
      expect(aggregateCall.where.tenant_id).toBe('tenant-123');
      expect(aggregateCall.where.created_at).toHaveProperty('gte');
      expect(aggregateCall.where.created_at).toHaveProperty('lt');
    });
  });

  describe('getUsageStats', () => {
    const mockTotalStats = {
      _sum: {
        call_count: 1000,
        input_tokens: 50000,
        output_tokens: 25000,
        estimated_cost: new Decimal('150.00'),
      },
    };

    const mockModelStats = [
      {
        model_name: 'gpt-4',
        _sum: {
          call_count: 500,
          input_tokens: 25000,
          output_tokens: 12500,
          estimated_cost: new Decimal('75.00'),
        },
      },
      {
        model_name: 'gpt-3.5-turbo',
        _sum: {
          call_count: 500,
          input_tokens: 25000,
          output_tokens: 12500,
          estimated_cost: new Decimal('75.00'),
        },
      },
    ];

    beforeEach(() => {
      (prismaService.ai_usage_logs.aggregate as jest.Mock).mockResolvedValue(mockTotalStats);
      (prismaService.ai_usage_logs.groupBy as jest.Mock)
        .mockResolvedValueOnce(mockModelStats)
        .mockResolvedValueOnce([
          { is_success: true, _count: { _all: 800 } },
          { is_success: false, _count: { _all: 200 } },
        ]);
    });

    it('should return comprehensive usage statistics', async () => {
      const stats = await service.getUsageStats('tenant-123');

      expect(stats.totalExecutions).toBe(1000);
      expect(stats.successfulExecutions).toBe(800);
      expect(stats.failedExecutions).toBe(200);
      expect(stats.averageExecutionTime).toBe(0);
      expect(stats.totalTokensUsed).toBe(75000);
      expect(stats.errorBreakdown).toEqual([
        { is_success: true, _count: { _all: 800 } },
        { is_success: false, _count: { _all: 200 } },
      ]);
    });

    it('should use correct date range for stats', async () => {
      await service.getUsageStats('tenant-123');

      const aggregateCall = (prismaService.ai_usage_logs.aggregate as jest.Mock).mock.calls[0][0];
      expect(aggregateCall.where.tenant_id).toBe('tenant-123');
      expect(aggregateCall.where.created_at).toHaveProperty('gte');
      expect(aggregateCall.where.created_at).toHaveProperty('lt');
    });

    it('should handle date range filter', async () => {
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-12-31');

      (prismaService.ai_usage_logs.groupBy as jest.Mock)
        .mockResolvedValueOnce(mockModelStats)
        .mockResolvedValueOnce([
          { is_success: true, _count: { _all: 800 } },
          { is_success: false, _count: { _all: 200 } },
        ]);

      await service.getUsageStats('tenant-123', startDate, endDate);

      const aggregateCall = (prismaService.ai_usage_logs.aggregate as jest.Mock).mock.calls[0][0];
      expect(aggregateCall.where.created_at.gte).toBe(startDate);
      expect(aggregateCall.where.created_at.lt).toBe(endDate);
    });

    it('should handle zero success rate', async () => {
      const zeroSuccessStats = {
        _sum: {
          call_count: 100,
          input_tokens: 5000,
          output_tokens: 2500,
          estimated_cost: new Decimal('15.00'),
        },
      };
      (prismaService.ai_usage_logs.aggregate as jest.Mock).mockResolvedValue(zeroSuccessStats);

      const stats = await service.getUsageStats('tenant-123');

      expect(stats.successRate).toBe(0);
    });

    it('should handle null stats', async () => {
      const nullStats = {
        _sum: {
          call_count: null,
          input_tokens: null,
          output_tokens: null,
          estimated_cost: null,
        },
      };
      (prismaService.ai_usage_logs.aggregate as jest.Mock).mockResolvedValue(nullStats);

      const stats = await service.getUsageStats('tenant-123');

      expect(stats.totalExecutions).toBe(0);
      expect(stats.totalTokensUsed).toBe(0);
    });

    it('should handle database errors', async () => {
      (prismaService.ai_usage_logs.aggregate as jest.Mock).mockRejectedValue(
        new Error('Database error'),
      );

      await expect(service.getUsageStats('tenant-123')).rejects.toThrow('Database error');
    });
  });

  describe('calculateCost', () => {
    beforeEach(() => {
      (prismaService.ai_models.findFirst as jest.Mock).mockResolvedValue(mockModel);
    });

    it('should calculate cost correctly for known model', async () => {
      const cost = await service.calculateCost('openai', 'gpt-4', 1000, 500);

      expect(cost).toBeCloseTo(0.06, 4);
    });

    it('should return 0 for unknown model', async () => {
      (prismaService.ai_models.findFirst as jest.Mock).mockResolvedValue(null);

      const cost = await service.calculateCost('unknown', 'unknown-model', 1000, 500);

      expect(cost).toBe(0);
    });

    it('should handle database errors gracefully', async () => {
      (prismaService.ai_models.findFirst as jest.Mock).mockRejectedValue(
        new Error('Database error'),
      );

      const cost = await service.calculateCost('openai', 'gpt-4', 1000, 500);

      expect(cost).toBe(0);
    });
  });
});
