# Agent Tools

此目錄包含所有 LangChain Agent 可使用的工具，按功能模組分類組織。

## 目錄結構

```
tools/
├── index.ts                    # 統一導出所有工具
├── project/                    # 專案相關工具
│   ├── project-info.tool.ts
│   ├── project-info-tool.factory.ts
│   └── *.spec.ts
├── knowledge/                  # 知識庫相關工具
│   └── (未來實作)
├── task/                      # 任務相關工具
│   └── (未來實作)
└── README.md                  # 本檔案
```

## 設計原則

### 1. 模組化組織
- 每個功能領域有獨立的目錄
- 相關工具集中管理
- 清晰的職責分離

### 2. 工廠模式
- 每個工具都有對應的工廠類別
- 支援動態建立和依賴注入
- 多租戶上下文隔離

### 3. 統一介面
- 所有工具繼承自 LangChain `Tool` 基礎類別
- 一致的錯誤處理機制
- 標準化的輸入/輸出格式

### 4. 測試覆蓋
- 每個工具都有對應的單元測試
- 工廠類別的測試
- 整合測試確保系統協作

## 使用方式

### 導入工具
```typescript
import { ProjectInfoToolFactory, ToolCategory, TOOL_REGISTRY } from './tools';
```

### 建立工具實例
```typescript
const projectInfoTool = projectInfoToolFactory.create(tenantId);
```

### 註冊到 Agent
```typescript
const tools = [
  projectInfoToolFactory.create(tenantId),
  // 其他工具...
];
```

## 開發指南

### 新增工具

1. **選擇適當的模組目錄**
   - 專案相關 → `project/`
   - 知識庫相關 → `knowledge/`
   - 任務相關 → `task/`

2. **建立工具類別**
   ```typescript
   export class NewTool extends Tool {
     name = 'NewTool';
     description = '工具描述';
     
     async _call(input: string): Promise<string> {
       // 實作邏輯
     }
   }
   ```

3. **建立工廠類別**
   ```typescript
   @Injectable()
   export class NewToolFactory {
     create(tenantId: string): NewTool {
       return new NewTool(dependencies, tenantId);
     }
   }
   ```

4. **撰寫測試**
   - 工具功能測試
   - 工廠建立測試
   - 錯誤處理測試

5. **更新導出**
   - 在 `index.ts` 中添加導出
   - 更新 `TOOL_REGISTRY`
   - 更新型別定義

### 最佳實踐

- **多租戶隔離**: 所有資料操作都要基於 `tenantId`
- **錯誤處理**: 捕獲所有異常並轉換為用戶友好的訊息
- **日誌記錄**: 記錄關鍵操作和錯誤資訊
- **輸入驗證**: 驗證和清理用戶輸入
- **輸出格式**: 提供結構化、易於理解的輸出

## 當前狀態

### ✅ 已實作
- **ProjectInfoTool**: 專案資訊查詢工具
- 模組化目錄結構
- 統一導出機制
- 完整測試覆蓋

### 🚧 進行中
- RAGQueryTool (知識庫查詢)

### 📋 計劃中
- TaskManagementTool (任務管理)
- DocumentAnalysisTool (文件分析)
- ProjectCreationTool (專案建立)
- TaskAutomationTool (任務自動化)
