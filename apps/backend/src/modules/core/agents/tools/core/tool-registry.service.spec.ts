import { Test, TestingModule } from '@nestjs/testing';
import { ToolRegistryService } from './tool-registry.service';
import { ToolImplementation, ToolExecutionContext, ToolConfig } from './tool-registry.interface';
import { Tool } from 'langchain/tools';
import { DynamicStructuredTool } from 'langchain/tools';
import { z } from 'zod';

// Mock 工具實作
class MockToolImplementation implements ToolImplementation {
  constructor(
    public readonly key: string,
    public readonly name: string,
    public readonly description: string,
  ) {}

  async createTool(context: ToolExecutionContext, config: ToolConfig): Promise<Tool> {
    return new DynamicStructuredTool({
      name: this.key,
      description: this.description,
      schema: z.object({
        input: z.string().describe('Test input'),
      }),
      func: async ({ input }) => `Mock response for: ${input}`,
    });
  }

  async validateConfig(config: ToolConfig): Promise<boolean> {
    return config.key === this.key;
  }

  async checkPermissions(context: ToolExecutionContext): Promise<boolean> {
    return true;
  }
}

describe('ToolRegistryService', () => {
  let service: ToolRegistryService;
  let mockTool1: MockToolImplementation;
  let mockTool2: MockToolImplementation;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ToolRegistryService],
    }).compile();

    service = module.get<ToolRegistryService>(ToolRegistryService);

    // 建立 mock 工具
    mockTool1 = new MockToolImplementation('test_tool_1', 'Test Tool 1', 'First test tool');
    mockTool2 = new MockToolImplementation('test_tool_2', 'Test Tool 2', 'Second test tool');
  });

  afterEach(() => {
    // 清理註冊表
    service.unregister(mockTool1.key);
    service.unregister(mockTool2.key);
  });

  describe('register', () => {
    it('should register a tool implementation', () => {
      service.register(mockTool1);

      expect(service.hasImplementation(mockTool1.key)).toBe(true);
      expect(service.getImplementation(mockTool1.key)).toBe(mockTool1);
    });

    it('should overwrite existing tool with warning', () => {
      const loggerSpy = jest.spyOn(service['logger'], 'warn');

      service.register(mockTool1);
      service.register(mockTool1); // 重複註冊

      expect(loggerSpy).toHaveBeenCalledWith(
        expect.stringContaining('工具 "test_tool_1" 已存在，將被覆蓋'),
      );
    });
  });

  describe('unregister', () => {
    it('should unregister a tool implementation', () => {
      service.register(mockTool1);
      expect(service.hasImplementation(mockTool1.key)).toBe(true);

      service.unregister(mockTool1.key);
      expect(service.hasImplementation(mockTool1.key)).toBe(false);
    });

    it('should warn when unregistering non-existent tool', () => {
      const loggerSpy = jest.spyOn(service['logger'], 'warn');

      service.unregister('non_existent_tool');

      expect(loggerSpy).toHaveBeenCalledWith(expect.stringContaining('嘗試移除不存在的工具'));
    });
  });

  describe('getImplementation', () => {
    it('should return registered tool implementation', () => {
      service.register(mockTool1);

      const implementation = service.getImplementation(mockTool1.key);
      expect(implementation).toBe(mockTool1);
    });

    it('should return undefined for non-existent tool', () => {
      const implementation = service.getImplementation('non_existent_tool');
      expect(implementation).toBeUndefined();
    });
  });

  describe('hasImplementation', () => {
    it('should return true for registered tool', () => {
      service.register(mockTool1);
      expect(service.hasImplementation(mockTool1.key)).toBe(true);
    });

    it('should return false for non-registered tool', () => {
      expect(service.hasImplementation('non_existent_tool')).toBe(false);
    });
  });

  describe('listTools', () => {
    it('should return empty array when no tools registered', () => {
      const tools = service.listTools();
      expect(tools).toEqual([]);
    });

    it('should return list of registered tool keys', () => {
      service.register(mockTool1);
      service.register(mockTool2);

      const tools = service.listTools();
      expect(tools).toContain(mockTool1.key);
      expect(tools).toContain(mockTool2.key);
      expect(tools).toHaveLength(2);
    });
  });

  describe('getAllImplementations', () => {
    it('should return empty map when no tools registered', () => {
      const implementations = service.getAllImplementations();
      expect(implementations.size).toBe(0);
    });

    it('should return map of all registered implementations', () => {
      service.register(mockTool1);
      service.register(mockTool2);

      const implementations = service.getAllImplementations();
      expect(implementations.size).toBe(2);
      expect(implementations.get(mockTool1.key)).toBe(mockTool1);
      expect(implementations.get(mockTool2.key)).toBe(mockTool2);
    });
  });

  describe('clear', () => {
    it('should clear all registered tools', () => {
      service.register(mockTool1);
      service.register(mockTool2);
      expect(service.listTools()).toHaveLength(2);

      service.clear();
      expect(service.listTools()).toHaveLength(0);
    });
  });
});
