import { FileReaderToolsFactory, createFileReaderTool, FileReaderConfig } from './file-reader-tools.factory';
import { ToolExecutionContext } from '../core/tool-registry.interface';
import { FileOperationError } from '../core/tool.errors';
import * as fs from 'fs/promises';
import * as path from 'path';

// Mock fs module
jest.mock('fs/promises');
const mockFs = fs as jest.Mocked<typeof fs>;

describe('FileReaderToolsFactory', () => {
  let mockContext: ToolExecutionContext;

  beforeEach(() => {
    mockContext = {
      tenantId: 'test-tenant-id',
      userId: 'test-user-id',
      workspaceId: 'test-workspace-id',
      workspaceRoot: '/test/workspace',
      ability: {} as any,
      user: {} as any,
    };

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('createFileReaderTool', () => {
    it('should create a file reader tool with correct configuration', () => {
      const tool = createFileReaderTool(mockContext);

      expect(tool).toBeDefined();
      expect(tool.name).toBe('file_reader');
      expect(tool.description).toContain('檔案讀取工具');
      expect(tool.schema).toBeDefined();
      expect(tool.call).toBeDefined();
    });

    it('should create tool with custom configuration', () => {
      const config: FileReaderConfig = { maxFileSizeMB: 5 };
      const tool = createFileReaderTool(mockContext, config);

      expect(tool.description).toContain('5MB');
    });

    it('should successfully read a valid file', async () => {
      const fileContent = 'Test file content';
      const mockStats = { isFile: () => true, size: 1024 };
      
      mockFs.stat.mockResolvedValue(mockStats as any);
      mockFs.readFile.mockResolvedValue(fileContent);

      const tool = createFileReaderTool(mockContext);
      const result = await tool.call({ filePath: 'test.txt' });

      expect(result).toBe(fileContent);
      expect(mockFs.readFile).toHaveBeenCalledWith(
        path.resolve(mockContext.workspaceRoot, 'test.txt'),
        { encoding: 'utf8' }
      );
    });

    it('should handle custom encoding', async () => {
      const fileContent = 'Test file content';
      const mockStats = { isFile: () => true, size: 1024 };
      
      mockFs.stat.mockResolvedValue(mockStats as any);
      mockFs.readFile.mockResolvedValue(fileContent);

      const tool = createFileReaderTool(mockContext);
      await tool.call({ filePath: 'test.txt', encoding: 'utf16le' });

      expect(mockFs.readFile).toHaveBeenCalledWith(
        path.resolve(mockContext.workspaceRoot, 'test.txt'),
        { encoding: 'utf16le' }
      );
    });

    it('should throw error for path traversal attempt', async () => {
      const tool = createFileReaderTool(mockContext);

      await expect(tool.call({ filePath: '../secret.txt' }))
        .rejects
        .toThrow(FileOperationError);
    });

    it('should throw error for home directory access', async () => {
      const tool = createFileReaderTool(mockContext);

      await expect(tool.call({ filePath: '~/secret.txt' }))
        .rejects
        .toThrow(FileOperationError);
    });

    it('should throw error for non-existent file', async () => {
      const error = new Error('ENOENT');
      (error as any).code = 'ENOENT';
      mockFs.stat.mockRejectedValue(error);

      const tool = createFileReaderTool(mockContext);

      await expect(tool.call({ filePath: 'nonexistent.txt' }))
        .rejects
        .toThrow(FileOperationError);
    });

    it('should throw error for directory instead of file', async () => {
      const mockStats = { isFile: () => false, size: 0 };
      mockFs.stat.mockResolvedValue(mockStats as any);

      const tool = createFileReaderTool(mockContext);

      await expect(tool.call({ filePath: 'directory' }))
        .rejects
        .toThrow(FileOperationError);
    });

    it('should throw error for file too large', async () => {
      const mockStats = { isFile: () => true, size: 20 * 1024 * 1024 }; // 20MB
      mockFs.stat.mockResolvedValue(mockStats as any);

      const config: FileReaderConfig = { maxFileSizeMB: 10 };
      const tool = createFileReaderTool(mockContext, config);

      await expect(tool.call({ filePath: 'large.txt' }))
        .rejects
        .toThrow(FileOperationError);
    });
  });

  describe('FileReaderToolsFactory static methods', () => {
    it('should create file reader tool via static method', () => {
      const tool = FileReaderToolsFactory.createFileReaderTool(mockContext);
      
      expect(tool).toBeDefined();
      expect(tool.name).toBe('file_reader');
    });

    it('should create LangChain tools array', () => {
      const tools = FileReaderToolsFactory.createLangChainTools(mockContext);
      
      expect(Array.isArray(tools)).toBe(true);
      expect(tools).toHaveLength(1);
      expect(tools[0].name).toBe('file_reader');
    });

    it('should create tools with custom config', () => {
      const config: FileReaderConfig = { maxFileSizeMB: 5 };
      const tools = FileReaderToolsFactory.createLangChainTools(mockContext, config);
      
      expect(tools[0].description).toContain('5MB');
    });
  });
}); 