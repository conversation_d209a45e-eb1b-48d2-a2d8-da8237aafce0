import { z } from 'zod';
import * as fs from 'fs/promises';
import * as path from 'path';
import { ToolExecutionContext, ToolConfig } from '../core/tool-registry.interface';
import { FileOperationError } from '../core/tool.errors';

const DEFAULT_MAX_FILE_SIZE_MB = 10;

export interface FileReaderConfig {
  maxFileSizeMB?: number;
}

export interface FileReadInput {
  filePath: string;
  encoding?: string;
}

/**
 * 創建檔案讀取工具
 */
export function createFileReaderTool(context: ToolExecutionContext, config?: FileReaderConfig) {
  const inputSchema = z.object({
    filePath: z.string().min(1).describe('要讀取的檔案路徑'),
    encoding: z
      .string()
      .optional()
      .default('utf8')
      .describe('檔案編碼，預設為 utf8'),
  });

  return {
    name: 'file_reader',
    description: `檔案讀取工具 - 讀取指定路徑的檔案內容，支援文字檔案格式

此工具可以安全地讀取工作空間內的檔案內容。

功能特色：
- 支援多種文字編碼格式
- 自動檢測和防止路徑遍歷攻擊
- 檔案大小限制保護
- 詳細的錯誤處理和日誌

使用方式：
- filePath: 檔案的相對或絕對路徑
- encoding: 檔案編碼（可選，預設 utf8）

安全限制：
- 只能存取工作空間內的檔案
- 檔案大小限制：${config?.maxFileSizeMB || DEFAULT_MAX_FILE_SIZE_MB}MB
- 不允許路徑遍歷（../ 等）`,
    schema: inputSchema,
    call: async (input: FileReadInput) => {
      try {
        const fileInput = {
          filePath: input.filePath,
          encoding: input.encoding || 'utf8',
        };

        // 1. 解析和驗證檔案路徑
        const resolvedPath = await resolveAndValidatePath(fileInput.filePath, context);
        
        // 2. 檢查檔案是否存在
        await ensureFileExists(resolvedPath);
        
        // 3. 檢查檔案大小
        await validateFileSize(resolvedPath, config);
        
        // 4. 讀取檔案內容
        const content = await fs.readFile(resolvedPath, { 
          encoding: fileInput.encoding as BufferEncoding || 'utf8' 
        });
        
        return content;
      } catch (error: any) {
        if (error instanceof FileOperationError) {
          throw error;
        }
        
        throw new FileOperationError(
          'file_reader',
          input.filePath,
          'read',
          new Error(`無法讀取檔案 ${input.filePath}: ${error.message}`),
        );
      }
    },
  };
}

async function resolveAndValidatePath(filePath: string, context: ToolExecutionContext): Promise<string> {
  // 移除開頭的 ./ 或 / 
  const cleanPath = filePath.replace(/^\.?\//, '');
  
  // 檢查路徑是否包含危險字符
  if (cleanPath.includes('..') || cleanPath.includes('~')) {
    throw new FileOperationError(
      'file_reader',
      filePath,
      'validate',
      new Error(`不安全的檔案路徑: ${filePath}. 不允許使用相對路徑 (..) 或家目錄 (~) 引用`),
    );
  }
  
  // 建構完整路徑
  const fullPath = path.resolve(context.workspaceRoot, cleanPath);
  
  // 確保解析後的路徑仍在工作空間內
  if (!fullPath.startsWith(path.resolve(context.workspaceRoot))) {
    throw new FileOperationError(
      'file_reader',
      filePath,
      'validate',
      new Error(`檔案路徑超出工作空間範圍: ${filePath}`),
    );
  }
  
  return fullPath;
}

async function ensureFileExists(filePath: string): Promise<void> {
  try {
    const stats = await fs.stat(filePath);
    if (!stats.isFile()) {
      throw new FileOperationError(
        'file_reader',
        filePath,
        'stat',
        new Error(`指定的路徑不是檔案: ${filePath}`),
      );
    }
  } catch (error: any) {
    if (error.code === 'ENOENT') {
      throw new FileOperationError(
        'file_reader',
        filePath,
        'read',
        new Error(`檔案不存在: ${filePath}`),
      );
    }
    if (error instanceof FileOperationError) {
      throw error;
    }
    throw new FileOperationError(
      'file_reader',
      filePath,
      'stat',
      new Error(`無法存取檔案: ${filePath}`),
    );
  }
}

async function validateFileSize(filePath: string, config?: FileReaderConfig): Promise<void> {
  const stats = await fs.stat(filePath);
  const maxSizeBytes = (config?.maxFileSizeMB || DEFAULT_MAX_FILE_SIZE_MB) * 1024 * 1024;
  
  if (stats.size > maxSizeBytes) {
    throw new FileOperationError(
      'file_reader',
      filePath,
      'validate',
      new Error(`檔案太大: ${(stats.size / 1024 / 1024).toFixed(2)}MB，超過限制 ${config?.maxFileSizeMB || DEFAULT_MAX_FILE_SIZE_MB}MB`),
    );
  }
}

/**
 * 檔案讀取工具Factory類別
 */
export class FileReaderToolsFactory {
  static createFileReaderTool(context: ToolExecutionContext, config?: FileReaderConfig) {
    return createFileReaderTool(context, config);
  }

  static createLangChainTools(context: ToolExecutionContext, config?: FileReaderConfig) {
    return [createFileReaderTool(context, config)];
  }
} 