import { NotificationTool } from './notification.tool';
import { ToolExecutionContext } from '../core/tool-registry.interface';

// Mock services
const mockNotificationService = {
  sendMessage: jest.fn(),
  createNotification: jest.fn(),
};

const mockMessageCenterService = {
  createNotification: jest.fn(),
  getNotifications: jest.fn(),
  markNotificationAsRead: jest.fn(),
};

describe('NotificationTool - Functional Tests', () => {
  let tool: NotificationTool;

  beforeEach(() => {
    tool = new NotificationTool(mockNotificationService as any, mockMessageCenterService as any);

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('Tool Configuration', () => {
    it('should return valid tool configuration', () => {
      const config = tool.getConfig();

      expect(config).toMatchObject({
        name: 'notification_tool',
        displayName: 'Notification Tool',
        category: 'communication',
        isEnabled: true,
        version: '1.0.0',
        author: 'HorizAI',
      });

      expect(config.description).toContain('Comprehensive notification management tool');
      expect(config.tags).toContain('notification');
      expect(config.tags).toContain('messaging');
      expect(config.requiredPermissions).toEqual(['notification:send', 'notification:read']);
    });

    it('should have valid input schema', () => {
      const config = tool.getConfig();
      expect(config.inputSchema).toBeDefined();
    });
  });

  describe('Send Message Operation', () => {
    const baseContext: ToolExecutionContext = {
      tenantId: 'tenant-123',
      userId: 'user-456',
      workspaceId: 'workspace-789',
      ability: {} as any,
      user: {} as any,
    };

    it('should send internal message successfully', async () => {
      const context = {
        ...baseContext,
        input: {
          operation: 'send_message',
          channel: 'internal',
          recipient_type: 'user',
          recipient_id: 'recipient-123',
          message: 'Hello from functional test!',
        },
      };

      mockNotificationService.sendMessage.mockResolvedValue(undefined);

      const result = await tool.execute(context);

      expect(result.success).toBe(true);
      expect(result.data?.success).toBe(true);
      expect(result.data?.message).toContain('Successfully sent message');
      expect(result.metadata?.operation).toBe('send_message');
      expect(result.metadata?.tenantId).toBe('tenant-123');

      expect(mockNotificationService.sendMessage).toHaveBeenCalledWith(
        {
          channel: 'internal',
          recipient_type: 'user',
          recipient_id: 'recipient-123',
          message: 'Hello from functional test!',
          message_type: undefined,
        },
        'tenant-123',
        'user-456',
      );
    });

    it('should handle LINE channel', async () => {
      const context = {
        ...baseContext,
        input: {
          operation: 'send_message',
          channel: 'line',
          recipient_type: 'user',
          recipient_id: 'line-user-123',
          message: 'LINE message test',
          message_type: 'text',
        },
      };

      mockNotificationService.sendMessage.mockResolvedValue(undefined);

      const result = await tool.execute(context);

      expect(result.success).toBe(true);
      expect(mockNotificationService.sendMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          channel: 'line',
          message_type: 'text',
        }),
        'tenant-123',
        'user-456',
      );
    });
  });

  describe('Create Notification Operation', () => {
    const baseContext: ToolExecutionContext = {
      tenantId: 'tenant-123',
      userId: 'user-456',
      ability: {} as any,
      user: {} as any,
    };

    it('should create notification successfully', async () => {
      const context = {
        ...baseContext,
        input: {
          operation: 'create_notification',
          title: 'Test Notification',
          message: 'This is a test notification',
          recipient_id: 'user-789',
          type: 'INFO',
          priority: 'HIGH',
        },
      };

      const mockNotification = {
        id: 'notif-123',
        title: 'Test Notification',
        message: 'This is a test notification',
        createdAt: new Date(),
      };

      mockMessageCenterService.createNotification.mockResolvedValue(mockNotification);

      const result = await tool.execute(context);

      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('Successfully created notification');
      expect(result.data?.data).toEqual(mockNotification);

      expect(mockMessageCenterService.createNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Test Notification',
          message: 'This is a test notification',
          recipient_id: 'user-789',
          type: 'INFO',
          priority: 'HIGH',
        }),
        'tenant-123',
      );
    });
  });

  describe('Get Notifications Operation', () => {
    const baseContext: ToolExecutionContext = {
      tenantId: 'tenant-123',
      userId: 'user-456',
      ability: {} as any,
      user: {} as any,
    };

    it('should retrieve notifications successfully', async () => {
      const context = {
        ...baseContext,
        input: {
          operation: 'get_notifications',
          recipient_id: 'user-789',
          limit: 5,
          offset: 0,
        },
      };

      const mockNotifications = [
        { id: 'notif-1', title: 'Notification 1', isRead: false },
        { id: 'notif-2', title: 'Notification 2', isRead: true },
      ];

      mockMessageCenterService.getNotifications.mockResolvedValue(mockNotifications);

      const result = await tool.execute(context);

      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('Retrieved 2 notifications');
      expect(result.data?.data.notifications).toEqual(mockNotifications);
      expect(result.data?.data.count).toBe(2);

      expect(mockMessageCenterService.getNotifications).toHaveBeenCalledWith(
        'tenant-123',
        'user-789',
        undefined, // workspace_id
        5, // limit
        0, // offset
      );
    });
  });

  describe('Error Handling', () => {
    const baseContext: ToolExecutionContext = {
      tenantId: 'tenant-123',
      userId: 'user-456',
      ability: {} as any,
      user: {} as any,
    };

    it('should handle invalid operation gracefully', async () => {
      const context = {
        ...baseContext,
        input: {
          operation: 'invalid_operation',
        },
      };

      const result = await tool.execute(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.data?.success).toBe(false);
    });

    it('should handle service errors gracefully', async () => {
      const context = {
        ...baseContext,
        input: {
          operation: 'send_message',
          channel: 'internal',
          recipient_type: 'user',
          recipient_id: 'user-123',
          message: 'Test message',
        },
      };

      mockNotificationService.sendMessage.mockRejectedValue(new Error('Service unavailable'));

      const result = await tool.execute(context);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Service unavailable');
      expect(result.data?.success).toBe(false);
    });

    it('should handle missing required fields', async () => {
      const context = {
        ...baseContext,
        input: {
          operation: 'send_message',
          channel: 'internal',
          // missing recipient_type, recipient_id, message
        },
      };

      const result = await tool.execute(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('Input Validation', () => {
    it('should validate input correctly', async () => {
      const validInput = {
        operation: 'send_message',
        channel: 'internal',
        recipient_type: 'user',
        recipient_id: 'user-123',
        message: 'Valid message',
      };

      const validation = await tool.validateInput(validInput);
      expect(validation.isValid).toBe(true);
    });
  });
});
