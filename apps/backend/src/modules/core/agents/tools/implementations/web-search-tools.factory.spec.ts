import { WebSearchToolsFactory, createWebSearchTool, WebSearchConfig } from './web-search-tools.factory';
import { ToolExecutionContext } from '../core/tool-registry.interface';
import { ToolExecutionError } from '../core/tool.errors';

describe('WebSearchToolsFactory', () => {
  let mockContext: ToolExecutionContext;

  beforeEach(() => {
    mockContext = {
      tenantId: 'test-tenant-id',
      userId: 'test-user-id',
      workspaceId: 'test-workspace-id',
      workspaceRoot: '/test/workspace',
      ability: {} as any,
      user: {} as any,
    };
  });

  describe('createWebSearchTool', () => {
    it('should create a web search tool with correct configuration', () => {
      const tool = createWebSearchTool(mockContext);

      expect(tool).toBeDefined();
      expect(tool.name).toBe('web_search');
      expect(tool.description).toContain('網頁搜尋工具');
      expect(tool.schema).toBeDefined();
      expect(tool.call).toBeDefined();
    });

    it('should create tool with custom configuration', () => {
      const config: WebSearchConfig = { 
        maxResults: 5,
        apiUrl: 'https://custom-api.com',
        timeout: 5000
      };
      const tool = createWebSearchTool(mockContext, config);

      expect(tool.description).toContain('5個');
    });

    it('should successfully perform a search', async () => {
      const tool = createWebSearchTool(mockContext);
      const result = await tool.call({ query: 'test search' });

      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);
      expect(result[0]).toHaveProperty('title');
      expect(result[0]).toHaveProperty('url');
      expect(result[0]).toHaveProperty('snippet');
      expect(result[0]).toHaveProperty('source');
      expect(result[0].title).toContain('test search');
    });

    it('should handle custom maxResults parameter', async () => {
      const config: WebSearchConfig = { maxResults: 3 };
      const tool = createWebSearchTool(mockContext, config);
      
      const result = await tool.call({ query: 'test', maxResults: 2 });

      expect(result).toHaveLength(1); // Mock only returns 1 result, but sliced to maxResults
    });

    it('should use default maxResults when not specified', async () => {
      const tool = createWebSearchTool(mockContext);
      const result = await tool.call({ query: 'test' });

      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeLessThanOrEqual(10); // Default max results
    });

    it('should handle empty query gracefully', async () => {
      const tool = createWebSearchTool(mockContext);
      
      // The schema should validate this, but if it passes through:
      const result = await tool.call({ query: 'empty' });
      expect(Array.isArray(result)).toBe(true);
    });

    it('should respect maxResults from input over config', async () => {
      const config: WebSearchConfig = { maxResults: 10 };
      const tool = createWebSearchTool(mockContext, config);
      
      const result = await tool.call({ query: 'test', maxResults: 5 });
      
      // Since mock returns 1 result, we can't test the slicing directly
      // but we can verify the call structure
      expect(Array.isArray(result)).toBe(true);
    });

    it('should include query in search results', async () => {
      const searchQuery = 'specific test query';
      const tool = createWebSearchTool(mockContext);
      
      const result = await tool.call({ query: searchQuery });
      
      expect(result[0].title).toContain(searchQuery);
      expect(result[0].snippet).toContain(searchQuery);
    });

    it('should generate proper URLs', async () => {
      const tool = createWebSearchTool(mockContext);
      const result = await tool.call({ query: 'test query' });

      expect(result[0].url).toMatch(/^https?:\/\//);
      expect(result[0].url).toContain(encodeURIComponent('test query'));
    });
  });

  describe('WebSearchToolsFactory static methods', () => {
    it('should create web search tool via static method', () => {
      const tool = WebSearchToolsFactory.createWebSearchTool(mockContext);
      
      expect(tool).toBeDefined();
      expect(tool.name).toBe('web_search');
    });

    it('should create LangChain tools array', () => {
      const tools = WebSearchToolsFactory.createLangChainTools(mockContext);
      
      expect(Array.isArray(tools)).toBe(true);
      expect(tools).toHaveLength(1);
      expect(tools[0].name).toBe('web_search');
    });

    it('should create tools with custom config', () => {
      const config: WebSearchConfig = { maxResults: 5 };
      const tools = WebSearchToolsFactory.createLangChainTools(mockContext, config);
      
      expect(tools[0].description).toContain('5個');
    });

    it('should pass config correctly to underlying tool', () => {
      const config: WebSearchConfig = { 
        maxResults: 7,
        apiUrl: 'https://test-api.com',
        timeout: 3000
      };
      
      const tool = WebSearchToolsFactory.createWebSearchTool(mockContext, config);
      expect(tool.description).toContain('7個');
    });
  });

  describe('input validation', () => {
    it('should validate query is required', () => {
      const tool = createWebSearchTool(mockContext);
      
      // The zod schema should validate this
      expect(() => {
        tool.schema.parse({ maxResults: 5 }); // Missing query
      }).toThrow();
    });

    it('should validate query is not empty', () => {
      const tool = createWebSearchTool(mockContext);
      
      expect(() => {
        tool.schema.parse({ query: '' }); // Empty query
      }).toThrow();
    });

    it('should accept valid input', () => {
      const tool = createWebSearchTool(mockContext);
      
      const validInput = { query: 'test search', maxResults: 5 };
      const parsed = tool.schema.parse(validInput);
      
      expect(parsed.query).toBe('test search');
      expect(parsed.maxResults).toBe(5);
    });

    it('should use default maxResults when not provided', () => {
      const tool = createWebSearchTool(mockContext);
      
      const input = { query: 'test' };
      const parsed = tool.schema.parse(input);
      
      expect(parsed.maxResults).toBe(10); // Default value
    });
  });
}); 