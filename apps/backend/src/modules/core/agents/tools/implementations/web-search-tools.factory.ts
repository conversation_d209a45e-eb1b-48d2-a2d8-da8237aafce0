import { z } from 'zod';
import axios from 'axios';
import { ToolExecutionContext, ToolConfig } from '../core/tool-registry.interface';
import { ToolExecutionError } from '../core/tool.errors';

export interface WebSearchConfig {
  apiUrl?: string;
  maxResults?: number;
  timeout?: number;
}

export interface WebSearchInput {
  query: string;
  maxResults?: number;
}

export interface WebSearchResult {
  title: string;
  url: string;
  snippet: string;
  source?: string;
}

/**
 * 創建網頁搜尋工具
 */
export function createWebSearchTool(context: ToolExecutionContext, config?: WebSearchConfig) {
  const inputSchema = z.object({
    query: z.string().min(1).describe('搜尋查詢字串'),
    maxResults: z
      .number()
      .optional()
      .default(config?.maxResults || 10)
      .describe('最大搜尋結果數量'),
  });

  return {
    name: 'web_search',
    description: `網頁搜尋工具 - 使用搜尋引擎查找最新的網頁資訊

此工具可以協助您搜尋網際網路上的最新資訊，獲取即時內容和答案。

功能特色：
- 支援多語言搜尋查詢
- 返回相關的網頁標題、URL和摘要
- 可設定搜尋結果數量限制
- 自動處理搜尋API錯誤

使用方式：
- query: 您想要搜尋的關鍵字或問題
- maxResults: 返回的搜尋結果數量（可選，預設${config?.maxResults || 10}個）

適用場景：
- 查找最新新聞和資訊
- 獲取技術文件和教學
- 驗證事實和數據
- 研究特定主題`,
    schema: inputSchema,
    call: async (input: WebSearchInput): Promise<WebSearchResult[]> => {
      try {
        const searchInput = {
          query: input.query,
          maxResults: input.maxResults || config?.maxResults || 10,
        };

        // 模擬網頁搜尋（實際實作需要整合真正的搜尋API）
        const mockResults: WebSearchResult[] = [
          {
            title: `搜尋結果：${searchInput.query}`,
            url: `https://example.com/search?q=${encodeURIComponent(searchInput.query)}`,
            snippet: `這是關於 "${searchInput.query}" 的搜尋結果摘要。此工具目前使用模擬資料，實際使用時需要整合真正的搜尋API。`,
            source: 'Mock Search Engine',
          },
        ];

        // 實際實作範例（需要配置真正的搜尋API）
        /*
        const apiUrl = config?.apiUrl || 'https://api.example.com/search';
        const response = await axios.get(apiUrl, {
          params: {
            q: searchInput.query,
            limit: searchInput.maxResults,
          },
          timeout: config?.timeout || 10000,
        });

        return response.data.results.map((result: any) => ({
          title: result.title,
          url: result.url,
          snippet: result.snippet,
          source: result.source || 'Web Search',
        }));
        */

        return mockResults.slice(0, searchInput.maxResults);
      } catch (error: any) {
        throw new ToolExecutionError(
          'web_search',
          `網頁搜尋失敗: ${error.message}`,
          { originalError: error, input },
        );
      }
    },
  };
}

/**
 * 網頁搜尋工具Factory類別
 */
export class WebSearchToolsFactory {
  static createWebSearchTool(context: ToolExecutionContext, config?: WebSearchConfig) {
    return createWebSearchTool(context, config);
  }

  static createLangChainTools(context: ToolExecutionContext, config?: WebSearchConfig) {
    return [createWebSearchTool(context, config)];
  }
} 