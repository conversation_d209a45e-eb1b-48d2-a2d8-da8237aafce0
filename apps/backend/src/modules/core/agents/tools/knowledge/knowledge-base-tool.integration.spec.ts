import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { KnowledgeBaseTool } from './knowledge-base-tool';
import { RAGIngestionService } from '../../../../rag/rag-ingestion.service';
import { RAGSecurityService } from '../../../../rag/rag-security.service';

// Test class to access protected methods
class TestableKnowledgeBaseTool extends KnowledgeBaseTool {
  public async testCall(input: string): Promise<string> {
    return this._call(input);
  }
}

describe('KnowledgeBaseTool - Integration Tests', () => {
  let tool: TestableKnowledgeBaseTool;
  let ragIngestionService: jest.Mocked<RAGIngestionService>;
  let ragSecurityService: jest.Mocked<RAGSecurityService>;

  const TENANT_A_ID = '11111111-1111-1111-1111-111111111111';
  const WORKSPACE_A_ID = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';

  beforeEach(async () => {
    // Create comprehensive mocks
    const mockRagIngestionService = {
      searchSimilarDocuments: jest.fn(),
    };

    const mockRagSecurityService = {
      validateSearchQuery: jest.fn(),
      validateDocumentAccess: jest.fn(),
      validateBulkDocumentAccess: jest.fn(),
      logSecurityEvent: jest.fn(),
    };

    ragIngestionService = mockRagIngestionService as any;
    ragSecurityService = mockRagSecurityService as any;

    // Suppress logger output during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'debug').mockImplementation();
    jest.spyOn(Logger.prototype, 'warn').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();

    // Default security validation behavior - allow all by default
    ragSecurityService.validateSearchQuery.mockImplementation(() => {});
    ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
    ragSecurityService.validateDocumentAccess.mockResolvedValue(true);
    ragSecurityService.logSecurityEvent.mockResolvedValue(undefined);

    // Reset mocks before each test
    jest.clearAllMocks();
  });

  describe('Basic Functionality', () => {
    it('should successfully search and return results', async () => {
      // Ensure all mocks are properly set up for this test
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);
      ragSecurityService.logSecurityEvent.mockResolvedValue(undefined);

      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );

      const mockResults = [
        {
          id: 'doc1',
          content: 'Test document content',
          similarity: 0.85,
          tenant_id: TENANT_A_ID,
          file_id: 'file1',
          workspace_id: WORKSPACE_A_ID,
          metadata: { file_name: 'test.txt' },
          embedding: [0.1, 0.2, 0.3],
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);

      const result = await tool.testCall('{"query": "test query"}');

      expect(result).toContain('Found 1 relevant documents');
      expect(result).toContain('Test document content');
      expect(result).toContain('test.txt');
      expect(ragIngestionService.searchSimilarDocuments).toHaveBeenCalledWith(
        'test query',
        TENANT_A_ID,
        WORKSPACE_A_ID,
        10,
        0.7,
      );
    });

    it('should handle empty search results', async () => {
      // Ensure all mocks are properly set up for this test
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);
      ragSecurityService.logSecurityEvent.mockResolvedValue(undefined);

      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );

      ragIngestionService.searchSimilarDocuments.mockResolvedValue([]);

      const result = await tool.testCall('{"query": "no results query"}');

      expect(result).toContain('No relevant documents found');
    });

    it('should handle service errors gracefully', async () => {
      // Ensure all mocks are properly set up for this test
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);
      ragSecurityService.logSecurityEvent.mockResolvedValue(undefined);

      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );

      ragIngestionService.searchSimilarDocuments.mockRejectedValue(
        new Error('Database connection failed'),
      );

      const result = await tool.testCall('{"query": "test"}');

      expect(result).toContain('Service temporarily unavailable');
      expect(result).not.toContain('Database connection failed');
    });
  });

  describe('Tenant Isolation', () => {
    it('should enforce bulk validation failure', async () => {
      // Set up mocks for this specific test
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);
      ragSecurityService.logSecurityEvent.mockResolvedValue(undefined);
      // Override to fail bulk validation for this test
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(false);

      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );

      const unauthorizedResults = [
        {
          id: 'doc-unauthorized',
          content: 'Unauthorized content',
          similarity: 0.9,
          tenant_id: 'different-tenant',
          file_id: 'file1',
          workspace_id: WORKSPACE_A_ID,
          metadata: { file_name: 'unauthorized.txt' },
          embedding: [0.1, 0.2, 0.3],
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      ragIngestionService.searchSimilarDocuments.mockResolvedValue(unauthorizedResults);

      const result = await tool.testCall('{"query": "test"}');

      expect(result).toContain('Access denied');
    });
  });

  describe('Security Validation', () => {
    it('should handle security query validation failure', async () => {
      // Set up mocks for this specific test
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);
      ragSecurityService.logSecurityEvent.mockResolvedValue(undefined);
      ragSecurityService.validateSearchQuery.mockImplementation(() => {
        throw new Error('Malicious query detected');
      });

      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );

      const result = await tool.testCall('{"query": "malicious query"}');

      expect(result).toContain('Query validation failed');
    });

    it('should validate input parameters', async () => {
      // Set up mocks for this test
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);
      ragSecurityService.logSecurityEvent.mockResolvedValue(undefined);

      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );

      // Test invalid JSON
      const result1 = await tool.testCall('invalid json');
      expect(result1).toContain('Invalid input format');

      // Test empty query
      const result2 = await tool.testCall('{"query": ""}');
      expect(result2).toContain('Query cannot be empty');

      // Test invalid parameters
      const result3 = await tool.testCall('{"query": "test", "maxResults": -1}');
      expect(result3).toContain('Invalid input');
    });
  });

  describe('Error Handling', () => {
    it('should sanitize error messages', async () => {
      // Set up mocks for this test
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);
      ragSecurityService.logSecurityEvent.mockResolvedValue(undefined);

      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );

      ragIngestionService.searchSimilarDocuments.mockRejectedValue(
        new Error(`Internal error with tenant ${TENANT_A_ID} and sensitive data`),
      );

      const result = await tool.testCall('{"query": "test"}');

      expect(result).toContain('Service temporarily unavailable');
      expect(result).not.toContain(TENANT_A_ID);
      expect(result).not.toContain('Internal error');
    });
  });
});
