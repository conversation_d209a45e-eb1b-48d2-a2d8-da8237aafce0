import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { KnowledgeBaseTool, KnowledgeBaseErrorType } from './knowledge-base-tool';
import { RAGIngestionService } from '../../../../rag/rag-ingestion.service';
import { RAGSecurityService } from '../../../../rag/rag-security.service';

// Test class to access protected methods
class TestableKnowledgeBaseTool extends KnowledgeBaseTool {
  public async testCall(input: string): Promise<string> {
    return this._call(input);
  }

  public getAuditLogs() {
    return this.getSecurityAuditLogs();
  }

  public clearAuditLogs() {
    this.clearSecurityAuditLogs();
  }
}

describe('KnowledgeBaseTool - Comprehensive Tests', () => {
  let tool: TestableKnowledgeBaseTool;
  let ragIngestionService: jest.Mocked<RAGIngestionService>;
  let ragSecurityService: jest.Mocked<RAGSecurityService>;

  const TENANT_A_ID = '11111111-1111-1111-1111-111111111111';
  const TENANT_B_ID = '22222222-2222-2222-2222-222222222222';
  const WORKSPACE_A_ID = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';
  const WORKSPACE_B_ID = 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb';

  // Helper function to create complete mock results
  const createMockResults = (tenantId: string, workspaceId: string, count: number = 1) => {
    return Array.from({ length: count }, (_, i) => ({
      id: `doc-${i + 1}`,
      content: `Test document content ${i + 1}`,
      similarity: 0.85 - i * 0.1,
      tenant_id: tenantId,
      file_id: `file-${i + 1}`,
      workspace_id: workspaceId,
      metadata: {
        file_name: `test-${i + 1}.txt`,
        chunk_index: i,
        source: 'test',
      },
      embedding: [0.1, 0.2, 0.3],
      created_at: new Date(),
      updated_at: new Date(),
    }));
  };

  beforeEach(async () => {
    // Create comprehensive mocks
    const mockRagIngestionService = {
      searchSimilarDocuments: jest.fn(),
    };

    const mockRagSecurityService = {
      validateSearchQuery: jest.fn(),
      validateDocumentAccess: jest.fn(),
      validateBulkDocumentAccess: jest.fn(),
      logSecurityEvent: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: RAGIngestionService,
          useValue: mockRagIngestionService,
        },
        {
          provide: RAGSecurityService,
          useValue: mockRagSecurityService,
        },
      ],
    }).compile();

    ragIngestionService = module.get(RAGIngestionService);
    ragSecurityService = module.get(RAGSecurityService);

    // Suppress logger output during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'debug').mockImplementation();
    jest.spyOn(Logger.prototype, 'warn').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Functionality', () => {
    beforeEach(() => {
      // Set up default successful mocks
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);
      ragSecurityService.logSecurityEvent.mockResolvedValue(undefined);
    });

    it('should be instantiated correctly', () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );

      expect(tool).toBeDefined();
      expect(tool.name).toBe('knowledge_base_search');
      expect(tool.description).toContain('Search and retrieve information');
    });

    it('should successfully search and return results', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );

      const mockResults = createMockResults(TENANT_A_ID, WORKSPACE_A_ID, 2);
      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);

      const result = await tool.testCall('{"query": "test query", "maxResults": 5}');

      expect(result).toContain('Found 2 relevant documents');
      expect(result).toContain('Test document content 1');
      expect(result).toContain('test-1.txt');
      expect(ragIngestionService.searchSimilarDocuments).toHaveBeenCalledWith(
        'test query',
        TENANT_A_ID,
        WORKSPACE_A_ID,
        5,
        0.7,
      );
    });

    it('should handle empty search results', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );

      ragIngestionService.searchSimilarDocuments.mockResolvedValue([]);

      const result = await tool.testCall('{"query": "no results query"}');

      expect(result).toContain('No relevant documents found');
    });

    it('should use default parameters when not specified', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );

      const mockResults = createMockResults(TENANT_A_ID, WORKSPACE_A_ID, 1);
      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);

      await tool.testCall('{"query": "test"}');

      expect(ragIngestionService.searchSimilarDocuments).toHaveBeenCalledWith(
        'test',
        TENANT_A_ID,
        WORKSPACE_A_ID,
        10, // default maxResults
        0.7, // default similarity_threshold
      );
    });

    it('should respect custom parameters', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );

      const mockResults = createMockResults(TENANT_A_ID, WORKSPACE_A_ID, 1);
      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);

      await tool.testCall('{"query": "test", "maxResults": 3, "similarity_threshold": 0.8}');

      expect(ragIngestionService.searchSimilarDocuments).toHaveBeenCalledWith(
        'test',
        TENANT_A_ID,
        WORKSPACE_A_ID,
        3,
        0.8,
      );
    });
  });

  describe('Input Validation', () => {
    beforeEach(() => {
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);
      ragSecurityService.logSecurityEvent.mockResolvedValue(undefined);

      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );
    });

    it('should reject invalid JSON', async () => {
      const result = await tool.testCall('invalid json');
      expect(result).toContain('Invalid input format');
    });

    it('should reject empty query', async () => {
      const result = await tool.testCall('{"query": ""}');
      expect(result).toContain('Query cannot be empty');
    });

    it('should reject query that is too long', async () => {
      const longQuery = 'x'.repeat(1001);
      const result = await tool.testCall(`{"query": "${longQuery}"}`);
      expect(result).toContain('Query too long');
    });

    it('should reject invalid maxResults', async () => {
      const result1 = await tool.testCall('{"query": "test", "maxResults": -1}');
      expect(result1).toContain('Invalid input');

      const result2 = await tool.testCall('{"query": "test", "maxResults": 101}');
      expect(result2).toContain('Invalid input');
    });

    it('should reject invalid similarity_threshold', async () => {
      const result1 = await tool.testCall('{"query": "test", "similarity_threshold": -0.1}');
      expect(result1).toContain('Invalid input');

      const result2 = await tool.testCall('{"query": "test", "similarity_threshold": 1.1}');
      expect(result2).toContain('Invalid input');
    });

    it('should reject missing query field', async () => {
      const result = await tool.testCall('{"maxResults": 5}');
      expect(result).toContain('Query is required');
    });
  });

  describe('Security Validation', () => {
    beforeEach(() => {
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);
      ragSecurityService.logSecurityEvent.mockResolvedValue(undefined);

      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );
    });

    it('should handle security query validation failure', async () => {
      ragSecurityService.validateSearchQuery.mockImplementation(() => {
        throw new Error('Malicious query detected');
      });

      const result = await tool.testCall('{"query": "malicious query"}');

      expect(result).toContain('Query validation failed');
      expect(ragSecurityService.validateSearchQuery).toHaveBeenCalledWith(
        'malicious query',
        TENANT_A_ID,
      );
    });

    it('should handle bulk document access validation failure', async () => {
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(false);

      const mockResults = createMockResults(TENANT_A_ID, WORKSPACE_A_ID, 1);
      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);

      const result = await tool.testCall('{"query": "test"}');

      expect(result).toContain('Access denied');
      expect(ragSecurityService.logSecurityEvent).toHaveBeenCalledWith(
        'access_violation',
        TENANT_A_ID,
        expect.objectContaining({
          violation: 'bulk_document_access_denied',
        }),
      );
    });

    it('should handle individual document access validation failure', async () => {
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(false);

      const mockResults = createMockResults(TENANT_A_ID, WORKSPACE_A_ID, 1);
      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);

      const result = await tool.testCall('{"query": "test"}');

      expect(result).toContain('Found 0 relevant documents');
    });
  });

  describe('Tenant Isolation', () => {
    beforeEach(() => {
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);
      ragSecurityService.logSecurityEvent.mockResolvedValue(undefined);
    });

    it('should reject results from different tenant', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );

      // Mock results that include documents from different tenant
      const mixedResults = [
        ...createMockResults(TENANT_A_ID, WORKSPACE_A_ID, 1),
        ...createMockResults(TENANT_B_ID, WORKSPACE_A_ID, 1),
      ];

      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mixedResults);

      const result = await tool.testCall('{"query": "test"}');

      // Should only return results from correct tenant
      expect(result).toContain('Found 1 relevant documents');
      expect(result).not.toContain('doc-2'); // Document from wrong tenant
    });

    it('should reject results from different workspace', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );

      // Mock results that include documents from different workspace
      const mixedResults = [
        ...createMockResults(TENANT_A_ID, WORKSPACE_A_ID, 1),
        ...createMockResults(TENANT_A_ID, WORKSPACE_B_ID, 1),
      ];

      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mixedResults);

      const result = await tool.testCall('{"query": "test"}');

      // Should only return results from correct workspace
      expect(result).toContain('Found 1 relevant documents');
    });

    it('should work without workspace restriction', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        // No workspace specified
      );

      const mockResults = createMockResults(TENANT_A_ID, WORKSPACE_A_ID, 2);
      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);

      const result = await tool.testCall('{"query": "test"}');

      expect(result).toContain('Found 2 relevant documents');
      expect(ragIngestionService.searchSimilarDocuments).toHaveBeenCalledWith(
        'test',
        TENANT_A_ID,
        undefined,
        10,
        0.7,
      );
    });

    it('should validate tenant ID format', async () => {
      expect(() => {
        new TestableKnowledgeBaseTool(
          ragIngestionService,
          ragSecurityService,
          'invalid-tenant-id',
          WORKSPACE_A_ID,
        );
      }).toThrow('Invalid tenant ID format');
    });

    it('should validate workspace ID format when provided', async () => {
      expect(() => {
        new TestableKnowledgeBaseTool(
          ragIngestionService,
          ragSecurityService,
          TENANT_A_ID,
          'invalid-workspace-id',
        );
      }).toThrow('Invalid workspace ID format');
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);
      ragSecurityService.logSecurityEvent.mockResolvedValue(undefined);

      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );
    });

    it('should handle RAG service errors gracefully', async () => {
      ragIngestionService.searchSimilarDocuments.mockRejectedValue(
        new Error('Database connection failed'),
      );

      const result = await tool.testCall('{"query": "test"}');

      expect(result).toContain('Service temporarily unavailable');
      expect(result).not.toContain('Database connection failed');
    });

    it('should handle security service errors gracefully', async () => {
      ragSecurityService.validateDocumentAccess.mockRejectedValue(
        new Error('Security service unavailable'),
      );

      const mockResults = createMockResults(TENANT_A_ID, WORKSPACE_A_ID, 1);
      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);

      const result = await tool.testCall('{"query": "test"}');

      // Should handle gracefully and not expose internal error
      expect(result).not.toContain('Security service unavailable');
    });

    it('should sanitize error messages to prevent information leakage', async () => {
      ragIngestionService.searchSimilarDocuments.mockRejectedValue(
        new Error(`Internal error with tenant ${TENANT_A_ID} and sensitive data`),
      );

      const result = await tool.testCall('{"query": "test"}');

      expect(result).toContain('Service temporarily unavailable');
      expect(result).not.toContain(TENANT_A_ID);
      expect(result).not.toContain('Internal error');
      expect(result).not.toContain('sensitive data');
    });

    it('should handle malformed metadata gracefully', async () => {
      const malformedResults = [
        {
          id: 'doc-1',
          content: 'Test content',
          similarity: 0.85,
          tenant_id: TENANT_A_ID,
          file_id: 'file-1',
          workspace_id: WORKSPACE_A_ID,
          metadata: null, // Malformed metadata
          embedding: [0.1, 0.2, 0.3],
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      ragIngestionService.searchSimilarDocuments.mockResolvedValue(malformedResults);

      const result = await tool.testCall('{"query": "test"}');

      expect(result).toContain('Found 1 relevant documents');
      expect(result).toContain('Test content');
    });
  });

  describe('Performance and Monitoring', () => {
    beforeEach(() => {
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);
      ragSecurityService.logSecurityEvent.mockResolvedValue(undefined);

      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );
    });

    it('should generate unique request IDs', async () => {
      const mockResults = createMockResults(TENANT_A_ID, WORKSPACE_A_ID, 1);
      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);

      await tool.testCall('{"query": "test1"}');
      await tool.testCall('{"query": "test2"}');

      const auditLogs = tool.getAuditLogs();
      expect(auditLogs).toHaveLength(2);
      expect(auditLogs[0].requestId).not.toBe(auditLogs[1].requestId);
    });

    it('should track security audit logs', async () => {
      const mockResults = createMockResults(TENANT_A_ID, WORKSPACE_A_ID, 1);
      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);

      await tool.testCall('{"query": "test query"}');

      const auditLogs = tool.getAuditLogs();
      expect(auditLogs).toHaveLength(1);
      expect(auditLogs[0]).toMatchObject({
        tenantId: TENANT_A_ID,
        workspaceId: WORKSPACE_A_ID,
        action: 'knowledge_base_search',
        success: true,
      });
    });

    it('should clear audit logs when requested', async () => {
      const mockResults = createMockResults(TENANT_A_ID, WORKSPACE_A_ID, 1);
      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);

      await tool.testCall('{"query": "test"}');
      expect(tool.getAuditLogs()).toHaveLength(1);

      tool.clearAuditLogs();
      expect(tool.getAuditLogs()).toHaveLength(0);
    });

    it('should limit audit log storage', async () => {
      const mockResults = createMockResults(TENANT_A_ID, WORKSPACE_A_ID, 1);
      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);

      // Simulate many requests
      for (let i = 0; i < 105; i++) {
        await tool.testCall(`{"query": "test${i}"}`);
      }

      const auditLogs = tool.getAuditLogs();
      expect(auditLogs.length).toBeLessThanOrEqual(100);
    });
  });

  describe('Edge Cases', () => {
    beforeEach(() => {
      ragSecurityService.validateSearchQuery.mockImplementation(() => {});
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);
      ragSecurityService.logSecurityEvent.mockResolvedValue(undefined);

      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A_ID,
      );
    });

    it('should handle extremely large result sets', async () => {
      const largeResults = createMockResults(TENANT_A_ID, WORKSPACE_A_ID, 50);
      ragIngestionService.searchSimilarDocuments.mockResolvedValue(largeResults);

      const result = await tool.testCall('{"query": "test"}');

      expect(result).toContain('Found 50 relevant documents');
      expect(result.length).toBeLessThan(10000); // Should not be excessively long
    });

    it('should handle special characters in query', async () => {
      const mockResults = createMockResults(TENANT_A_ID, WORKSPACE_A_ID, 1);
      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mockResults);

      const specialQuery = '特殊字符 & symbols: @#$%^&*()';
      const result = await tool.testCall(`{"query": "${specialQuery}"}`);

      expect(result).toContain('Found 1 relevant documents');
      expect(ragIngestionService.searchSimilarDocuments).toHaveBeenCalledWith(
        specialQuery,
        TENANT_A_ID,
        WORKSPACE_A_ID,
        10,
        0.7,
      );
    });

    it('should handle very low similarity scores', async () => {
      const lowSimilarityResults = createMockResults(TENANT_A_ID, WORKSPACE_A_ID, 1);
      lowSimilarityResults[0].similarity = 0.1;
      ragIngestionService.searchSimilarDocuments.mockResolvedValue(lowSimilarityResults);

      const result = await tool.testCall('{"query": "test", "similarity_threshold": 0.05}');

      expect(result).toContain('Found 1 relevant documents');
    });

    it('should handle results with missing optional fields', async () => {
      const incompleteResults = [
        {
          id: 'doc-1',
          content: 'Test content',
          similarity: 0.85,
          tenant_id: TENANT_A_ID,
          file_id: null,
          workspace_id: WORKSPACE_A_ID,
          metadata: { file_name: 'test.txt' },
          embedding: [0.1, 0.2, 0.3],
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      ragIngestionService.searchSimilarDocuments.mockResolvedValue(incompleteResults);

      const result = await tool.testCall('{"query": "test"}');

      expect(result).toContain('Found 1 relevant documents');
      expect(result).toContain('Test content');
    });
  });
});
