import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { KnowledgeBaseTool } from './knowledge-base-tool';
import { RAGIngestionService } from '../../../../rag/rag-ingestion.service';
import { RAGSecurityService } from '../../../../rag/rag-security.service';

// Test class to access protected methods
class TestableKnowledgeBaseTool extends KnowledgeBaseTool {
  public async testCall(input: string): Promise<string> {
    return this.testCall(input);
  }
}

describe('KnowledgeBaseTool - Tenant Isolation Integration Tests', () => {
  let tool: TestableKnowledgeBaseTool;
  let ragIngestionService: jest.Mocked<RAGIngestionService>;
  let ragSecurityService: jest.Mocked<RAGSecurityService>;

  // Test tenant scenarios
  const TENANT_A_ID = '11111111-1111-1111-1111-111111111111';
  const TENANT_B_ID = '22222222-2222-2222-2222-222222222222';
  const WORKSPACE_A1_ID = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';
  const WORKSPACE_A2_ID = 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb';
  const WORKSPACE_B1_ID = 'cccccccc-cccc-cccc-cccc-cccccccccccc';

  beforeEach(async () => {
    const mockRagIngestionService = {
      searchSimilarDocuments: jest.fn(),
    };

    const mockRagSecurityService = {
      validateSearchQuery: jest.fn(),
      validateDocumentAccess: jest.fn(),
      validateBulkDocumentAccess: jest.fn(),
      logSecurityEvent: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: RAGIngestionService,
          useValue: mockRagIngestionService,
        },
        {
          provide: RAGSecurityService,
          useValue: mockRagSecurityService,
        },
      ],
    }).compile();

    ragIngestionService = module.get(RAGIngestionService);
    ragSecurityService = module.get(RAGSecurityService);

    // Suppress logger output during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'debug').mockImplementation();
    jest.spyOn(Logger.prototype, 'warn').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();

    // Default security validation behavior
    ragSecurityService.validateSearchQuery.mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Cross-Tenant Access Prevention', () => {
    it('should prevent access to documents from different tenants', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A1_ID,
      );

      const mixedTenantResults = [
        {
          id: 'doc-tenant-a-1',
          content: 'Tenant A confidential document 1',
          tenant_id: TENANT_A_ID,
          workspace_id: WORKSPACE_A1_ID,
          similarity: 0.95,
          file_id: 'file-a-1',
          metadata: { file_name: 'confidential-a.txt' },
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'doc-tenant-b-1',
          content: 'Tenant B confidential document 1',
          tenant_id: TENANT_B_ID,
          workspace_id: WORKSPACE_B1_ID,
          similarity: 0.93,
          file_id: 'file-b-1',
          metadata: { file_name: 'confidential-b.txt' },
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'doc-tenant-a-2',
          content: 'Tenant A confidential document 2',
          tenant_id: TENANT_A_ID,
          workspace_id: WORKSPACE_A1_ID,
          similarity: 0.88,
          file_id: 'file-a-2',
          metadata: { file_name: 'confidential-a2.txt' },
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mixedTenantResults);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);

      // Mock individual document access validation
      ragSecurityService.validateDocumentAccess
        .mockResolvedValueOnce(true) // doc-tenant-a-1: allowed
        .mockResolvedValueOnce(false) // doc-tenant-b-1: denied (different tenant)
        .mockResolvedValueOnce(true); // doc-tenant-a-2: allowed

      const result = await tool.testCall('{"query": "confidential documents"}');

      // Should only return documents from Tenant A
      expect(result).toContain('Found 2 relevant documents');
      expect(result).toContain('Tenant A confidential document 1');
      expect(result).toContain('Tenant A confidential document 2');
      expect(result).not.toContain('Tenant B confidential document 1');
      expect(result).toContain('confidential-a.txt');
      expect(result).toContain('confidential-a2.txt');
      expect(result).not.toContain('confidential-b.txt');
    });

    it('should handle bulk validation failure for cross-tenant access', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A1_ID,
      );

      const crossTenantResults = [
        {
          id: 'doc-tenant-b-secret',
          content: 'Highly sensitive Tenant B data',
          tenant_id: TENANT_B_ID,
          workspace_id: WORKSPACE_B1_ID,
          similarity: 0.99,
          file_id: 'file-b-secret',
          metadata: {},
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      ragIngestionService.searchSimilarDocuments.mockResolvedValue(crossTenantResults);
      // Bulk validation should fail due to cross-tenant access
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(false);

      const result = await tool.testCall('{"query": "sensitive data"}');

      expect(result).toContain('Access denied');
      expect(ragSecurityService.logSecurityEvent).toHaveBeenCalledWith(
        'access_violation',
        TENANT_A_ID,
        expect.objectContaining({
          violation: 'bulk_document_access_denied',
          documentIds: ['doc-tenant-b-secret'],
        }),
      );
    });
  });

  describe('Workspace Isolation', () => {
    it('should isolate documents within tenant workspaces', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A1_ID,
      );

      const workspaceResults = [
        {
          id: 'doc-workspace-a1-1',
          content: 'Workspace A1 project documentation',
          tenant_id: TENANT_A_ID,
          workspace_id: WORKSPACE_A1_ID,
          similarity: 0.9,
          file_id: 'file-a1-1',
          metadata: { file_name: 'project-a1.md' },
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'doc-workspace-a2-1',
          content: 'Workspace A2 project documentation',
          tenant_id: TENANT_A_ID,
          workspace_id: WORKSPACE_A2_ID,
          similarity: 0.85,
          file_id: 'file-a2-1',
          metadata: { file_name: 'project-a2.md' },
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      ragIngestionService.searchSimilarDocuments.mockResolvedValue(workspaceResults);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);

      ragSecurityService.validateDocumentAccess
        .mockResolvedValueOnce(true) // doc-workspace-a1-1: same workspace
        .mockResolvedValueOnce(false); // doc-workspace-a2-1: different workspace

      const result = await tool.testCall('{"query": "project documentation"}');

      // Should only return documents from the same workspace
      expect(result).toContain('Found 1 relevant documents');
      expect(result).toContain('Workspace A1 project documentation');
      expect(result).not.toContain('Workspace A2 project documentation');
      expect(result).toContain('project-a1.md');
      expect(result).not.toContain('project-a2.md');
    });

    it('should allow access when no workspace is specified', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        // No workspace specified
      );

      const tenantResults = [
        {
          id: 'doc-tenant-a-global',
          content: 'Tenant A global document',
          tenant_id: TENANT_A_ID,
          workspace_id: null,
          similarity: 0.9,
          file_id: 'file-global',
          metadata: { file_name: 'global.txt' },
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'doc-workspace-a1-specific',
          content: 'Workspace A1 specific document',
          tenant_id: TENANT_A_ID,
          workspace_id: WORKSPACE_A1_ID,
          similarity: 0.85,
          file_id: 'file-specific',
          metadata: { file_name: 'workspace-specific.txt' },
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      ragIngestionService.searchSimilarDocuments.mockResolvedValue(tenantResults);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess
        .mockResolvedValueOnce(true) // global document
        .mockResolvedValueOnce(true); // workspace document (allowed when no workspace filter)

      const result = await tool.testCall('{"query": "documents"}');

      expect(result).toContain('Found 2 relevant documents');
      expect(result).toContain('Tenant A global document');
      expect(result).toContain('Workspace A1 specific document');
    });
  });

  describe('Data Leakage Prevention', () => {
    it('should not leak tenant information in error messages', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A1_ID,
      );

      ragIngestionService.searchSimilarDocuments.mockRejectedValue(
        new Error(`Database error: tenant ${TENANT_B_ID} access denied for user ${TENANT_A_ID}`),
      );

      const result = await tool.testCall('{"query": "test"}');

      // Should not expose tenant IDs or internal error details
      expect(result).toContain('Service temporarily unavailable');
      expect(result).not.toContain(TENANT_A_ID);
      expect(result).not.toContain(TENANT_B_ID);
      expect(result).not.toContain('Database error');
      expect(result).not.toContain('access denied for user');
    });

    it('should sanitize search results metadata', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A1_ID,
      );

      const resultsWithSensitiveMetadata = [
        {
          id: 'doc-1',
          content: 'Public content',
          tenant_id: TENANT_A_ID,
          workspace_id: WORKSPACE_A1_ID,
          similarity: 0.9,
          file_id: 'file-1',
          metadata: {
            file_name: 'public.txt',
            internal_path: '/sensitive/internal/path',
            user_credentials: 'admin:password123',
            database_connection: 'postgresql://user:pass@localhost/db',
          },
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      ragIngestionService.searchSimilarDocuments.mockResolvedValue(resultsWithSensitiveMetadata);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);

      const result = await tool.testCall('{"query": "test"}');

      // Should only show safe metadata
      expect(result).toContain('public.txt');
      expect(result).toContain('Public content');
      expect(result).not.toContain('/sensitive/internal/path');
      expect(result).not.toContain('admin:password123');
      expect(result).not.toContain('postgresql://user:pass@localhost/db');
    });
  });

  describe('Security Boundary Enforcement', () => {
    it('should enforce strict tenant boundaries during search', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A1_ID,
      );

      // Verify that search is called with correct tenant parameters
      ragIngestionService.searchSimilarDocuments.mockResolvedValue([]);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);

      await tool.testCall('{"query": "test search"}');

      expect(ragIngestionService.searchSimilarDocuments).toHaveBeenCalledWith(
        'test search',
        TENANT_A_ID,
        WORKSPACE_A1_ID,
        5,
        0.7,
      );
    });

    it('should validate all document IDs in search results', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A1_ID,
      );

      const searchResults = [
        {
          id: 'doc-1',
          content: 'Content 1',
          tenant_id: TENANT_A_ID,
          workspace_id: WORKSPACE_A1_ID,
          similarity: 0.9,
          file_id: 'file1',
          metadata: {},
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'doc-2',
          content: 'Content 2',
          tenant_id: TENANT_A_ID,
          workspace_id: WORKSPACE_A1_ID,
          similarity: 0.8,
          file_id: 'file2',
          metadata: {},
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'doc-3',
          content: 'Content 3',
          tenant_id: TENANT_A_ID,
          workspace_id: WORKSPACE_A1_ID,
          similarity: 0.7,
          file_id: 'file3',
          metadata: {},
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      ragIngestionService.searchSimilarDocuments.mockResolvedValue(searchResults);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess
        .mockResolvedValueOnce(true)
        .mockResolvedValueOnce(true)
        .mockResolvedValueOnce(true);

      await tool.testCall('{"query": "test"}');

      // Should validate each document individually
      expect(ragSecurityService.validateDocumentAccess).toHaveBeenCalledTimes(3);
      expect(ragSecurityService.validateDocumentAccess).toHaveBeenNthCalledWith(
        1,
        'doc-1',
        TENANT_A_ID,
        WORKSPACE_A1_ID,
      );
      expect(ragSecurityService.validateDocumentAccess).toHaveBeenNthCalledWith(
        2,
        'doc-2',
        TENANT_A_ID,
        WORKSPACE_A1_ID,
      );
      expect(ragSecurityService.validateDocumentAccess).toHaveBeenNthCalledWith(
        3,
        'doc-3',
        TENANT_A_ID,
        WORKSPACE_A1_ID,
      );
    });

    it('should handle partial validation failures gracefully', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A1_ID,
      );

      const mixedResults = [
        {
          id: 'doc-valid-1',
          content: 'Valid content 1',
          tenant_id: TENANT_A_ID,
          similarity: 0.9,
          workspace_id: WORKSPACE_A1_ID,
          file_id: 'file1',
          metadata: {},
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'doc-invalid',
          content: 'Invalid content',
          tenant_id: TENANT_B_ID,
          similarity: 0.8,
          workspace_id: WORKSPACE_B1_ID,
          file_id: 'file2',
          metadata: {},
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'doc-valid-2',
          content: 'Valid content 2',
          tenant_id: TENANT_A_ID,
          similarity: 0.7,
          workspace_id: WORKSPACE_A1_ID,
          file_id: 'file3',
          metadata: {},
          embedding: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      ragIngestionService.searchSimilarDocuments.mockResolvedValue(mixedResults);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess
        .mockResolvedValueOnce(true) // doc-valid-1: allowed
        .mockResolvedValueOnce(false) // doc-invalid: denied
        .mockResolvedValueOnce(true); // doc-valid-2: allowed

      const result = await tool.testCall('{"query": "content"}');

      expect(result).toContain('Found 2 relevant documents');
      expect(result).toContain('Valid content 1');
      expect(result).toContain('Valid content 2');
      expect(result).not.toContain('Invalid content');
    });
  });

  describe('Multi-Tenant Security Scenarios', () => {
    it('should handle concurrent requests from different tenants', async () => {
      const toolTenantA = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A1_ID,
      );

      const toolTenantB = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_B_ID,
        WORKSPACE_B1_ID,
      );

      // Mock different results for different tenants
      ragIngestionService.searchSimilarDocuments
        .mockResolvedValueOnce([
          {
            id: 'doc-a',
            content: 'Tenant A content',
            tenant_id: TENANT_A_ID,
            similarity: 0.9,
            workspace_id: WORKSPACE_A1_ID,
            file_id: 'file-a',
            metadata: {},
            embedding: {},
            created_at: new Date(),
            updated_at: new Date(),
          },
        ])
        .mockResolvedValueOnce([
          {
            id: 'doc-b',
            content: 'Tenant B content',
            tenant_id: TENANT_B_ID,
            similarity: 0.9,
            workspace_id: WORKSPACE_B1_ID,
            file_id: 'file-b',
            metadata: {},
            embedding: {},
            created_at: new Date(),
            updated_at: new Date(),
          },
        ]);

      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);
      ragSecurityService.validateDocumentAccess.mockResolvedValue(true);

      // Execute concurrent requests
      const [resultA, resultB] = await Promise.all([
        toolTenantA.testCall('{"query": "test"}'),
        toolTenantB.testCall('{"query": "test"}'),
      ]);

      // Each tenant should only see their own content
      expect(resultA).toContain('Tenant A content');
      expect(resultA).not.toContain('Tenant B content');

      expect(resultB).toContain('Tenant B content');
      expect(resultB).not.toContain('Tenant A content');

      // Verify audit logs are separate
      const auditLogsA = toolTenantA.getSecurityAuditLogs();
      const auditLogsB = toolTenantB.getSecurityAuditLogs();

      expect(auditLogsA[0].tenantId).toBe(TENANT_A_ID);
      expect(auditLogsB[0].tenantId).toBe(TENANT_B_ID);
    });

    it('should prevent privilege escalation attempts', async () => {
      tool = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A1_ID,
      );

      // Attempt to access documents by manipulating query parameters
      const maliciousQuery = {
        query: 'test',
        _tenantOverride: TENANT_B_ID,
        _workspaceOverride: WORKSPACE_B1_ID,
        _adminAccess: true,
      };

      ragIngestionService.searchSimilarDocuments.mockResolvedValue([]);
      ragSecurityService.validateBulkDocumentAccess.mockResolvedValue(true);

      await tool.testCall(JSON.stringify(maliciousQuery));

      // Should still use original tenant/workspace, not the malicious overrides
      expect(ragIngestionService.searchSimilarDocuments).toHaveBeenCalledWith(
        'test',
        TENANT_A_ID, // Original tenant, not TENANT_B_ID
        WORKSPACE_A1_ID, // Original workspace, not WORKSPACE_B1_ID
        5,
        0.7,
      );
    });

    it('should maintain security audit trail across tenant operations', async () => {
      const toolTenantA = new TestableKnowledgeBaseTool(
        ragIngestionService,
        ragSecurityService,
        TENANT_A_ID,
        WORKSPACE_A1_ID,
      );

      ragSecurityService.validateSearchQuery.mockImplementation(() => {
        throw new Error('Suspicious activity detected');
      });

      await toolTenantA.testCall('{"query": "malicious query"}');

      // Verify security event logging
      expect(ragSecurityService.logSecurityEvent).toHaveBeenCalledWith(
        'suspicious_query',
        TENANT_A_ID,
        expect.objectContaining({
          query: 'malicious query',
          workspaceId: WORKSPACE_A1_ID,
          error: 'Suspicious activity detected',
        }),
      );

      // Verify audit log contains correct tenant information
      const auditLogs = toolTenantA.getSecurityAuditLogs();
      expect(auditLogs[0].tenantId).toBe(TENANT_A_ID);
      expect(auditLogs[0].workspaceId).toBe(WORKSPACE_A1_ID);
      expect(auditLogs[0].success).toBe(false);
      expect(auditLogs[0].securityLevel).toBe('high');
    });
  });
});
