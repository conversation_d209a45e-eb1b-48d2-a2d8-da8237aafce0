import { Test, TestingModule } from '@nestjs/testing';
import { NotificationToolsFactory } from './notification-tools.factory';
import { NotificationService } from '@/modules/notification/notification.service';
import { MessageCenterService } from '@/modules/workspace/message-center/services/message-center.service';

describe('NotificationToolsFactory', () => {
  let factory: NotificationToolsFactory;
  let notificationService: jest.Mocked<NotificationService>;
  let messageCenterService: jest.Mocked<MessageCenterService>;

  const mockNotificationService = {
    sendMessage: jest.fn(),
    createNotification: jest.fn(),
    markAsRead: jest.fn(),
    deleteNotification: jest.fn(),
    getUserNotifications: jest.fn(),
  };

  const mockMessageCenterService = {
    sendMessage: jest.fn(),
    getMessages: jest.fn(),
    markAsRead: jest.fn(),
    deleteMessage: jest.fn(),
    createNotification: jest.fn(),
    getNotifications: jest.fn(),
    markNotificationAsRead: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationToolsFactory,
        {
          provide: NotificationService,
          useValue: mockNotificationService,
        },
        {
          provide: MessageCenterService,
          useValue: mockMessageCenterService,
        },
      ],
    }).compile();

    factory = module.get<NotificationToolsFactory>(NotificationToolsFactory);
    notificationService = module.get(NotificationService);
    messageCenterService = module.get(MessageCenterService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createAllTools', () => {
    it('should create all notification tools for a tenant and user', () => {
      const tenantId = 'test-tenant-id';
      const userId = 'test-user-id';

      const tools = factory.createAllTools(tenantId, userId);

      expect(Array.isArray(tools)).toBe(true);
      expect(tools).toHaveLength(4);
      
      // Verify all tools are created
      const toolNames = tools.map(tool => tool.name);
      expect(toolNames).toContain('send_message');
      expect(toolNames).toContain('create_notification');
      expect(toolNames).toContain('get_notifications');
      expect(toolNames).toContain('mark_notification_read');
    });

    it('should create tools with correct tenant and user context', () => {
      const tenantId = 'tenant-123';
      const userId = 'user-456';

      const tools = factory.createAllTools(tenantId, userId);

      // All tools should be defined and have the expected structure
      tools.forEach(tool => {
        expect(tool).toBeDefined();
        expect(tool.name).toBeDefined();
        expect(tool.description).toBeDefined();
        expect(tool.schema).toBeDefined();
        expect(tool.call).toBeDefined();
      });
    });
  });

  describe('individual tool creation methods', () => {
    const tenantId = 'test-tenant';
    const userId = 'test-user';

    it('should create send message tool', () => {
      const tool = factory.createSendMessageTool(tenantId, userId);

      expect(tool).toBeDefined();
      expect(tool.name).toBe('send_message');
      expect(tool.description).toContain('Send a message');
    });

    it('should create notification tool', () => {
      const tool = factory.createNotificationTool(tenantId);

      expect(tool).toBeDefined();
      expect(tool.name).toBe('create_notification');
      expect(tool.description).toContain('Create a system notification');
    });

    it('should create get notifications tool', () => {
      const tool = factory.createGetNotificationsTool(tenantId, userId);

      expect(tool).toBeDefined();
      expect(tool.name).toBe('get_notifications');
      expect(tool.description).toContain('Retrieve notifications');
    });

    it('should create mark notification read tool', () => {
      const tool = factory.createMarkNotificationReadTool(tenantId, userId);

      expect(tool).toBeDefined();
      expect(tool.name).toBe('mark_notification_read');
      expect(tool.description).toContain('Mark a specific notification');
    });
  });

  describe('service injection', () => {
    it('should inject NotificationService correctly', () => {
      expect(factory).toBeDefined();
      expect(notificationService).toBeDefined();
    });

    it('should inject MessageCenterService correctly', () => {
      expect(factory).toBeDefined();
      expect(messageCenterService).toBeDefined();
    });
  });

  describe('tool isolation', () => {
    it('should create separate tool instances for different tenants', () => {
      const tenant1Tools = factory.createAllTools('tenant-1', 'user-1');
      const tenant2Tools = factory.createAllTools('tenant-2', 'user-2');

      expect(tenant1Tools).toHaveLength(4);
      expect(tenant2Tools).toHaveLength(4);
      
      // Tools should be different instances
      expect(tenant1Tools[0]).not.toBe(tenant2Tools[0]);
    });

    it('should create separate tool instances for different users', () => {
      const user1Tools = factory.createAllTools('tenant-1', 'user-1');
      const user2Tools = factory.createAllTools('tenant-1', 'user-2');

      expect(user1Tools).toHaveLength(4);
      expect(user2Tools).toHaveLength(4);
      
      // Tools should be different instances
      expect(user1Tools[0]).not.toBe(user2Tools[0]);
    });
  });

  describe('tool functionality', () => {
    it('should create tools that have proper schema validation', () => {
      const tenantId = 'test-tenant';
      const userId = 'test-user';
      
      const tools = factory.createAllTools(tenantId, userId);
      
      tools.forEach(tool => {
        expect(tool.schema).toBeDefined();
        expect(typeof tool.schema.parse).toBe('function');
      });
    });

    it('should create tools with async call functions', () => {
      const tenantId = 'test-tenant';
      const userId = 'test-user';
      
      const tools = factory.createAllTools(tenantId, userId);
      
      tools.forEach(tool => {
        expect(typeof tool.call).toBe('function');
      });
    });
  });
}); 