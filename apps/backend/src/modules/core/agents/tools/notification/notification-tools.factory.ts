import { NotificationService } from '@/modules/notification/notification.service';
import { MessageCenterService } from '@/modules/workspace/message-center/services/message-center.service';
import { createSendMessageTool } from './send-message.tool';
import { createNotificationTool } from './create-notification.tool';
import { createGetNotificationsTool } from './get-notifications.tool';
import { createMarkNotificationReadTool } from './mark-notification-read.tool';

/**
 * NotificationToolsFactory creates LangChain tools for notification operations
 * 
 * This factory provides a convenient way to create all notification-related tools
 * with proper dependency injection and tenant isolation.
 */
export class NotificationToolsFactory {
  constructor(
    private readonly notificationService: NotificationService,
    private readonly messageCenterService: MessageCenterService,
  ) {}

  /**
   * Create all notification tools for a specific tenant and user
   * @param tenantId - The tenant ID for isolation
   * @param userId - The user ID for the current user
   * @returns Array of configured LangChain tools
   */
  createAllTools(tenantId: string, userId: string) {
    return [
      createSendMessageTool(this.notificationService, tenantId, userId),
      createNotificationTool(this.messageCenterService, tenantId),
      createGetNotificationsTool(this.messageCenterService, tenantId, userId),
      createMarkNotificationReadTool(this.messageCenterService, tenantId, userId),
    ];
  }

  /**
   * Create individual notification tools
   */
  createSendMessageTool(tenantId: string, userId: string) {
    return createSendMessageTool(this.notificationService, tenantId, userId);
  }

  createNotificationTool(tenantId: string) {
    return createNotificationTool(this.messageCenterService, tenantId);
  }

  createGetNotificationsTool(tenantId: string, userId: string) {
    return createGetNotificationsTool(this.messageCenterService, tenantId, userId);
  }

  createMarkNotificationReadTool(tenantId: string, userId: string) {
    return createMarkNotificationReadTool(this.messageCenterService, tenantId, userId);
  }
} 