import { z } from 'zod';
import { ProgressService } from '@/modules/workspace/progress/progress.service';
import { ProgressType } from '@prisma/client';
import { ToolExecutionContext, ToolConfig } from '../core/tool-registry.interface';
import { ToolExecutionError } from '../core/tool.errors';

export interface ProgressUpdateConfig {
  allowAutoTimestamp?: boolean;
  defaultProgressType?: ProgressType;
  maxPhotoUrls?: number;
}

export interface ProgressUpdateInput {
  title: string;
  description?: string;
  progressType: ProgressType;
  progressValue?: number;
  projectId?: string;
  taskId?: string;
  photoUrls?: string[];
  notes?: string;
  status?: string;
  recordedAt?: string;
  metadata?: any;
}

/**
 * 創建進度更新工具
 */
export function createProgressUpdateTool(
  progressService: ProgressService,
  context: ToolExecutionContext,
  config?: ProgressUpdateConfig,
) {
  const inputSchema = z.object({
    title: z.string().min(1).describe('進度更新標題'),
    description: z.string().optional().describe('進度更新描述（可選）'),
    progressType: z
      .nativeEnum(ProgressType)
      .describe(`進度類型：${Object.values(ProgressType).join(', ')}`),
    progressValue: z.number().min(0).max(100).optional().describe('進度百分比（0-100，可選）'),
    projectId: z.string().optional().describe('專案ID（與taskId二選一）'),
    taskId: z.string().optional().describe('任務ID（與projectId二選一）'),
    photoUrls: z
      .array(z.string().url())
      .max(config?.maxPhotoUrls || 10)
      .optional()
      .describe('相關照片URLs（可選）'),
    notes: z.string().optional().describe('備註（可選）'),
    status: z.string().optional().describe('狀態（可選）'),
    recordedAt: z.string().datetime().optional().describe('記錄時間（ISO 8601格式，可選）'),
    metadata: z.any().optional().describe('額外的元資料（可選）'),
  });

  return {
    name: 'progress_update',
    description: `進度更新工具 - 更新專案或任務的進度資訊

此工具允許您記錄和更新專案或任務的進度狀態，支援多種進度類型和豐富的資訊。

功能特色：
- 支援多種進度類型（${Object.values(ProgressType).join(', ')}）
- 可附加照片和詳細備註
- 自動時間戳記錄
- 多租戶隔離確保資料安全
- 靈活的元資料支援

使用方式：
- title: 進度更新標題（必要）
- progressType: 進度類型（必要）
- projectId 或 taskId: 二選一（必要）
- progressValue: 進度百分比 0-100（可選）
- description: 詳細描述（可選）
- photoUrls: 相關照片連結（可選）
- notes: 備註資訊（可選）

範例：
{
  "title": "完成用戶認證模組",
  "description": "已完成登入、註冊和密碼重置功能",
  "progressType": "TASK_COMPLETION",
  "progressValue": 85,
  "projectId": "clxxxxxxxxxxxxx",
  "notes": "等待前端整合測試"
}`,
    schema: inputSchema,
    call: async (input: ProgressUpdateInput) => {
      try {
        const {
          title,
          description,
          progressType,
          progressValue,
          projectId,
          taskId,
          photoUrls,
          notes,
          status,
          recordedAt,
          metadata,
        } = input;

        // 驗證必要欄位
        if (!projectId && !taskId) {
          throw new ToolExecutionError(
            'progress_update',
            '必須提供 projectId 或 taskId 其中之一。',
            { input },
          );
        }

        if (projectId && taskId) {
          throw new ToolExecutionError(
            'progress_update',
            '不能同時提供 projectId 和 taskId，請擇一使用。',
            { input },
          );
        }

        // 準備進度更新資料
        const progressData = {
          title,
          description,
          progress_type: progressType,
          progress_value: progressValue,
          project_id: projectId,
          task_id: taskId,
          photo_urls: photoUrls,
          notes,
          status,
          recorded_at: recordedAt ? new Date(recordedAt) : new Date(),
          metadata,
        };

        // 調用 ProgressService 建立進度記錄
        const newEntry = await progressService.create(
          progressData,
          context.tenantId,
          context.userId,
        );

        return formatSuccessResponse(newEntry);
      } catch (error: any) {
        if (error instanceof ToolExecutionError) {
          throw error;
        }

        if (error instanceof z.ZodError) {
          const errorMessages = error.errors
            .map((e) => `${e.path.join('.')}: ${e.message}`)
            .join(', ');
          throw new ToolExecutionError(
            'progress_update',
            `資料驗證錯誤: ${errorMessages}`,
            { input, zodErrors: error.errors },
          );
        }

        throw new ToolExecutionError(
          'progress_update',
          `進度更新失敗: ${error.message}`,
          { originalError: error, input },
        );
      }
    },
  };
}

function formatSuccessResponse(progressEntry: any): string {
  const sections = [
    `✅ 進度更新成功！`,
    ``,
    `=== 進度記錄詳細資訊 ===`,
    `記錄 ID: ${progressEntry.id}`,
    `標題: ${progressEntry.title}`,
    `進度類型: ${translateProgressType(progressEntry.progress_type)}`,
  ];

  if (progressEntry.description) {
    sections.push(`描述: ${progressEntry.description}`);
  }

  if (progressEntry.progress_value !== undefined && progressEntry.progress_value !== null) {
    sections.push(`進度: ${progressEntry.progress_value}%`);
  }

  if (progressEntry.project_id) {
    sections.push(`專案 ID: ${progressEntry.project_id}`);
  }

  if (progressEntry.task_id) {
    sections.push(`任務 ID: ${progressEntry.task_id}`);
  }

  if (progressEntry.status) {
    sections.push(`狀態: ${progressEntry.status}`);
  }

  if (progressEntry.notes) {
    sections.push(`備註: ${progressEntry.notes}`);
  }

  if (progressEntry.photo_urls && progressEntry.photo_urls.length > 0) {
    sections.push(`附加照片: ${progressEntry.photo_urls.length} 張`);
    progressEntry.photo_urls.forEach((url: string, index: number) => {
      sections.push(`  ${index + 1}. ${url}`);
    });
  }

  if (progressEntry.recorded_at) {
    sections.push(`記錄時間: ${new Date(progressEntry.recorded_at).toLocaleString('zh-TW')}`);
  }

  if (progressEntry.metadata) {
    sections.push(`額外資訊: ${JSON.stringify(progressEntry.metadata, null, 2)}`);
  }

  sections.push(``, `進度記錄已成功建立並保存。`);

  return sections.join('\n');
}

function translateProgressType(progressType: ProgressType): string {
  const typeMap: Record<ProgressType, string> = {
    [ProgressType.TASK_UPDATE]: '任務更新',
    [ProgressType.MILESTONE]: '里程碑',
    [ProgressType.PHOTO_EVIDENCE]: '照片證據',
    [ProgressType.STATUS_CHANGE]: '狀態變更',
    [ProgressType.QUALITY_CHECK]: '質量檢查',
    [ProgressType.RESOURCE_UPDATE]: '資源更新',
    [ProgressType.RISK_IDENTIFIED]: '風險識別',
    [ProgressType.ISSUE_REPORTED]: '問題報告',
  };

  return typeMap[progressType] || progressType;
}

/**
 * 進度更新工具Factory類別
 */
export class ProgressUpdateToolsFactory {
  static createProgressUpdateTool(
    progressService: ProgressService,
    context: ToolExecutionContext,
    config?: ProgressUpdateConfig,
  ) {
    return createProgressUpdateTool(progressService, context, config);
  }

  static createLangChainTools(
    progressService: ProgressService,
    context: ToolExecutionContext,
    config?: ProgressUpdateConfig,
  ) {
    return [createProgressUpdateTool(progressService, context, config)];
  }
} 