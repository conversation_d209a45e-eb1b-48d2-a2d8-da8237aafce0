import { ProgressUpdateTool } from './progress-update.tool';
import { ProgressService } from '@/modules/workspace/progress/progress.service';
import { ProgressType } from '@prisma/client';
import { progress_entries } from '@prisma/client';

describe('ProgressUpdateTool', () => {
  let tool: ProgressUpdateTool;
  const mockProgressService = {
    create: jest.fn(),
  };
  const tenantId = 'test-tenant-id';
  const userId = 'test-user-id';

  beforeEach(() => {
    jest.clearAllMocks();
    tool = new ProgressUpdateTool(mockProgressService as any, tenantId, userId);
  });

  it('should create a progress entry successfully', async () => {
    const input = {
      title: 'Foundation laid',
      progress_type: ProgressType.TASK_UPDATE,
      project_id: 'project-123',
    };
    const mockResult: progress_entries = {
      id: 'entry-1',
      title: 'Foundation laid',
      description: null,
      progress_type: ProgressType.TASK_UPDATE,
      progress_value: null,
      status: null,
      notes: null,
      photo_urls: [],
      metadata: {},
      project_id: 'project-123',
      task_id: null,
      tenant_id: tenantId,
      user_id: userId,
      recorded_at: new Date(),
      created_at: new Date(),
      updated_at: new Date(),
    };
    mockProgressService.create.mockResolvedValue(mockResult);

    const result = await tool.call(JSON.stringify(input));

    expect(mockProgressService.create).toHaveBeenCalledWith(
      expect.objectContaining(input),
      tenantId,
      userId,
    );
    expect(result).toBe(
      `Successfully created progress entry with ID: ${mockResult.id}. Title: "${mockResult.title}"`,
    );
  });

  it('should return an error if neither project_id nor task_id is provided', async () => {
    const input = {
      title: 'Just a title',
      progress_type: ProgressType.STATUS_CHANGE,
    };
    const result = await tool.call(JSON.stringify(input));
    expect(mockProgressService.create).not.toHaveBeenCalled();
    expect(result).toBe(
      'Error: Progress update failed. The JSON object must contain either a "project_id" or a "task_id".',
    );
  });

  it('should return an error for invalid JSON input', async () => {
    const invalidJson = '{"title": "test",}'; // Invalid JSON with trailing comma
    const result = await tool.call(invalidJson);
    expect(mockProgressService.create).not.toHaveBeenCalled();
    expect(result).toBe('Error: Invalid input. The provided string is not a valid JSON object.');
  });

  it('should return a Zod validation error for incorrect data types', async () => {
    const input = {
      title: 'Wrong type',
      progress_type: ProgressType.RISK_IDENTIFIED,
      project_id: 'project-123',
      progress_value: 'not-a-number', // incorrect type
    };
    const result = await tool.call(JSON.stringify(input));
    expect(mockProgressService.create).not.toHaveBeenCalled();
    expect(result).toContain('Error: Invalid input format.');
    expect(result).toContain('progress_value: Expected number, received string');
  });

  it('should handle errors from the progress service', async () => {
    const input = {
      title: 'Service error test',
      progress_type: ProgressType.STATUS_CHANGE,
      project_id: 'project-123',
    };
    const errorMessage = 'Database connection failed';
    mockProgressService.create.mockRejectedValue(new Error(errorMessage));
    const result = await tool.call(JSON.stringify(input));
    expect(result).toBe(`Error: Progress update failed. ${errorMessage}`);
  });
});
