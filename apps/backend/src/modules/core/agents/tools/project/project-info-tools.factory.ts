import { z } from 'zod';
import { ProjectsService } from '@/modules/workspace/projects/projects.service';
import { ToolExecutionContext, ToolConfig } from '../core/tool-registry.interface';
import { ToolExecutionError } from '../core/tool.errors';

export interface ProjectInfoConfig {
  defaultLimit?: number;
}

export interface ProjectInfoInput {
  query: string;
  projectId?: string;
  filters?: Record<string, string>;
}

/**
 * 創建專案資訊查詢工具
 */
export function createProjectInfoTool(
  projectsService: ProjectsService,
  context: ToolExecutionContext,
  config?: ProjectInfoConfig,
) {
  const inputSchema = z.object({
    query: z.string().min(1).describe('專案查詢字串：專案ID、名稱或查詢條件'),
    projectId: z.string().optional().describe('特定的專案ID（可選）'),
    filters: z.record(z.string()).optional().describe('篩選條件（可選）'),
  });

  return {
    name: 'project_info',
    description: `專案資訊查詢工具 - 查詢專案狀態和詳細資訊

此工具允許您查詢專案的完整資訊，包括基本資料、狀態、進度等。

功能特色：
- 支援多種查詢方式（ID、名稱、條件篩選）
- 多租戶隔離確保資料安全
- 詳細的專案資訊格式化輸出
- 自動錯誤處理和友好提示

使用方式：
- query: 專案查詢字串（必要）
  - 專案ID：如 "clxxxxxxxxxxxxx"
  - 專案名稱：如 "新產品開發專案"
  - 查詢條件：如 "status:active" 或 "priority:high"
- projectId: 特定專案ID（可選）
- filters: 額外的篩選條件（可選）

支援的篩選條件：
- status: active, completed, cancelled
- priority: high, medium, low`,
    schema: inputSchema,
    call: async (input: ProjectInfoInput) => {
      try {
        const { query, projectId, filters } = input;
        const tenantId = context.tenantId;

        if (projectId) {
          // 直接按ID查詢
          const project = await projectsService.findOne(projectId, tenantId);
          return formatProjectDetails(project);
        }

        // 解析查詢字串
        const queryParams = parseInput(query);

        if (queryParams.projectId) {
          // 根據 ID 查詢單一專案
          const project = await projectsService.findOne(queryParams.projectId, tenantId);
          return formatProjectDetails(project);
        } else if (queryParams.projectName) {
          // 根據名稱搜尋專案
          const result = await projectsService.findAll(
            tenantId,
            1, // page
            10, // limit
            queryParams.projectName, // search
          );

          if (result.projects.length === 0) {
            return `找不到名稱包含 "${queryParams.projectName}" 的專案。`;
          }

          if (result.projects.length === 1) {
            const project = await projectsService.findOne(result.projects[0].id, tenantId);
            return formatProjectDetails(project);
          }

          return formatProjectList(result.projects, `搜尋 "${queryParams.projectName}" 的結果`);
        } else if (queryParams.filters || filters) {
          // 根據條件篩選專案
          const mergedFilters = { ...queryParams.filters, ...filters };
          const result = await projectsService.findAll(
            tenantId,
            1, // page
            config?.defaultLimit || 20, // limit
            undefined, // search
            mergedFilters.status,
            mergedFilters.priority,
          );

          if (result.projects.length === 0) {
            const filterDesc = Object.entries(mergedFilters)
              .map(([key, value]) => `${key}=${value}`)
              .join(', ');
            return `找不到符合條件 (${filterDesc}) 的專案。`;
          }

          return formatProjectList(result.projects, '篩選結果');
        } else {
          // 預設返回所有專案概覽
          const result = await projectsService.findAll(tenantId, 1, 10);

          if (result.projects.length === 0) {
            return '目前沒有任何專案。';
          }

          return formatProjectList(result.projects, '專案概覽');
        }
      } catch (error: any) {
        throw new ToolExecutionError(
          'project_info',
          `專案資訊查詢失敗: ${error.message}`,
          { originalError: error, input },
        );
      }
    },
  };
}

function parseInput(input: string): {
  projectId?: string;
  projectName?: string;
  filters?: Record<string, string>;
} {
  const trimmedInput = input.trim();

  // 檢查是否為 CUID 格式的 ID
  if (/^cl[a-z0-9]{24}$/i.test(trimmedInput)) {
    return { projectId: trimmedInput };
  }

  // 檢查是否為查詢條件格式（如 "status:active"）
  if (trimmedInput.includes(':')) {
    const filters: Record<string, string> = {};
    const conditions = trimmedInput.split(',').map((c) => c.trim());

    for (const condition of conditions) {
      const [key, value] = condition.split(':').map((s) => s.trim());
      if (key && value) {
        filters[key] = value;
      }
    }

    if (Object.keys(filters).length > 0) {
      return { filters };
    }
  }

  // 否則視為專案名稱
  return { projectName: trimmedInput };
}

function formatProjectDetails(project: any): string {
  const sections = [
    `=== 專案詳細資訊 ===`,
    `專案名稱: ${project.name}`,
    `專案 ID: ${project.id}`,
    `狀態: ${translateStatus(project.status)}`,
    `優先級: ${translatePriority(project.priority)}`,
  ];

  if (project.description) {
    sections.push(`描述: ${project.description}`);
  }

  if (project.startDate) {
    sections.push(`開始日期: ${new Date(project.startDate).toLocaleDateString('zh-TW')}`);
  }

  if (project.endDate) {
    sections.push(`結束日期: ${new Date(project.endDate).toLocaleDateString('zh-TW')}`);
  }

  if (project.budget) {
    sections.push(`預算: ${project.budget.toLocaleString()}`);
  }

  if (project.actualBudget) {
    sections.push(`實際預算: ${project.actualBudget.toLocaleString()}`);
  }

  if (project.progress !== undefined) {
    sections.push(`進度: ${project.progress}%`);
  }

  if (project.createdAt) {
    sections.push(`建立時間: ${new Date(project.createdAt).toLocaleString('zh-TW')}`);
  }

  if (project.updatedAt) {
    sections.push(`更新時間: ${new Date(project.updatedAt).toLocaleString('zh-TW')}`);
  }

  // 顯示專案成員
  if (project.members && project.members.length > 0) {
    sections.push(`\n=== 專案成員 ===`);
    project.members.forEach((member: any) => {
      sections.push(`- ${member.user?.name || '未知使用者'} (${member.role || '一般成員'})`);
    });
  }

  // 顯示專案任務
  if (project.tasks && project.tasks.length > 0) {
    sections.push(`\n=== 專案任務 ===`);
    project.tasks.slice(0, 5).forEach((task: any) => {
      sections.push(
        `- ${task.title} [${translateStatus(task.status)}] - ${task.assignee?.name || '未指派'}`,
      );
    });

    if (project.tasks.length > 5) {
      sections.push(`... 還有 ${project.tasks.length - 5} 個任務`);
    }
  }

  return sections.join('\n');
}

function formatProjectList(projects: any[], title: string): string {
  const sections = [`=== ${title} ===`, `共找到 ${projects.length} 個專案:\n`];

  projects.forEach((project, index) => {
    const status = translateStatus(project.status);
    const priority = translatePriority(project.priority);
    const progress = project.progress !== undefined ? ` (${project.progress}%)` : '';

    sections.push(
      `${index + 1}. ${project.name}`,
      `   ID: ${project.id}`,
      `   狀態: ${status} | 優先級: ${priority}${progress}`,
      `   描述: ${project.description || '無描述'}`,
      '',
    );
  });

  return sections.join('\n');
}

function translateStatus(status: string): string {
  const statusMap: Record<string, string> = {
    active: '進行中',
    completed: '已完成',
    cancelled: '已取消',
    on_hold: '暫停',
    planning: '規劃中',
  };
  return statusMap[status] || status;
}

function translatePriority(priority: string): string {
  const priorityMap: Record<string, string> = {
    high: '高',
    medium: '中',
    low: '低',
    urgent: '緊急',
  };
  return priorityMap[priority] || priority;
}

/**
 * 專案資訊工具Factory類別
 */
export class ProjectInfoToolsFactory {
  static createProjectInfoTool(
    projectsService: ProjectsService,
    context: ToolExecutionContext,
    config?: ProjectInfoConfig,
  ) {
    return createProjectInfoTool(projectsService, context, config);
  }

  static createLangChainTools(
    projectsService: ProjectsService,
    context: ToolExecutionContext,
    config?: ProjectInfoConfig,
  ) {
    return [createProjectInfoTool(projectsService, context, config)];
  }
} 