import { Test, TestingModule } from '@nestjs/testing';
import { ToolsModule } from './tools.module';
import { ToolRegistrationService } from './tool-registration.service';
import { NotificationTool } from './implementations/notification.tool';
import { NotificationService } from '@/modules/notification/notification.service';
import { MessageCenterService } from '@/modules/workspace/message-center/services/message-center.service';

// Mock services
const mockNotificationService = {
  sendMessage: jest.fn(),
  createNotification: jest.fn(),
};

const mockMessageCenterService = {
  createNotification: jest.fn(),
  getNotifications: jest.fn(),
  markNotificationAsRead: jest.fn(),
};

describe('Tools Integration', () => {
  let module: TestingModule;
  let toolRegistrationService: ToolRegistrationService;
  let notificationTool: NotificationTool;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [ToolsModule],
    })
      .overrideProvider(NotificationService)
      .useValue(mockNotificationService)
      .overrideProvider(MessageCenterService)
      .useValue(mockMessageCenterService)
      .compile();

    toolRegistrationService = module.get<ToolRegistrationService>(ToolRegistrationService);
    notificationTool = module.get<NotificationTool>(NotificationTool);
  });

  afterEach(async () => {
    await module.close();
  });

  it('should be defined', () => {
    expect(toolRegistrationService).toBeDefined();
    expect(notificationTool).toBeDefined();
  });

  it('should have NotificationTool properly configured', () => {
    const config = notificationTool.getConfig();
    expect(config).toBeDefined();
    expect(config.name).toBe('notification_tool');
    expect(config.displayName).toBe('Notification Tool');
    expect(config.isEnabled).toBe(true);
  });

  it('should register tools on application bootstrap', async () => {
    // Trigger the bootstrap event
    await toolRegistrationService.onApplicationBootstrap();

    // Check if tools are registered
    const registeredTools = toolRegistrationService.getRegisteredTools();
    expect(registeredTools).toBeDefined();
    expect(Array.isArray(registeredTools)).toBe(true);
  });

  it('should execute NotificationTool successfully', async () => {
    const mockContext = {
      tenantId: 'test-tenant',
      userId: 'test-user',
      workspaceId: 'test-workspace',
      ability: {} as any,
      user: {} as any,
      input: {
        operation: 'send_message',
        channel: 'internal',
        recipient_type: 'user',
        recipient_id: 'recipient-123',
        message: 'Test integration message',
      },
    };

    mockNotificationService.sendMessage.mockResolvedValue(undefined);

    const result = await notificationTool.execute(mockContext);

    expect(result).toBeDefined();
    expect(result.success).toBe(true);
    expect(result.data?.success).toBe(true);
    expect(mockNotificationService.sendMessage).toHaveBeenCalledWith(
      {
        channel: 'internal',
        recipient_type: 'user',
        recipient_id: 'recipient-123',
        message: 'Test integration message',
        message_type: undefined,
      },
      'test-tenant',
      'test-user',
    );
  });
});
