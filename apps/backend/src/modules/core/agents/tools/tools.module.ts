import { Module } from '@nestjs/common';
import { ToolRegistryService, ToolExecutorService } from './core';
import { ToolRegistrationService } from './tool-registration.service';
import { ModelsModule } from '@/modules/ai/models/models.module';
import { NotificationModule } from '@/modules/notification/notification.module';
import { MessageCenterModule } from '@/modules/workspace/message-center/message-center.module';
import { RAGModule } from '@/modules/ai/rag/rag.module';
import { PrismaModule } from '@/modules/core/prisma/prisma.module';
// Modern tools are now available as factory functions from ./implementations
// All tool implementations have been converted to the new architecture

/**
 * 工具模組
 *
 * 負責提供工具執行框架的所有服務，包括：
 * - 工具註冊表管理
 * - 工具執行器
 * - 工具自動註冊
 * - 與 AI 模組的整合
 * - 基礎工具實作（通知、知識庫、檔案讀取等）
 */
@Module({
  imports: [
    PrismaModule, // 引入 Prisma 模組以支援資料庫操作
    ModelsModule, // 引入 AI 模組以使用 AiToolsService 和 AiBotsService
    NotificationModule, // 引入通知模組
    MessageCenterModule, // 引入訊息中心模組
    RAGModule, // 引入 RAG 模組以支援知識庫工具
  ],
  providers: [
    ToolRegistryService,
    ToolExecutorService,
    ToolRegistrationService, // 自動註冊工具
    // Modern tools (All tools) are now available as factory functions
  ],
  exports: [
    ToolRegistryService,
    ToolExecutorService,
    ToolRegistrationService,
    // Modern tools are exported via factory functions from implementations/index.ts
  ],
})
export class ToolsModule {}
