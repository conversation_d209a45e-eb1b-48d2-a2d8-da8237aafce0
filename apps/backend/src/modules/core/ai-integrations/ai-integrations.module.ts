import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { CaslModule } from '@/casl/casl.module';
import { PrismaModule } from '../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module';
import { EncryptionModule } from '../encryption/encryption.module';
import { AiIntegrationsController } from './ai-integrations.controller';
import { AiIntegrationsService } from './ai-integrations.service';
import { AiProviderFactory } from '../../ai/models/core/providers/factory';

@Module({
  imports: [
    HttpModule,
    CaslModule,
    PrismaModule,
    AuthModule,
    EncryptionModule,
  ],
  controllers: [AiIntegrationsController],
  providers: [
    AiIntegrationsService,
    AiProviderFactory,
  ],
  exports: [
    AiIntegrationsService,
    AiProviderFactory,
  ],
})
export class AiIntegrationsModule {} 