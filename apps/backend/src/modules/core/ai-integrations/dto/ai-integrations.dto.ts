import {
  IsString,
  IsOptional,
  IsEnum,
  IsBoolean,
  IsDateString,
  IsArray,
  IsNumber,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ProjectAnalysisDto {
  @ApiProperty({ description: '專案 ID' })
  @IsString()
  projectId: string;

  @ApiProperty({ description: '租戶 ID' })
  @IsString()
  tenantId: string;

  @ApiProperty({
    description: '分析類型',
    enum: ['status', 'risk', 'performance', 'optimization'],
  })
  @IsEnum(['status', 'risk', 'performance', 'optimization'])
  analysisType: 'status' | 'risk' | 'performance' | 'optimization';

  @ApiPropertyOptional({ description: '是否包含子專案' })
  @IsOptional()
  @IsBoolean()
  includeSubProjects?: boolean;

  @ApiPropertyOptional({ description: '是否包含任務' })
  @IsOptional()
  @IsBoolean()
  includeTasks?: boolean;

  @ApiPropertyOptional({ description: '是否包含進度' })
  @IsOptional()
  @IsBoolean()
  includeProgress?: boolean;

  @ApiPropertyOptional({
    description: 'AI 分析信心度設定 (0-1)',
    minimum: 0,
    maximum: 1,
    example: 0.85,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence?: number;
}

export class PhotoAnalysisDto {
  @ApiProperty({ description: '照片 URL' })
  @IsString()
  photoUrl: string;

  @ApiProperty({ description: '專案 ID' })
  @IsString()
  projectId: string;

  @ApiProperty({ description: '租戶 ID' })
  @IsString()
  tenantId: string;

  @ApiProperty({
    description: '分析類型',
    enum: ['progress', 'quality', 'safety', 'equipment'],
  })
  @IsEnum(['progress', 'quality', 'safety', 'equipment'])
  analysisType: 'progress' | 'quality' | 'safety' | 'equipment';

  @ApiPropertyOptional({ description: '分析上下文' })
  @IsOptional()
  @IsString()
  context?: string;

  @ApiPropertyOptional({
    description: 'AI 分析信心度設定 (0-1)',
    minimum: 0,
    maximum: 1,
    example: 0.8,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence?: number;
}

export class WorkflowOptimizationDto {
  @ApiProperty({ description: '租戶 ID' })
  @IsString()
  tenantId: string;

  @ApiPropertyOptional({ description: '專案 ID（可選）' })
  @IsOptional()
  @IsString()
  projectId?: string;

  @ApiProperty({ description: '開始日期' })
  @IsDateString()
  startDate: string;

  @ApiProperty({ description: '結束日期' })
  @IsDateString()
  endDate: string;

  @ApiProperty({
    description: '包含的指標',
    type: [String],
    example: ['taskCompletion', 'efficiency', 'resourceUsage'],
  })
  @IsArray()
  @IsString({ each: true })
  includeMetrics: string[];

  @ApiPropertyOptional({
    description: 'AI 分析信心度設定 (0-1)',
    minimum: 0,
    maximum: 1,
    example: 0.85,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence?: number;
}
