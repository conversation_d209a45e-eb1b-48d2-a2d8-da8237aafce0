import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * 用戶資訊 DTO
 */
export class UserInfoDto {
  @ApiProperty({ description: '用戶 ID' })
  @Expose()
  id: string;

  @ApiProperty({ description: '電子郵件' })
  @Expose()
  email: string;

  @ApiProperty({ description: '用戶名稱' })
  @Expose()
  name: string;

  @ApiProperty({ description: '用戶類型', enum: ['system', 'tenant'] })
  @Expose()
  user_type: 'system' | 'tenant';

  @ApiProperty({ description: '租戶 ID', required: false })
  @Expose()
  tenant_id?: string | null;
}

/**
 * 登入響應 DTO
 * 用於返回登入成功後的用戶資訊和令牌
 */
export class LoginResponseDto {
  @ApiProperty({ description: '存取令牌' })
  @Expose()
  access_token: string;

  @ApiProperty({ description: '刷新令牌' })
  @Expose()
  refresh_token: string;

  @ApiProperty({ description: '用戶資訊', type: UserInfoDto })
  @Expose()
  user: UserInfoDto;

  @ApiProperty({ description: '用戶類型', enum: ['system', 'tenant'] })
  @Expose()
  user_type: 'system' | 'tenant';

  @ApiProperty({ description: '用戶權限規則', type: 'array', required: false })
  @Expose()
  ability_rules?: any[];
}
