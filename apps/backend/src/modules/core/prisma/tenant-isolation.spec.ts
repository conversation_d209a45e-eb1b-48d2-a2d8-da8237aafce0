import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from './prisma.service';
import { ClsService } from 'nestjs-cls';

describe('PrismaService Tenant Isolation', () => {
  let service: PrismaService;
  let clsService: ClsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PrismaService,
        {
          provide: ClsService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<PrismaService>(PrismaService);
    clsService = module.get<ClsService>(ClsService);
  });

  afterEach(async () => {
    await service.$disconnect();
  });

  describe('Tenant ID Injection', () => {
    it('should inject tenant_id for findMany operations', async () => {
      const mockTenantId = 'tenant-123';
      (clsService.get as jest.Mock).mockReturnValue(mockTenantId);

      const params = {
        model: 'projects',
        action: 'findMany',
        args: {
          where: {
            name: 'Test Project',
          } as any,
        },
      };

      // Simulate middleware behavior - inject tenant_id into where clause
      if (params.args.where) {
        params.args.where = {
          ...params.args.where,
          tenant_id: mockTenantId,
        };
      } else {
        params.args.where = {
          tenant_id: mockTenantId,
        };
      }

      // Test middleware logic manually
      expect(params.args.where).toEqual({
        name: 'Test Project',
        tenant_id: mockTenantId,
      });
    });

    it('should prevent cross-tenant data access', async () => {
      const mockTenantId = 'tenant-123';
      (clsService.get as jest.Mock).mockReturnValue(mockTenantId);

      const params = {
        model: 'projects',
        action: 'findFirst',
        args: {
          where: {
            tenant_id: 'tenant-456', // Different tenant
            name: 'Test Project',
          },
        },
      };

      // This should throw an error in real implementation
      expect(() => {
        if (params.args.where.tenant_id !== mockTenantId) {
          throw new Error(
            'Tenant isolation violation: Attempted to access data from different tenant',
          );
        }
      }).toThrow('Tenant isolation violation');
    });

    it('should inject tenant_id for create operations', async () => {
      const mockTenantId = 'tenant-123';
      (clsService.get as jest.Mock).mockReturnValue(mockTenantId);

      const params = {
        model: 'projects',
        action: 'create',
        args: {
          data: {
            name: 'New Project',
            description: 'Test description',
            tenant_id: undefined as string | undefined,
          },
        },
      };

      // Simulate middleware behavior
      params.args.data.tenant_id = mockTenantId;

      expect(params.args.data).toEqual({
        name: 'New Project',
        description: 'Test description',
        tenant_id: mockTenantId,
      });
    });

    it('should handle createMany operations', async () => {
      const mockTenantId = 'tenant-123';
      (clsService.get as jest.Mock).mockReturnValue(mockTenantId);

      const params = {
        model: 'tasks',
        action: 'createMany',
        args: {
          data: [
            { title: 'Task 1', project_id: 'proj-1', tenant_id: undefined as string | undefined },
            { title: 'Task 2', project_id: 'proj-2', tenant_id: undefined as string | undefined },
          ],
        },
      };

      // Simulate middleware behavior
      params.args.data = params.args.data.map((item: any) => ({
        ...item,
        tenant_id: mockTenantId,
      }));

      expect(params.args.data).toEqual([
        { title: 'Task 1', project_id: 'proj-1', tenant_id: mockTenantId },
        { title: 'Task 2', project_id: 'proj-2', tenant_id: mockTenantId },
      ]);
    });

    it('should handle upsert operations', async () => {
      const mockTenantId = 'tenant-123';
      (clsService.get as jest.Mock).mockReturnValue(mockTenantId);

      const params = {
        model: 'projects',
        action: 'upsert',
        args: {
          where: { id: 'project-1', tenant_id: undefined as string | undefined },
          create: { name: 'New Project', tenant_id: undefined as string | undefined },
          update: { name: 'Updated Project', tenant_id: undefined as string | undefined },
        },
      };

      // Simulate middleware behavior
      params.args.where.tenant_id = mockTenantId;
      params.args.create.tenant_id = mockTenantId;
      delete params.args.update.tenant_id; // Prevent updating tenant_id

      expect(params.args.where).toEqual({
        id: 'project-1',
        tenant_id: mockTenantId,
      });
      expect(params.args.create).toEqual({
        name: 'New Project',
        tenant_id: mockTenantId,
      });
      expect(params.args.update).toEqual({
        name: 'Updated Project',
      });
    });

    it('should skip isolation when no tenant_id is set', async () => {
      (clsService.get as jest.Mock).mockReturnValue(null);

      const params = {
        model: 'projects',
        action: 'findMany',
        args: {
          where: {
            name: 'Test Project',
          },
        },
      };

      // Should not modify params when no tenant_id
      expect(params.args.where).toEqual({
        name: 'Test Project',
      });
    });

    it('should handle models not requiring tenant isolation', async () => {
      const mockTenantId = 'tenant-123';
      (clsService.get as jest.Mock).mockReturnValue(mockTenantId);

      const params = {
        model: 'ai_keys', // System-level model
        action: 'findMany',
        args: {
          where: {
            provider: 'openai',
          },
        },
      };

      // Should not modify params for non-tenant models
      expect(params.args.where).toEqual({
        provider: 'openai',
      });
    });
  });

  describe('Tenant Model Coverage', () => {
    it('should include all required tenant models', () => {
      const requiredModels = [
        'ai_photo_analysis_results',
        'albums',
        'orders',
        'photos',
        'projects',
        'tasks',
        'progress_entries',
        'project_milestones',
        'progress_reports',
        'tenant_credit_purchases',
        'tenant_invitations',
        'tenant_users',
        'workspaces',
        'workspace_members',
        'workspace_activity_logs',
        'ai_workflows',
        'workflow_nodes',
        'workflow_executions',
        'tenant_lifecycle_events',
        'comments',
        'comment_reactions',
        'comment_mentions',
        'shared_files',
        'file_permissions',
        'file_shares',
        'file_access_logs',
        'message_conversations',
        'messages',
        'message_files',
        'file_attachments',
        'dashboard_charts',
        'dashboard_settings',
        'notifications',
        'notification_settings',
        'ai_bots',
        'ai_usage_logs',
        'line_bots',
        'line_group_verifications',
        'line_message_logs',
      ];

      // 檢查模型列表的基本要求
      expect(requiredModels.length).toBeGreaterThan(25);
      expect(requiredModels).toContain('projects');
      expect(requiredModels).toContain('tasks');
      expect(requiredModels).toContain('photos');
      expect(requiredModels).toContain('workspaces');
      expect(requiredModels).toContain('ai_bots');
      expect(requiredModels).toContain('line_bots');
      expect(requiredModels).toContain('ai_usage_logs');
      expect(requiredModels).toContain('line_group_verifications');
      expect(requiredModels).toContain('line_message_logs');
    });

    it('should include optional tenant models', () => {
      const optionalModels = ['roles', 'subscriptions', 'system_logs'];

      expect(optionalModels).toContain('roles');
      expect(optionalModels).toContain('system_logs');
      expect(optionalModels.length).toBe(3);
    });
  });
});
