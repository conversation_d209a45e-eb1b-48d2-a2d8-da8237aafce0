import { Injectable } from '@nestjs/common';
import { BaseStorageService } from '../base/base-storage.service';
import { StorageConfig, UploadOptions, FileInfo } from '../interfaces/storage.interface';
import { Storage, Bucket, File } from '@google-cloud/storage';
import * as zlib from 'zlib';
import { promisify } from 'util';
import * as path from 'path';

/**
 * Google Cloud Storage 儲存服務
 */
@Injectable()
export class GcsStorageService extends BaseStorageService {
  private readonly storage: Storage;
  private readonly bucket: Bucket;
  private readonly bucketName: string;

  constructor(config: StorageConfig) {
    super(config);

    if (!config.bucket) {
      throw new Error('GCS bucket is required');
    }

    this.bucketName = config.bucket;

    // 初始化 Google Cloud Storage 客戶端
    this.storage = new Storage({
      projectId: config.region, // 在 GCS 中，region 字段用於存放 projectId
      keyFilename: config.accessKey, // 服務帳號金鑰檔案路徑
      // 或者使用 credentials 物件
      ...(config.secretKey && {
        credentials: JSON.parse(config.secretKey), // JSON 格式的服務帳號金鑰
      }),
    });

    this.bucket = this.storage.bucket(this.bucketName);
    this.logger.log(`GCS Storage initialized for bucket: ${this.bucketName}`);
  }

  async uploadFile(
    file: Express.Multer.File | Buffer,
    options: UploadOptions = {},
  ): Promise<string> {
    try {
      this.validateFile(file);

      let buffer: Buffer;
      let originalName: string;
      let contentType: string;

      if (file instanceof Buffer) {
        buffer = file;
        originalName = options.filename || 'file';
        contentType = options.contentType || 'application/octet-stream';
      } else {
        buffer = Buffer.from(file.buffer);
        originalName = (file as Express.Multer.File).originalname || 'file';
        contentType =
          (file as Express.Multer.File).mimetype || this.getContentTypeFromExtension(originalName);
      }

      const filename = options.filename || this.generateUniqueFilename(originalName);
      const key = this.buildPath(options.path || '', filename);

      // 壓縮檔案（如果啟用）
      if (options.compress) {
        const gzipAsync = promisify(zlib.gzip);
        buffer = await gzipAsync(buffer);
        contentType = 'application/gzip';
      }

      const fileRef = this.bucket.file(key);
      const stream = fileRef.createWriteStream({
        metadata: {
          contentType,
          metadata: options.metadata || {},
        },
        public: options.public || false,
        resumable: buffer.length > 5 * 1024 * 1024, // 使用可恢復上傳處理大檔案 (>5MB)
      });

      return new Promise((resolve, reject) => {
        stream.on('error', (error) => {
          this.logger.error('GCS upload error', error);
          reject(error);
        });

        stream.on('finish', () => {
          this.logger.log(`File uploaded to GCS: ${key}`);
          resolve(key);
        });

        stream.end(buffer);
      });
    } catch (error) {
      return this.handleError('uploadFile', error);
    }
  }

  async uploadText(content: string, options: UploadOptions): Promise<string> {
    try {
      const buffer = Buffer.from(content, 'utf8');
      const uploadOptions = {
        ...options,
        contentType: options.contentType || 'text/plain',
      };
      return this.uploadFile(buffer, uploadOptions);
    } catch (error) {
      return this.handleError('uploadText', error);
    }
  }

  async downloadFile(filePath: string): Promise<Buffer> {
    try {
      const fileRef = this.bucket.file(filePath);
      const [buffer] = await fileRef.download();

      this.logger.log(`File downloaded from GCS: ${filePath}`);
      return buffer;
    } catch (error) {
      return this.handleError('downloadFile', error);
    }
  }

  async deleteFile(filePath: string): Promise<void> {
    try {
      const fileRef = this.bucket.file(filePath);
      await fileRef.delete();

      this.logger.log(`File deleted from GCS: ${filePath}`);
    } catch (error) {
      return this.handleError('deleteFile', error);
    }
  }

  async fileExists(filePath: string): Promise<boolean> {
    try {
      const fileRef = this.bucket.file(filePath);
      const [exists] = await fileRef.exists();
      return exists;
    } catch (error) {
      this.logger.error('Error checking file existence', error);
      return false;
    }
  }

  async getFileInfo(filePath: string): Promise<FileInfo> {
    try {
      const fileRef = this.bucket.file(filePath);
      const [metadata] = await fileRef.getMetadata();

      return {
        path: filePath,
        size: parseInt(String(metadata.size || '0'), 10),
        contentType: metadata.contentType,
        lastModified: new Date(metadata.timeCreated || Date.now()),
        metadata: metadata.metadata || {},
        url: metadata.mediaLink,
      };
    } catch (error) {
      return this.handleError('getFileInfo', error);
    }
  }

  async listFiles(dirPath: string, recursive: boolean = false): Promise<string[]> {
    try {
      const prefix = dirPath.endsWith('/') ? dirPath : `${dirPath}/`;
      const options: any = {
        prefix,
      };

      if (!recursive) {
        options.delimiter = '/';
      }

      const [files] = await this.bucket.getFiles(options);

      return files.map((file) => file.name).filter((name) => name !== prefix); // 排除目錄本身
    } catch (error) {
      return this.handleError('listFiles', error);
    }
  }

  async getPublicUrl(filePath: string, expiresIn: number = 3600): Promise<string> {
    try {
      const fileRef = this.bucket.file(filePath);

      // 生成簽名 URL
      const [signedUrl] = await fileRef.getSignedUrl({
        action: 'read',
        expires: Date.now() + expiresIn * 1000,
      });

      return signedUrl;
    } catch (error) {
      return this.handleError('getPublicUrl', error);
    }
  }

  async copyFile(sourcePath: string, destinationPath: string): Promise<void> {
    try {
      const sourceFile = this.bucket.file(sourcePath);
      const destinationFile = this.bucket.file(destinationPath);

      await sourceFile.copy(destinationFile);

      this.logger.log(`File copied in GCS: ${sourcePath} -> ${destinationPath}`);
    } catch (error) {
      return this.handleError('copyFile', error);
    }
  }

  async moveFile(sourcePath: string, destinationPath: string): Promise<void> {
    try {
      const sourceFile = this.bucket.file(sourcePath);
      const destinationFile = this.bucket.file(destinationPath);

      await sourceFile.move(destinationFile);

      this.logger.log(`File moved in GCS: ${sourcePath} -> ${destinationPath}`);
    } catch (error) {
      return this.handleError('moveFile', error);
    }
  }

  async compressFiles(
    paths: string[],
    archivePath: string,
    format: 'zip' | 'gzip' = 'gzip',
  ): Promise<string> {
    try {
      if (format === 'gzip' && paths.length === 1) {
        // 單檔案 gzip 壓縮
        const sourcePath = paths[0];
        const sourceBuffer = await this.downloadFile(sourcePath);
        const gzipAsync = promisify(zlib.gzip);
        const compressedBuffer = await gzipAsync(sourceBuffer);

        const gzipPath = archivePath.endsWith('.gz') ? archivePath : `${archivePath}.gz`;
        await this.uploadFile(compressedBuffer, {
          filename: path.basename(gzipPath),
          path: path.dirname(gzipPath),
          contentType: 'application/gzip',
        });

        return gzipPath;
      }

      throw new Error(
        `Compression format '${format}' not supported for multiple files in GCS storage`,
      );
    } catch (error) {
      return this.handleError('compressFiles', error);
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      // 測試 bucket 存在性和權限
      const [exists] = await this.bucket.exists();

      if (!exists) {
        this.logger.error(`GCS bucket '${this.bucketName}' does not exist`);
        return false;
      }

      // 測試列出檔案權限
      await this.bucket.getFiles({ maxResults: 1 });

      return true;
    } catch (error) {
      this.logger.error('GCS storage connection test failed', error);
      return false;
    }
  }

  /**
   * 設定檔案的公開存取權限
   * @param filePath 檔案路徑
   * @param isPublic 是否公開
   */
  async setFilePublic(filePath: string, isPublic: boolean = true): Promise<void> {
    try {
      const fileRef = this.bucket.file(filePath);

      if (isPublic) {
        await fileRef.makePublic();
        this.logger.log(`File made public: ${filePath}`);
      } else {
        await fileRef.makePrivate();
        this.logger.log(`File made private: ${filePath}`);
      }
    } catch (error) {
      return this.handleError('setFilePublic', error);
    }
  }

  /**
   * 獲取檔案的公開 URL（無需簽名）
   * @param filePath 檔案路徑
   * @returns 公開 URL
   */
  getPublicUrlDirect(filePath: string): string {
    return `https://storage.googleapis.com/${this.bucketName}/${filePath}`;
  }

  /**
   * 設定檔案的生命週期規則
   * @param filePath 檔案路徑
   * @param deleteAfterDays 多少天後刪除
   */
  async setFileLifecycle(filePath: string, deleteAfterDays: number): Promise<void> {
    try {
      const fileRef = this.bucket.file(filePath);

      // 設定自定義元數據來追蹤生命週期
      await fileRef.setMetadata({
        metadata: {
          deleteAfterDays: deleteAfterDays.toString(),
          deleteAfter: new Date(Date.now() + deleteAfterDays * 24 * 60 * 60 * 1000).toISOString(),
        },
      });

      this.logger.log(`File lifecycle set: ${filePath} (delete after ${deleteAfterDays} days)`);
    } catch (error) {
      return this.handleError('setFileLifecycle', error);
    }
  }

  /**
   * 批次刪除檔案
   * @param filePaths 檔案路徑陣列
   */
  async deleteFiles(filePaths: string[]): Promise<void> {
    try {
      const deletePromises = filePaths.map((filePath) => {
        const fileRef = this.bucket.file(filePath);
        return fileRef.delete();
      });

      await Promise.all(deletePromises);

      this.logger.log(`Batch deleted ${filePaths.length} files from GCS`);
    } catch (error) {
      return this.handleError('deleteFiles', error);
    }
  }
}
