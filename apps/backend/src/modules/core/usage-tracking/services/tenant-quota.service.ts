import {
  Injectable,
  Logger,
  HttpException,
  HttpStatus,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { Decimal } from '@prisma/client/runtime/library';
import * as crypto from 'crypto';

export class InsufficientCreditsException extends HttpException {
  constructor(message = 'AI 信用點數不足') {
    super(message, HttpStatus.FORBIDDEN); // 403 Forbidden or 402 Payment Required might be suitable
    this.name = 'InsufficientCreditsException';
  }
}

@Injectable()
export class TenantQuotaService {
  private readonly logger = new Logger(TenantQuotaService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Checks if the tenant has enough AI credits and deducts them.
   * This operation should be part of a transaction if other dependent operations exist.
   * @param tenantId The ID of the tenant.
   * @param costToDeduct The amount of credits to deduct.
   * @param userId Optional user ID for audit logging.
   * @param context Optional context for logging (e.g., feature used, bot ID).
   * @returns True if credits were sufficient and deducted, otherwise throws InsufficientCreditsException.
   * @throws InsufficientCreditsException if credits are not enough.
   * @throws Error if tenant or plan information is missing.
   */
  async checkAndDeductCredits(
    tenantId: string,
    costToDeduct: Decimal,
    userId?: string,
    context?: {
      featureKey?: string;
      botId?: string;
      operation?: string;
      ip?: string;
    },
  ): Promise<boolean> {
    this.logger.debug(`Attempting to deduct ${costToDeduct} credits from tenant ${tenantId}`);

    if (costToDeduct.isNegative() || costToDeduct.isZero()) {
      this.logger.log(
        `Deduction amount is zero or negative (${costToDeduct}) for tenant ${tenantId}, no action taken.`,
      );

      // 記錄零成本使用日誌
      await this.logSystemEvent({
        action: 'ai_credit_check',
        tenantId,
        userId,
        message: `AI 信用點數檢查 - 零成本操作`,
        level: 'info',
        status: 'success',
        targetResource: 'ai_credits',
        targetResourceId: tenantId,
        details: {
          cost: costToDeduct.toString(),
          operation: context?.operation || 'unknown',
          featureKey: context?.featureKey,
          botId: context?.botId,
        },
        ip: context?.ip,
      });

      return true; // No cost, so it's fine
    }

    const tenant = await this.prisma.tenants.findUnique({
      where: { id: tenantId },
      select: { current_ai_credits: true, id: true, name: true },
    });

    if (!tenant) {
      this.logger.error(`Tenant with ID ${tenantId} not found.`);

      // 記錄錯誤日誌
      await this.logSystemEvent({
        action: 'ai_credit_deduct',
        tenantId,
        userId,
        message: `AI 信用點數扣除失敗 - 租戶不存在`,
        level: 'error',
        status: 'failed',
        targetResource: 'tenant',
        targetResourceId: tenantId,
        errorMessage: `找不到租戶 ${tenantId}`,
        details: {
          cost: costToDeduct.toString(),
          operation: context?.operation || 'unknown',
        },
        ip: context?.ip,
      });

      throw new NotFoundException(`找不到租戶 ${tenantId}`);
    }

    const currentCredits = tenant.current_ai_credits ?? new Decimal(0);

    if (currentCredits.lessThan(costToDeduct)) {
      this.logger.warn(
        `Tenant ${tenantId} has insufficient AI credits. Current: ${currentCredits}, Required: ${costToDeduct}`,
      );

      // 記錄信用點數不足日誌
      await this.logSystemEvent({
        action: 'ai_credit_insufficient',
        tenantId,
        userId,
        message: `AI 信用點數不足`,
        level: 'warning',
        status: 'failed',
        targetResource: 'ai_credits',
        targetResourceId: tenantId,
        errorMessage: `信用點數不足。目前剩餘: ${currentCredits}, 本次需要: ${costToDeduct}`,
        details: {
          currentCredits: currentCredits.toString(),
          requiredCredits: costToDeduct.toString(),
          shortfall: costToDeduct.sub(currentCredits).toString(),
          operation: context?.operation || 'unknown',
          featureKey: context?.featureKey,
          botId: context?.botId,
        },
        ip: context?.ip,
      });

      throw new InsufficientCreditsException(
        `您的 AI 信用點數不足。目前剩餘: ${currentCredits}, 本次需要: ${costToDeduct}`,
      );
    }

    // Deduct credits
    const updatedTenant = await this.prisma.tenants.update({
      where: { id: tenantId },
      data: {
        current_ai_credits: currentCredits.sub(costToDeduct),
      },
    });

    // 記錄成功扣除日誌
    await this.logSystemEvent({
      action: 'ai_credit_deducted',
      tenantId,
      userId,
      message: `AI 信用點數扣除成功`,
      level: 'info',
      status: 'success',
      targetResource: 'ai_credits',
      targetResourceId: tenantId,
      details: {
        previousCredits: currentCredits.toString(),
        deductedAmount: costToDeduct.toString(),
        newBalance: updatedTenant.current_ai_credits?.toString() || '0',
        operation: context?.operation || 'unknown',
        featureKey: context?.featureKey,
        botId: context?.botId,
        tenantName: tenant.name,
      },
      ip: context?.ip,
    });

    this.logger.log(
      `Deducted ${costToDeduct} AI credits from tenant ${tenantId}. New balance: ${updatedTenant.current_ai_credits}`,
    );

    return true;
  }

  /**
   * Resets or adds monthly AI credits for a tenant based on their plan.
   * Typically called by a scheduled job.
   * @param tenantId The ID of the tenant.
   * @param userId Optional user ID for manual resets.
   * @param ip Optional IP address for audit logging.
   * @returns True if credits were reset/added successfully.
   */
  async resetMonthlyCredits(tenantId: string, userId?: string, ip?: string): Promise<boolean> {
    const tenant = await this.prisma.tenants.findUnique({
      where: { id: tenantId },
      include: { plans: true },
    });

    if (!tenant) {
      this.logger.error(`Tenant ${tenantId} not found for monthly credit reset.`);

      // 記錄錯誤日誌
      await this.logSystemEvent({
        action: 'ai_credit_reset',
        tenantId,
        userId,
        message: `月度 AI 信用點數重設失敗 - 租戶不存在`,
        level: 'error',
        status: 'failed',
        targetResource: 'tenant',
        targetResourceId: tenantId,
        errorMessage: `找不到租戶 ${tenantId}`,
        ip,
      });

      throw new NotFoundException(`找不到租戶 ${tenantId}`);
    }

    if (!tenant.plans) {
      this.logger.warn(
        `Tenant ${tenantId} does not have an associated plan. Cannot reset monthly credits.`,
      );

      // 記錄警告日誌
      await this.logSystemEvent({
        action: 'ai_credit_reset',
        tenantId,
        userId,
        message: `月度 AI 信用點數重設失敗 - 未綁定方案`,
        level: 'warning',
        status: 'failed',
        targetResource: 'tenant_plan',
        targetResourceId: tenantId,
        errorMessage: `租戶 ${tenantId} 未綁定方案，無法重設信用點數`,
        details: {
          tenantName: tenant.name,
          tenantId,
        },
        ip,
      });

      throw new BadRequestException(`租戶 ${tenantId} 未綁定方案，無法重設信用點數`);
    }

    const planMonthlyCredits = tenant.plans.monthly_ai_credits_limit ?? new Decimal(0);
    const previousCredits = tenant.current_ai_credits ?? new Decimal(0);

    await this.prisma.tenants.update({
      where: { id: tenantId },
      data: { current_ai_credits: planMonthlyCredits },
    });

    // 記錄成功重設日誌
    await this.logSystemEvent({
      action: 'ai_credit_monthly_reset',
      tenantId,
      userId,
      message: `月度 AI 信用點數重設成功`,
      level: 'info',
      status: 'success',
      targetResource: 'ai_credits',
      targetResourceId: tenantId,
      details: {
        tenantName: tenant.name,
        planName: tenant.plans.name,
        planId: tenant.plans.id,
        previousCredits: previousCredits.toString(),
        newCredits: planMonthlyCredits.toString(),
        planMonthlyLimit: tenant.plans.monthly_ai_credits_limit?.toString() || '0',
        resetType: userId ? 'manual' : 'automatic',
        resetDate: new Date().toISOString(),
      },
      ip,
    });

    this.logger.log(
      `重設租戶 ${tenantId} 的 AI 信用點數為 ${planMonthlyCredits}（方案：${tenant.plans.name}）`,
    );

    return true;
  }

  /**
   * Adds purchased AI credits to a tenant's balance.
   * @param tenantId The ID of the tenant.
   * @param creditsToAdd The amount of credits to add.
   * @param purchaseDetails Optional details about the purchase for logging or record keeping.
   * @param userId Optional user ID for audit logging.
   * @param ip Optional IP address for audit logging.
   */
  async addPurchasedCredits(
    tenantId: string,
    creditsToAdd: Decimal,
    purchaseDetails?: {
      amount?: Decimal;
      pricePaid: Decimal;
      currency: string;
      paymentId?: string;
      notes?: string;
      purchasedAt?: Date;
    },
    userId?: string,
    ip?: string,
  ): Promise<any> {
    this.logger.debug(`Attempting to add ${creditsToAdd} purchased credits to tenant ${tenantId}`);

    if (creditsToAdd.isNegative() || creditsToAdd.isZero()) {
      this.logger.warn(`嘗試為租戶 ${tenantId} 增加 0 或負數點數 (${creditsToAdd})。`);

      // 記錄無效購買嘗試日誌
      await this.logSystemEvent({
        action: 'ai_credit_purchase',
        tenantId,
        userId,
        message: `AI 信用點數購買失敗 - 無效金額`,
        level: 'warning',
        status: 'failed',
        targetResource: 'ai_credits',
        targetResourceId: tenantId,
        errorMessage: '購買點數必須大於 0',
        details: {
          invalidAmount: creditsToAdd.toString(),
          purchaseDetails,
        },
        ip,
      });

      throw new BadRequestException('購買點數必須大於 0');
    }

    // 交易式操作，確保點數與購買記錄同時寫入
    return await this.prisma.$transaction(async (prisma) => {
      const tenant = await prisma.tenants.findUnique({
        where: { id: tenantId },
      });

      if (!tenant) {
        this.logger.error(`Tenant ${tenantId} not found when adding purchased credits.`);

        // 記錄錯誤日誌
        await this.logSystemEvent({
          action: 'ai_credit_purchase',
          tenantId,
          userId,
          message: `AI 信用點數購買失敗 - 租戶不存在`,
          level: 'error',
          status: 'failed',
          targetResource: 'tenant',
          targetResourceId: tenantId,
          errorMessage: `找不到租戶 ${tenantId}`,
          details: {
            creditsToAdd: creditsToAdd.toString(),
            purchaseDetails,
          },
          ip,
        });

        throw new NotFoundException(`找不到租戶 ${tenantId}`);
      }

      // 若有購買記錄，檢查必要欄位
      let creditPurchase: { id: string } | undefined = undefined;
      if (purchaseDetails) {
        if (!purchaseDetails.pricePaid || !purchaseDetails.currency) {
          // 記錄無效購買詳情日誌
          await this.logSystemEvent({
            action: 'ai_credit_purchase',
            tenantId,
            userId,
            message: `AI 信用點數購買失敗 - 缺少必要欄位`,
            level: 'error',
            status: 'failed',
            targetResource: 'ai_credits',
            targetResourceId: tenantId,
            errorMessage: '缺少購買記錄必要欄位（pricePaid, currency）',
            details: {
              creditsToAdd: creditsToAdd.toString(),
              missingFields: {
                pricePaid: !purchaseDetails.pricePaid,
                currency: !purchaseDetails.currency,
              },
            },
            ip,
          });

          throw new BadRequestException('缺少購買記錄必要欄位（pricePaid, currency）');
        }

        creditPurchase = await prisma.tenant_credit_purchases.create({
          data: {
            id: crypto.randomUUID(),
            tenant_id: tenantId,
            amount: creditsToAdd,
            price_paid: purchaseDetails.pricePaid,
            currency: purchaseDetails.currency,
            payment_id: purchaseDetails.paymentId ?? null,
            notes: purchaseDetails.notes ?? null,
            purchased_at: purchaseDetails.purchasedAt ?? new Date(),
          },
          select: { id: true },
        });
      }

      const previousCredits = tenant.current_ai_credits ?? new Decimal(0);
      const updatedTenant = await prisma.tenants.update({
        where: { id: tenantId },
        data: {
          current_ai_credits: {
            increment: creditsToAdd,
          },
        },
      });

      // 記錄成功購買日誌
      await this.logSystemEvent({
        action: 'ai_credit_purchased',
        tenantId,
        userId,
        message: `AI 信用點數購買成功`,
        level: 'info',
        status: 'success',
        targetResource: 'ai_credits',
        targetResourceId: tenantId,
        details: {
          tenantName: tenant.name,
          previousCredits: previousCredits.toString(),
          creditsAdded: creditsToAdd.toString(),
          newBalance: updatedTenant.current_ai_credits?.toString() || '0',
          purchaseId: creditPurchase?.id,
          pricePaid: purchaseDetails?.pricePaid?.toString(),
          currency: purchaseDetails?.currency,
          paymentId: purchaseDetails?.paymentId,
          notes: purchaseDetails?.notes,
          purchasedAt: purchaseDetails?.purchasedAt?.toISOString() || new Date().toISOString(),
        },
        ip,
      });

      this.logger.log(
        `租戶 ${tenantId} 購買點數：+${creditsToAdd}，新餘額：${updatedTenant.current_ai_credits}` +
          (creditPurchase ? `，已建立購買記錄（ID: ${creditPurchase.id}）` : ''),
      );

      return updatedTenant;
    });
  }

  /**
   * Retrieves the current AI credit balance for a tenant.
   * @param tenantId The ID of the tenant.
   * @param userId Optional user ID for audit logging.
   * @param ip Optional IP address for audit logging.
   * @returns The current AI credit balance.
   */
  async getCurrentCredits(tenantId: string, userId?: string, ip?: string): Promise<Decimal> {
    const tenant = await this.prisma.tenants.findUnique({
      where: { id: tenantId },
      select: { current_ai_credits: true, name: true },
    });

    if (!tenant) {
      this.logger.error(`Tenant with ID ${tenantId} not found when fetching credits.`);

      // 記錄錯誤日誌
      await this.logSystemEvent({
        action: 'ai_credit_query',
        tenantId,
        userId,
        message: `AI 信用點數查詢失敗 - 租戶不存在`,
        level: 'error',
        status: 'failed',
        targetResource: 'tenant',
        targetResourceId: tenantId,
        errorMessage: `找不到租戶 ${tenantId}`,
        ip,
      });

      throw new NotFoundException(`找不到租戶 ${tenantId}`);
    }

    const credits = tenant.current_ai_credits ?? new Decimal(0);

    // 記錄查詢日誌（調試級別）
    await this.logSystemEvent({
      action: 'ai_credit_query',
      tenantId,
      userId,
      message: `AI 信用點數查詢`,
      level: 'info',
      status: 'success',
      targetResource: 'ai_credits',
      targetResourceId: tenantId,
      details: {
        currentCredits: credits.toString(),
        tenantName: tenant.name,
      },
      ip,
    });

    this.logger.debug(`查詢租戶 ${tenantId} 的 AI 信用點數：${credits}`);
    return credits;
  }

  /**
   * 記錄系統事件到系統日誌
   */
  private async logSystemEvent(eventData: {
    action: string;
    tenantId: string;
    userId?: string;
    message: string;
    level: 'info' | 'warning' | 'error' | 'debug';
    status: 'success' | 'failed' | 'pending';
    targetResource: string;
    targetResourceId: string;
    errorMessage?: string;
    details?: Record<string, any>;
    ip?: string;
  }): Promise<void> {
    try {
      await this.prisma.system_logs.create({
        data: {
          id: crypto.randomUUID(),
          level: eventData.level,
          message: eventData.message,
          action: eventData.action,
          status: eventData.status,
          target_resource: eventData.targetResource,
          target_resource_id: eventData.targetResourceId,
          tenant_id: eventData.tenantId,
          user_id: eventData.userId || null,
          error_message: eventData.errorMessage || null,
          details: eventData.details ? JSON.parse(JSON.stringify(eventData.details)) : null,
          ip: eventData.ip || null,
          created_at: new Date(),
        },
      });
    } catch (error) {
      // 日誌記錄失敗不應該影響主要業務邏輯
      this.logger.error(`Failed to log system event: ${error.message}`, error.stack);
    }
  }
}
