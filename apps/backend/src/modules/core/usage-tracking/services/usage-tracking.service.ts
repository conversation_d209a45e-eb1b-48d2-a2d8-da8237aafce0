import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { AiModelsService } from '../../../ai/models/configuration/models/ai-models.service';
import { Decimal } from '@prisma/client/runtime/library';

interface UsageLogData {
  tenantId: string;
  userId?: string;
  agentId: string;
  featureKey?: string;
  apiKeyId: string;
  provider: string;
  modelName: string;
  inputTokens: number;
  outputTokens: number;
  callCount?: number;
  isSuccess: boolean;
  errorMessage?: string;
  requestTimestamp: Date;
  responseTimestamp?: Date;
}

interface CostCalculationResult {
  inputCost: number;
  outputCost: number;
  totalCost: number;
}

@Injectable()
export class UsageTrackingService {
  private readonly logger = new Logger(UsageTrackingService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly aiModelsService: AiModelsService,
  ) {}

  /**
   * 記錄 AI 使用量
   */
  async logUsage(data: UsageLogData): Promise<void> {
    try {
      // 計算成本
      const cost = await this.calculateModelCost(
        data.provider,
        data.modelName,
        data.inputTokens,
        data.outputTokens,
      );

      // 建立使用量日誌記錄
      await this.prisma.ai_usage_logs.create({
        data: {
          id: this.generateLogId(),
          tenant_id: data.tenantId,
          user_id: data.userId,
          agent_id: data.agentId,
          feature_key: data.featureKey,
          api_key_id: data.apiKeyId,
          provider: data.provider,
          model_name: data.modelName,
          input_tokens: data.inputTokens,
          output_tokens: data.outputTokens,
          call_count: data.callCount || 1,
          estimated_cost: new Decimal(cost.totalCost),
          request_timestamp: data.requestTimestamp,
          response_timestamp: data.responseTimestamp,
          is_success: data.isSuccess,
          error_message: data.errorMessage,
          created_at: new Date(),
        },
      });

      this.logger.log(
        `記錄 AI 使用量: ${data.provider}/${data.modelName}, ` +
          `輸入: ${data.inputTokens} tokens, 輸出: ${data.outputTokens} tokens, ` +
          `成本: $${cost.totalCost.toFixed(4)}, 租戶: ${data.tenantId}`,
      );
    } catch (error) {
      this.logger.error('記錄 AI 使用量失敗', error);
      // 不拋出錯誤，避免影響主要功能
    }
  }

  /**
   * 計算 AI 使用成本
   */
  private async calculateModelCost(
    provider: string,
    modelName: string,
    inputTokens: number,
    outputTokens: number,
  ): Promise<CostCalculationResult> {
    try {
      // 從資料庫獲取模型定價
      const model = await this.prisma.ai_models.findFirst({
        where: {
          provider,
          model_name: modelName,
          is_enabled: true,
        },
      });

      if (!model) {
        this.logger.warn(`未找到模型定價: ${provider}/${modelName}`);
        return { inputCost: 0, outputCost: 0, totalCost: 0 };
      }

      // 計算成本（價格是每 1000 tokens）
      const inputCost = (inputTokens / 1000) * Number(model.input_price_per_1k_tokens);
      const outputCost = (outputTokens / 1000) * Number(model.output_price_per_1k_tokens);
      const totalCost = inputCost + outputCost;

      return {
        inputCost,
        outputCost,
        totalCost,
      };
    } catch (error) {
      this.logger.error('計算成本失敗', error);
      return { inputCost: 0, outputCost: 0, totalCost: 0 };
    }
  }

  /**
   * 檢查配額限制
   */
  async checkQuotaLimits(tenantId: string): Promise<{
    withinLimits: boolean;
    monthlyUsage: {
      totalCalls: number;
      totalTokens: number;
      totalCost: number;
    };
    limits: {
      monthlyCalls?: number;
      monthlyTokens?: number;
      monthlyCost?: number;
    };
  }> {
    try {
      // 獲取當月使用量
      const currentMonth = new Date();
      currentMonth.setDate(1);
      currentMonth.setHours(0, 0, 0, 0);

      const nextMonth = new Date(currentMonth);
      nextMonth.setMonth(nextMonth.getMonth() + 1);

      const monthlyUsage = await this.prisma.ai_usage_logs.aggregate({
        where: {
          tenant_id: tenantId,
          request_timestamp: {
            gte: currentMonth,
            lt: nextMonth,
          },
        },
        _sum: {
          call_count: true,
          input_tokens: true,
          output_tokens: true,
          estimated_cost: true,
        },
      });

      const totalCalls = monthlyUsage._sum.call_count || 0;
      const totalTokens =
        (monthlyUsage._sum.input_tokens || 0) + (monthlyUsage._sum.output_tokens || 0);
      const totalCost = Number(monthlyUsage._sum.estimated_cost) || 0;

      // 獲取全域配額限制
      const globalSettings = await this.prisma.ai_global_settings.findFirst();

      // 檢查是否超過限制
      let withinLimits = true;
      const limits: any = {};

      if (globalSettings?.global_monthly_quota_calls) {
        limits.monthlyCalls = Number(globalSettings.global_monthly_quota_calls);
        if (totalCalls >= limits.monthlyCalls) {
          withinLimits = false;
        }
      }

      if (globalSettings?.global_monthly_quota_tokens) {
        limits.monthlyTokens = Number(globalSettings.global_monthly_quota_tokens);
        if (totalTokens >= limits.monthlyTokens) {
          withinLimits = false;
        }
      }

      return {
        withinLimits,
        monthlyUsage: {
          totalCalls,
          totalTokens,
          totalCost,
        },
        limits,
      };
    } catch (error) {
      this.logger.error('檢查配額限制失敗', error);
      // 發生錯誤時允許繼續執行
      return {
        withinLimits: true,
        monthlyUsage: { totalCalls: 0, totalTokens: 0, totalCost: 0 },
        limits: {},
      };
    }
  }

  /**
   * 獲取租戶使用量統計
   */
  async getTenantUsageStats(
    tenantId: string,
    timeRange?: { start: Date; end: Date },
  ): Promise<{
    totalCalls: number;
    totalTokens: number;
    totalCost: number;
    successRate: number;
    topModels: Array<{ modelName: string; usage: number; cost: number }>;
    topFeatures: Array<{ featureKey: string; usage: number; cost: number }>;
  }> {
    try {
      const whereCondition: any = {
        tenant_id: tenantId,
      };

      if (timeRange) {
        whereCondition.request_timestamp = {
          gte: timeRange.start,
          lte: timeRange.end,
        };
      }

      // 總體統計
      const totalStats = await this.prisma.ai_usage_logs.aggregate({
        where: whereCondition,
        _sum: {
          call_count: true,
          input_tokens: true,
          output_tokens: true,
          estimated_cost: true,
        },
        _count: {
          _all: true,
          is_success: true,
        },
      });

      // 按模型分組
      const modelStats = await this.prisma.ai_usage_logs.groupBy({
        by: ['model_name'],
        where: whereCondition,
        _sum: {
          call_count: true,
          estimated_cost: true,
        },
        orderBy: {
          _sum: {
            call_count: 'desc',
          },
        },
        take: 5,
      });

      // 按功能分組
      const featureStats = await this.prisma.ai_usage_logs.groupBy({
        by: ['feature_key'],
        where: whereCondition,
        _sum: {
          call_count: true,
          estimated_cost: true,
        },
        orderBy: {
          _sum: {
            call_count: 'desc',
          },
        },
        take: 5,
      });

      const totalCalls = totalStats._sum.call_count || 0;
      const totalTokens =
        (totalStats._sum.input_tokens || 0) + (totalStats._sum.output_tokens || 0);
      const totalCost = Number(totalStats._sum.estimated_cost) || 0;
      const successRate =
        totalStats._count._all > 0
          ? (totalStats._count.is_success / totalStats._count._all) * 100
          : 0;

      return {
        totalCalls,
        totalTokens,
        totalCost,
        successRate,
        topModels: modelStats.map((stat) => ({
          modelName: stat.model_name,
          usage: stat._sum.call_count || 0,
          cost: Number(stat._sum.estimated_cost) || 0,
        })),
        topFeatures: featureStats.map((stat) => ({
          featureKey: stat.feature_key || 'unknown',
          usage: stat._sum.call_count || 0,
          cost: Number(stat._sum.estimated_cost) || 0,
        })),
      };
    } catch (error) {
      this.logger.error('獲取租戶使用量統計失敗', error);
      throw error;
    }
  }

  /**
   * 生成唯一的日誌 ID
   */
  private generateLogId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * 獲取使用統計資訊
   */
  async getUsageStats(
    tenantId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<{
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    totalTokensUsed: number;
    successRate: number;
    errorBreakdown: any[];
  }> {
    try {
      const whereCondition = {
        tenant_id: tenantId,
        ...(startDate &&
          endDate && {
            created_at: {
              gte: startDate,
              lt: endDate,
            },
          }),
      };

      // 獲取總體統計
      const totalStats = await this.prisma.ai_usage_logs.aggregate({
        where: whereCondition,
        _sum: {
          call_count: true,
          input_tokens: true,
          output_tokens: true,
          estimated_cost: true,
        },
      });

      // 獲取成功/失敗統計
      const errorBreakdown = await this.prisma.ai_usage_logs.groupBy({
        by: ['is_success'],
        where: whereCondition,
        _count: {
          _all: true,
        },
      });

      // 計算平均執行時間 - 獲取有 response_timestamp 的記錄
      const executionTimeRecords = await this.prisma.ai_usage_logs.findMany({
        where: {
          ...whereCondition,
          response_timestamp: { not: null },
        },
        select: {
          request_timestamp: true,
          response_timestamp: true,
        },
      });

      // 計算平均執行時間（毫秒）
      let averageExecutionTime = 0;
      if (executionTimeRecords.length > 0) {
        const totalExecutionTime = executionTimeRecords.reduce((sum, record) => {
          if (record.request_timestamp && record.response_timestamp) {
            const executionTime =
              record.response_timestamp.getTime() - record.request_timestamp.getTime();
            return sum + executionTime;
          }
          return sum;
        }, 0);

        averageExecutionTime = totalExecutionTime / executionTimeRecords.length;
      }

      const totalExecutions = Number(totalStats._sum.call_count) || 0;
      const totalTokensUsed =
        (Number(totalStats._sum.input_tokens) || 0) + (Number(totalStats._sum.output_tokens) || 0);

      const successfulExecutions =
        errorBreakdown.find((e) => e.is_success === true)?._count._all || 0;
      const failedExecutions = errorBreakdown.find((e) => e.is_success === false)?._count._all || 0;

      const successRate = totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0;

      return {
        totalExecutions,
        successfulExecutions,
        failedExecutions,
        averageExecutionTime: Math.round(averageExecutionTime), // 四捨五入到最近的毫秒
        totalTokensUsed,
        successRate,
        errorBreakdown,
      };
    } catch (error) {
      this.logger.error(`Failed to get usage stats for tenant ${tenantId}:`, error);
      throw error;
    }
  }

  /**
   * 計算成本（公開方法供測試使用）
   */
  async calculateCost(
    provider: string,
    modelName: string,
    inputTokens: number,
    outputTokens: number,
  ): Promise<number> {
    const result = await this.calculateModelCost(provider, modelName, inputTokens, outputTokens);
    return result.totalCost;
  }
}
