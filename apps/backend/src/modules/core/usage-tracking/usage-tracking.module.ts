import { Module } from '@nestjs/common';
import { PrismaModule } from '../prisma/prisma.module';
import { UsageTrackingService } from './services/usage-tracking.service';
import { UsageStatisticsService } from './services/usage-statistics.service';
import { TenantQuotaService } from './services/tenant-quota.service';
import { ModelsModule } from '../../ai/models/models.module';

@Module({
  imports: [PrismaModule, ModelsModule],
  providers: [
    UsageTrackingService,
    UsageStatisticsService,
    TenantQuotaService,
  ],
  exports: [
    UsageTrackingService,
    UsageStatisticsService,
    TenantQuotaService,
  ],
})
export class UsageTrackingModule {} 