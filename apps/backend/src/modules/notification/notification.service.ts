import { Injectable, NotImplementedException, BadRequestException } from '@nestjs/common';
import { MessageCenterService } from '../workspace/message-center/services/message-center.service';
import { PrismaService } from '../core/prisma/prisma.service';

interface SendMessageDto {
  channel: 'internal' | 'line' | 'slack';
  recipient_type: 'user' | 'group';
  recipient_id: string;
  message: string;
  message_type?: string;
}

@Injectable()
export class NotificationService {
  constructor(
    private readonly messageCenterService: MessageCenterService,
    private readonly prisma: PrismaService,
  ) {}

  async sendMessage(dto: SendMessageDto, tenantId: string, senderId: string): Promise<void> {
    switch (dto.channel) {
      case 'internal':
        await this.handleInternalMessage(dto, tenantId, senderId);
        break;
      case 'line':
        await this.handleLineMessage(dto, tenantId);
        break;
      case 'slack':
        throw new NotImplementedException('Slack notifications are not yet implemented.');
      default:
        throw new Error(`Unsupported notification channel: ${dto.channel}`);
    }
  }

  private async handleInternalMessage(
    dto: SendMessageDto,
    tenantId: string,
    senderId: string,
  ): Promise<void> {
    let conversationId: string;

    if (dto.recipient_type === 'user') {
      const recipientId = dto.recipient_id;
      const conversation = await this.findOrCreateDirectConversation(
        senderId,
        recipientId,
        tenantId,
      );
      conversationId = conversation.id;
    } else if (dto.recipient_type === 'group') {
      const groupId = dto.recipient_id;
      const conversation = await this.findOrCreateGroupConversation(groupId, tenantId);
      conversationId = conversation.id;
    } else {
      throw new BadRequestException('Invalid recipient_type for internal message.');
    }

    await this.messageCenterService.sendMessage(
      {
        conversationId: conversationId,
        content: dto.message,
        contentType: (dto.message_type as any) || 'TEXT',
      },
      tenantId,
      senderId,
      'AGENT',
      'Agent',
    );
  }

  private async findOrCreateDirectConversation(user1Id: string, user2Id: string, tenantId: string) {
    const participantIds = [user1Id, user2Id].sort();

    const existingConversation = await this.prisma.message_conversations.findFirst({
      where: {
        tenant_id: tenantId,
        type: 'DIRECT',
        AND: [
          { participant_ids: { array_contains: participantIds[0] } },
          { participant_ids: { array_contains: participantIds[1] } },
        ],
        // We also need to ensure the array has exactly two participants
        // This is a simplified query; a more robust solution might involve array length checks
        // which can be complex with Prisma on JSON fields. This is good enough for now.
      },
    });

    if (existingConversation) {
      return existingConversation;
    }

    return this.prisma.message_conversations.create({
      data: {
        tenant_id: tenantId,
        type: 'DIRECT',
        participant_ids: participantIds,
        created_by: user1Id,
        title: `Conversation between ${user1Id} and ${user2Id}`,
      },
    });
  }

  private async findOrCreateGroupConversation(groupId: string, tenantId: string) {
    const conversationTitle = `group_chat_${groupId}`;

    const existingConversation = await this.prisma.message_conversations.findFirst({
      where: {
        tenant_id: tenantId,
        type: 'GROUP',
        title: conversationTitle,
      },
    });

    if (existingConversation) {
      return existingConversation;
    }

    return this.prisma.message_conversations.create({
      data: {
        tenant_id: tenantId,
        type: 'GROUP',
        title: conversationTitle,
        created_by: 'system',
        participant_ids: [],
      },
    });
  }

  private async handleLineMessage(dto: SendMessageDto, tenantId: string): Promise<void> {
    // 實現 LINE 訊息邏輯
    // 1. 從 tenantId 獲取 LINE 的配置 (例如 channel access token)
    // 2. 根據 recipient_type 決定是 push message (to user) 還是 reply/multicast (to group)
    // 3. 呼叫 LINE Messaging API

    // 臨時實現：拋出未實現錯誤
    throw new NotImplementedException('LINE notifications are not yet implemented.');
  }
}
