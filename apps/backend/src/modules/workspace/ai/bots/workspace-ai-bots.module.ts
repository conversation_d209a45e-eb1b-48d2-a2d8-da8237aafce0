import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '@/modules/core/prisma/prisma.module';
import { AuthModule } from '@/modules/core/auth/auth.module';
import { AgentsModule } from '@/modules/core/agents/agents.module';
import { WorkspaceAiBotsController } from './workspace-ai-bots.controller';
import { WorkspaceAiBotsService } from './workspace-ai-bots.service';
import { AiProviderFactory } from '@/modules/ai/models/core/providers/factory';

@Module({
  imports: [PrismaModule, AuthModule, AgentsModule],
  controllers: [WorkspaceAiBotsController],
  providers: [WorkspaceAiBotsService, AiProviderFactory],
  exports: [WorkspaceAiBotsService],
})
export class WorkspaceAiBotsModule {}
