import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { AiProviderFactory } from '@/modules/ai/models/core/providers/factory';
import { WorkspaceChatDto } from './dto/workspace-ai-bots-chat.dto';
import { CreateWorkspaceAiBotDto, UpdateWorkspaceAiBotDto } from './dto/workspace-ai-bot.dto';
import { AiAgentProviderType, AiAgentScope, AiAgentResponseFormat } from '@prisma/client';
import { AiMessage, LlmExecuteOptions } from '@/modules/ai/llm/interfaces/llm-service.interface';
import { randomUUID } from 'crypto';

@Injectable()
export class WorkspaceAiBotsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly aiProviderFactory: AiProviderFactory,
  ) {}

  private async validateTenantAccess(tenantId: string, userId: string): Promise<void> {
    const user = await this.prisma.tenant_users.findUnique({
      where: { id: userId },
      select: { tenant_id: true },
    });

    if (!user?.tenant_id) {
      throw new ForbiddenException('使用者未綁定租戶');
    }

    if (user.tenant_id !== tenantId) {
      throw new ForbiddenException('無權存取此租戶');
    }
  }

  async getBots(tenantId: string, userId: string, options?: { page?: number; limit?: number }) {
    await this.validateTenantAccess(tenantId, userId);

    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;

    const [total, bots] = await Promise.all([
      this.prisma.ai_agents.count({
        where: { tenant_id: tenantId },
      }),
      this.prisma.ai_agents.findMany({
        where: { tenant_id: tenantId },
        skip,
        take: limit,
        orderBy: { created_at: 'desc' },
      }),
    ]);

    return {
      items: bots,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getBot(tenantId: string, botId: string, userId: string) {
    await this.validateTenantAccess(tenantId, userId);

    const bot = await this.prisma.ai_agents.findFirst({
      where: {
        id: botId,
        tenant_id: tenantId,
      },
      include: {
        ai_models: true,
        ai_keys: true,
      },
    });

    if (!bot) {
      throw new NotFoundException('找不到指定的 Bot');
    }

    return bot;
  }

  async createBot(tenantId: string, dto: CreateWorkspaceAiBotDto, userId: string) {
    await this.validateTenantAccess(tenantId, userId);

    return this.prisma.ai_agents.create({
      data: {
        id: randomUUID(),
        ...dto,
        tenant_id: tenantId,
        created_by: userId,
        updated_at: new Date(),
      },
    });
  }

  async updateBot(tenantId: string, botId: string, dto: UpdateWorkspaceAiBotDto, userId: string) {
    await this.validateTenantAccess(tenantId, userId);

    const bot = await this.prisma.ai_agents.findFirst({
      where: {
        id: botId,
        tenant_id: tenantId,
      },
      include: {
        ai_models: true,
        ai_keys: true,
      },
    });

    if (!bot) {
      throw new NotFoundException('找不到指定的 Bot');
    }

    return this.prisma.ai_agents.update({
      where: { id: botId },
      data: {
        ...dto,
        updated_by: userId,
        updated_at: new Date(),
      },
    });
  }

  async deleteBot(tenantId: string, botId: string, userId: string) {
    await this.validateTenantAccess(tenantId, userId);

    const bot = await this.prisma.ai_agents.findFirst({
      where: {
        id: botId,
        tenant_id: tenantId,
      },
      include: {
        ai_models: true,
        ai_keys: true,
      },
    });

    if (!bot) {
      throw new NotFoundException('找不到指定的 Bot');
    }

    await this.prisma.ai_agents.delete({
      where: { id: botId },
    });

    return { success: true };
  }

  async chatWithBot(tenantId: string, botId: string, dto: WorkspaceChatDto, userId: string) {
    await this.validateTenantAccess(tenantId, userId);

    const bot = await this.prisma.ai_agents.findFirst({
      where: {
        id: botId,
        tenant_id: tenantId,
      },
      include: {
        ai_models: true,
        ai_keys: true,
      },
    });

    if (!bot) {
      throw new NotFoundException('指定的 Bot 不存在');
    }

    // 建立 provider 實例
    const provider = this.aiProviderFactory.createProvider(
      bot.provider_type as AiAgentProviderType,
      bot.ai_keys.api_key,
      bot.ai_keys.api_url ?? undefined,
    );

    // 準備訊息 (加入 system prompt)
    const messages: AiMessage[] = [];
    if (bot.system_prompt) {
      messages.push({
        role: 'system',
        content: bot.system_prompt,
      } as AiMessage);
    }
    messages.push(
      ...dto.messages
        .filter((m) => m.role === 'user')
        .map((m) => ({ role: 'user', content: m.content }) as AiMessage),
    );

    if (messages.filter((m) => m.role === 'user').length === 0) {
      throw new BadRequestException('至少需要一條使用者訊息');
    }

    // 準備選項
    const options = {
      model: bot.ai_models.model_name,
      temperature: dto.temperature ?? bot.temperature ?? 0.7,
      responseFormat: bot.response_format,
    };

    // 呼叫 provider
    try {
      const response = await provider.execute(messages, options);
      return response;
    } catch (error) {
      console.error(`Error chatting with bot ${botId}:`, error);
      throw new BadRequestException(`與 AI Bot 通訊時發生錯誤: ${error.message}`);
    }
  }
}
