import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '@/modules/core/prisma/prisma.module';
import { AiExecutionService } from './ai-execution.service';
import { AiExecutionsController } from './ai-executions.controller';
import { AgentsModule } from '@/modules/core/agents/agents.module';

@Module({
  imports: [
    PrismaModule,
    AgentsModule, // Import for agent execution functionality
  ],
  controllers: [AiExecutionsController],
  providers: [AiExecutionService],
  exports: [AiExecutionService],
})
export class AiExecutionsModule {}
