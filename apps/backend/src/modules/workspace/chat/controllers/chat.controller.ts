import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { ChatService } from '../services/chat.service';
import { CreateConversationDto } from '../dto/create-conversation.dto';
import { UpdateConversationDto } from '../dto/update-conversation.dto';
import { SendMessageDto } from '../dto/send-message.dto';
import { JwtAuthGuard } from '../../../core/auth/guards/auth.guard';
import { CurrentUser } from '../../../core/auth/decorators/current-user.decorator';

@ApiTags('Chat')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('workspace/chat')
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  // 對話相關端點
  @Post('conversations')
  @ApiOperation({ summary: '建立新對話' })
  async createConversation(
    @Body() createDto: CreateConversationDto,
    @CurrentUser() user: any,
    @Query('workspaceId') workspaceId: string,
  ) {
    return this.chatService.createConversation(workspaceId, user.id, createDto);
  }

  @Get('conversations')
  @ApiOperation({ summary: '獲取用戶對話列表' })
  @ApiQuery({ name: 'workspaceId', required: true })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getUserConversations(
    @Query('workspaceId') workspaceId: string,
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 20,
    @CurrentUser() user: any,
  ) {
    return this.chatService.getUserConversations(workspaceId, user.id, page, limit);
  }

  @Get('conversations/:id')
  @ApiOperation({ summary: '獲取單個對話詳情' })
  async getConversation(@Param('id') conversationId: string, @CurrentUser() user: any) {
    return this.chatService.getConversation(conversationId, user.id);
  }

  @Patch('conversations/:id')
  @ApiOperation({ summary: '更新對話' })
  async updateConversation(
    @Param('id') conversationId: string,
    @Body() updateDto: UpdateConversationDto,
    @CurrentUser() user: any,
  ) {
    return this.chatService.updateConversation(conversationId, user.id, updateDto);
  }

  @Delete('conversations/:id')
  @ApiOperation({ summary: '刪除對話' })
  async deleteConversation(@Param('id') conversationId: string, @CurrentUser() user: any) {
    await this.chatService.deleteConversation(conversationId, user.id);
    return { message: '對話已刪除' };
  }

  @Post('conversations/:id/participants')
  @ApiOperation({ summary: '添加參與者到對話' })
  async addParticipant(
    @Param('id') conversationId: string,
    @Body('userId') targetUserId: string,
    @CurrentUser() user: any,
  ) {
    return this.chatService.addParticipant(conversationId, user.id, targetUserId);
  }

  @Delete('conversations/:id/participants/:userId')
  @ApiOperation({ summary: '從對話中移除參與者' })
  async removeParticipant(
    @Param('id') conversationId: string,
    @Param('userId') targetUserId: string,
    @CurrentUser() user: any,
  ) {
    await this.chatService.removeParticipant(conversationId, user.id, targetUserId);
    return { message: '參與者已移除' };
  }

  @Post('conversations/:id/read')
  @ApiOperation({ summary: '標記對話為已讀' })
  async markConversationAsRead(
    @Param('id') conversationId: string,
    @Body('messageId') messageId?: string,
    @CurrentUser() user?: any,
  ) {
    await this.chatService.markConversationAsRead(conversationId, user.id, messageId);
    return { message: '對話已標記為已讀' };
  }

  // 訊息相關端點
  @Post('messages')
  @ApiOperation({ summary: '發送訊息' })
  async sendMessage(
    @Body() sendDto: SendMessageDto & { conversationId: string },
    @CurrentUser() user: any,
  ) {
    const { conversationId, ...messageDto } = sendDto;
    return this.chatService.sendMessage(conversationId, user.id, messageDto);
  }

  @Get('conversations/:id/messages')
  @ApiOperation({ summary: '獲取對話中的訊息' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getConversationMessages(
    @Param('id') conversationId: string,
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 50,
    @CurrentUser() user: any,
  ) {
    return this.chatService.getConversationMessages(conversationId, user.id, page, limit);
  }

  @Patch('messages/:id')
  @ApiOperation({ summary: '編輯訊息' })
  async updateMessage(
    @Param('id') messageId: string,
    @Body('content') content: string,
    @CurrentUser() user: any,
  ) {
    return this.chatService.updateMessage(messageId, user.id, content);
  }

  @Delete('messages/:id')
  @ApiOperation({ summary: '刪除訊息' })
  async deleteMessage(@Param('id') messageId: string, @CurrentUser() user: any) {
    await this.chatService.deleteMessage(messageId, user.id);
    return { message: '訊息已刪除' };
  }

  @Post('messages/:id/reactions')
  @ApiOperation({ summary: '添加訊息反應' })
  async addReaction(
    @Param('id') messageId: string,
    @Body('emoji') emoji: string,
    @CurrentUser() user: any,
  ) {
    return this.chatService.addReaction(messageId, user.id, emoji);
  }

  @Delete('messages/:id/reactions/:emoji')
  @ApiOperation({ summary: '移除訊息反應' })
  async removeReaction(
    @Param('id') messageId: string,
    @Param('emoji') emoji: string,
    @CurrentUser() user: any,
  ) {
    return this.chatService.removeReaction(messageId, user.id, emoji);
  }

  @Post('messages/read')
  @ApiOperation({ summary: '批量標記訊息為已讀' })
  async markMessagesAsRead(
    @Body() data: { conversation_id: string; messageIds: string[] },
    @CurrentUser() user: any,
  ) {
    await this.chatService.markMessagesAsRead(data.conversation_id, user.id, data.messageIds);
    return { message: '訊息已標記為已讀' };
  }

  @Get('messages/search')
  @ApiOperation({ summary: '搜索訊息' })
  @ApiQuery({ name: 'conversationId', required: true })
  @ApiQuery({ name: 'query', required: true })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async searchMessages(
    @Query('conversationId') conversationId: string,
    @Query('query') query: string,
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 20,
    @CurrentUser() user: any,
  ) {
    return this.chatService.searchMessages(conversationId, user.id, query, page, limit);
  }

  // 輸入狀態相關端點
  @Post('conversations/:id/typing/start')
  @ApiOperation({ summary: '開始輸入' })
  async startTyping(@Param('id') conversationId: string, @CurrentUser() user: any) {
    await this.chatService.startTyping(conversationId, user.id);
    return { message: '輸入狀態已開始' };
  }

  @Post('conversations/:id/typing/stop')
  @ApiOperation({ summary: '停止輸入' })
  async stopTyping(@Param('id') conversationId: string, @CurrentUser() user: any) {
    await this.chatService.stopTyping(conversationId, user.id);
    return { message: '輸入狀態已停止' };
  }

  // 統計相關端點
  @Get('overview')
  @ApiOperation({ summary: '獲取聊天概覽' })
  @ApiQuery({ name: 'workspaceId', required: true })
  async getChatOverview(@Query('workspaceId') workspaceId: string, @CurrentUser() user: any) {
    return this.chatService.getChatOverview(workspaceId, user.id);
  }

  @Get('statistics')
  @ApiOperation({ summary: '獲取工作區聊天統計' })
  @ApiQuery({ name: 'workspaceId', required: true })
  async getWorkspaceChatStatistics(@Query('workspaceId') workspaceId: string) {
    return this.chatService.getWorkspaceChatStatistics(workspaceId);
  }

  @Get('conversations/:id/statistics')
  @ApiOperation({ summary: '獲取對話統計' })
  async getMessageStatistics(@Param('id') conversationId: string) {
    return this.chatService.getMessageStatistics(conversationId);
  }
}
