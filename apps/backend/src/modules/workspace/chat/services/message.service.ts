import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../../../core/prisma/prisma.service';
import { SendMessageDto } from '../dto/send-message.dto';
import { UpdateMessageDto } from '../dto/update-message.dto';
import { MessageWithReplies, CreateMessageDto } from '../types/chat.types';

@Injectable()
export class MessageService {
  constructor(private readonly prisma: PrismaService) {}

  async sendMessage(conversationId: string, senderId: string, sendDto: SendMessageDto) {
    const { content, type, reply_to_id, attachments } = sendDto;

    // 檢查用戶是否為對話參與者
    const participant = await this.prisma.conversation_participants.findFirst({
      where: {
        conversation_id: conversationId,
        user_id: senderId,
        status: 'ACTIVE',
      },
    });

    if (!participant) {
      throw new ForbiddenException('您不是此對話的參與者');
    }

    // 建立訊息
    const message = await this.prisma.messages.create({
      data: {
        content,
        type: type || 'TEXT',
        conversation_id: conversationId,
        sender_id: senderId,
        reply_to_id,
        status: 'SENT',
        attachments: attachments
          ? {
              create: attachments.map((attachment) => ({
                name: attachment.file_name,
                url: attachment.file_url,
                type: attachment.file_type,
                size: attachment.file_size,
              })),
            }
          : undefined,
      },
      include: {
        reply_to_message: true,
        attachments: true,
        reactions: true,
      },
    });

    // 更新對話的最後活動時間
    await this.prisma.conversations.update({
      where: { id: conversationId },
      data: {
        last_activity_at: new Date(),
        last_message_id: message.id,
      },
    });

    // 獲取發送者資訊
    let senderInfo = { senderName: '未知用戶', senderAvatar: null as string | null };

    // 先嘗試查詢系統用戶
    const systemUser = await this.prisma.system_users.findUnique({
      where: { id: senderId },
      select: { name: true, avatar: true, email: true },
    });

    if (systemUser) {
      senderInfo = {
        senderName: systemUser.name || systemUser.email || '未知用戶',
        senderAvatar: systemUser.avatar,
      };
    } else {
      // 查詢租戶用戶
      const tenantUser = await this.prisma.tenant_users.findUnique({
        where: { id: senderId },
        select: { name: true, avatar: true, email: true },
      });

      if (tenantUser) {
        senderInfo = {
          senderName: tenantUser.name || tenantUser.email || '未知用戶',
          senderAvatar: tenantUser.avatar,
        };
      }
    }

    // 返回包含發送者資訊的訊息
    return {
      ...message,
      ...senderInfo,
    };
  }

  async getMessages(conversationId: string, userId: string, page: number = 1, limit: number = 50) {
    // 檢查用戶權限
    const participant = await this.prisma.conversation_participants.findFirst({
      where: {
        conversation_id: conversationId,
        user_id: userId,
        status: 'ACTIVE',
      },
    });

    if (!participant) {
      throw new ForbiddenException('您沒有權限查看此對話的訊息');
    }

    const skip = (page - 1) * limit;

    const [messages, total] = await Promise.all([
      this.prisma.messages.findMany({
        where: {
          conversation_id: conversationId,
          is_deleted: false,
        },
        include: {
          reply_to_message: true,
          attachments: true,
          reactions: true,
        },
        orderBy: {
          created_at: 'desc',
        },
        skip,
        take: limit,
      }),
      this.prisma.messages.count({
        where: {
          conversation_id: conversationId,
          is_deleted: false,
        },
      }),
    ]);

    // 獲取所有發送者的資訊
    const senderIds = [...new Set(messages.map((msg) => msg.sender_id))];
    const senderInfoMap = new Map();

    // 查詢系統用戶
    const systemUsers = await this.prisma.system_users.findMany({
      where: { id: { in: senderIds } },
      select: { id: true, name: true, avatar: true, email: true },
    });

    // 查詢租戶用戶
    const tenantUsers = await this.prisma.tenant_users.findMany({
      where: { id: { in: senderIds } },
      select: { id: true, name: true, avatar: true, email: true },
    });

    // 建立發送者資訊映射
    [...systemUsers, ...tenantUsers].forEach((user) => {
      senderInfoMap.set(user.id, {
        senderName: user.name || user.email || '未知用戶',
        senderAvatar: user.avatar as string | null,
      });
    });

    // 為每個訊息添加發送者資訊
    const messagesWithSenderInfo = messages.map((message) => ({
      ...message,
      senderName: senderInfoMap.get(message.sender_id)?.senderName || '未知用戶',
      senderAvatar: senderInfoMap.get(message.sender_id)?.senderAvatar || (null as string | null),
    }));

    return {
      messages: messagesWithSenderInfo.reverse(), // 反轉以獲得正確的時間順序
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getMessageById(messageId: string, userId: string) {
    const message = await this.prisma.messages.findUnique({
      where: { id: messageId },
      include: {
        reply_to_message: true,
        attachments: true,
        reactions: true,
        conversation: {
          include: {
            participants: {
              where: {
                user_id: userId,
                status: 'ACTIVE',
              },
            },
          },
        },
      },
    });

    if (!message) {
      throw new NotFoundException('訊息不存在');
    }

    if (message.is_deleted) {
      throw new NotFoundException('訊息已被刪除');
    }

    // 檢查用戶是否有權限查看此訊息
    if (message.conversation.participants.length === 0) {
      throw new ForbiddenException('您沒有權限查看此訊息');
    }

    return message;
  }

  async updateMessage(messageId: string, userId: string, content: string) {
    const message = await this.prisma.messages.findUnique({
      where: { id: messageId },
    });

    if (!message) {
      throw new NotFoundException('訊息不存在');
    }

    if (message.sender_id !== userId) {
      throw new ForbiddenException('您只能編輯自己的訊息');
    }

    if (message.is_deleted) {
      throw new BadRequestException('無法編輯已刪除的訊息');
    }

    const updatedMessage = await this.prisma.messages.update({
      where: { id: messageId },
      data: {
        content,
        is_edited: true,
        edited_at: new Date(),
      },
      include: {
        reply_to_message: true,
        attachments: true,
        reactions: true,
      },
    });

    return updatedMessage;
  }

  async deleteMessage(messageId: string, userId: string): Promise<void> {
    const message = await this.prisma.messages.findUnique({
      where: { id: messageId },
    });

    if (!message) {
      throw new NotFoundException('訊息不存在');
    }

    if (message.sender_id !== userId) {
      throw new ForbiddenException('您只能刪除自己的訊息');
    }

    await this.prisma.messages.update({
      where: { id: messageId },
      data: {
        is_deleted: true,
        deleted_at: new Date(),
      },
    });
  }

  async addReaction(messageId: string, userId: string, emoji: string) {
    // 檢查訊息是否存在
    const message = await this.prisma.messages.findUnique({
      where: { id: messageId },
    });

    if (!message || message.is_deleted) {
      throw new NotFoundException('訊息不存在');
    }

    // 檢查是否已存在相同的反應
    const existingReaction = await this.prisma.message_reactions.findFirst({
      where: {
        message_id: messageId,
        user_id: userId,
        emoji,
      },
    });

    if (existingReaction) {
      // 如果已存在，則移除反應
      await this.prisma.message_reactions.delete({
        where: { id: existingReaction.id },
      });
      return null;
    }

    // 建立新反應
    const reaction = await this.prisma.message_reactions.create({
      data: {
        message_id: messageId,
        user_id: userId,
        emoji,
      },
    });

    return reaction;
  }

  async removeReaction(messageId: string, userId: string, emoji: string): Promise<void> {
    const reaction = await this.prisma.message_reactions.findFirst({
      where: {
        message_id: messageId,
        user_id: userId,
        emoji,
      },
    });

    if (!reaction) {
      throw new NotFoundException('反應不存在');
    }

    await this.prisma.message_reactions.delete({
      where: { id: reaction.id },
    });
  }

  async markMessagesAsRead(
    conversationId: string,
    userId: string,
    messageIds?: string[],
  ): Promise<void> {
    // 檢查用戶權限
    const participant = await this.prisma.conversation_participants.findFirst({
      where: {
        conversation_id: conversationId,
        user_id: userId,
        status: 'ACTIVE',
      },
    });

    if (!participant) {
      throw new ForbiddenException('您沒有權限標記此對話的訊息為已讀');
    }

    if (messageIds && messageIds.length > 0) {
      // 標記特定訊息為已讀
      // TODO: 實作已讀狀態追蹤表
      console.log(`標記訊息 ${messageIds.join(', ')} 為已讀`);
    } else {
      // 標記對話中所有未讀訊息為已讀
      // TODO: 實作已讀狀態追蹤表
      console.log(`標記對話 ${conversationId} 中所有訊息為已讀`);
    }
  }

  async searchMessages(
    conversationId: string,
    userId: string,
    query: string,
    page: number = 1,
    limit: number = 20,
  ) {
    // 檢查用戶權限
    const participant = await this.prisma.conversation_participants.findFirst({
      where: {
        conversation_id: conversationId,
        user_id: userId,
        status: 'ACTIVE',
      },
    });

    if (!participant) {
      throw new ForbiddenException('您沒有權限搜尋此對話的訊息');
    }

    const skip = (page - 1) * limit;

    const [messages, total] = await Promise.all([
      this.prisma.messages.findMany({
        where: {
          conversation_id: conversationId,
          is_deleted: false,
          content: {
            contains: query,
            mode: 'insensitive',
          },
        },
        include: {
          reply_to_message: true,
          attachments: true,
        },
        orderBy: {
          created_at: 'desc',
        },
        skip,
        take: limit,
      }),
      this.prisma.messages.count({
        where: {
          conversation_id: conversationId,
          is_deleted: false,
          content: {
            contains: query,
            mode: 'insensitive',
          },
        },
      }),
    ]);

    return {
      messages,
      total,
      query,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findByConversation(conversationId: string): Promise<MessageWithReplies[]> {
    return this.prisma.messages.findMany({
      where: { conversation_id: conversationId },
      include: {
        reply_to_message: true,
        replies: {
          include: {
            reply_to_message: true,
            attachments: true,
            reactions: true,
          },
        },
        attachments: true,
        reactions: true,
      },
      orderBy: { created_at: 'asc' },
    });
  }

  async create(data: CreateMessageDto): Promise<MessageWithReplies> {
    const message = await this.prisma.messages.create({
      data: {
        content: data.content,
        conversation_id: data.conversation_id,
        sender_id: data.sender_id,
        reply_to_id: data.reply_to_message_id,
      },
      include: {
        reply_to_message: true,
        replies: {
          include: {
            reply_to_message: true,
            attachments: true,
            reactions: true,
          },
        },
        attachments: true,
        reactions: true,
      },
    });

    // 更新對話的最後訊息
    await this.prisma.conversations.update({
      where: { id: data.conversation_id },
      data: {
        last_message_id: message.id,
        last_activity_at: new Date(),
      },
    });

    return message;
  }

  async findById(id: string): Promise<MessageWithReplies | null> {
    return this.prisma.messages.findUnique({
      where: { id },
      include: {
        reply_to_message: true,
        replies: {
          include: {
            reply_to_message: true,
            attachments: true,
            reactions: true,
          },
        },
        attachments: true,
        reactions: true,
      },
    });
  }

  async update(id: string, data: UpdateMessageDto): Promise<MessageWithReplies> {
    return this.prisma.messages.update({
      where: { id },
      data,
      include: {
        reply_to_message: true,
        replies: {
          include: {
            reply_to_message: true,
            attachments: true,
            reactions: true,
          },
        },
        attachments: true,
        reactions: true,
      },
    });
  }
}
