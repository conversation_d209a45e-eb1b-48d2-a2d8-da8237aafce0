import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import { CreateCommentDto, UpdateCommentDto, AddCommentReactionDto } from './dto';
import { CommentEntityType, CommentReactionType } from '@prisma/client';
import { RealtimeEventsService } from '../websocket/realtime-events.service';

@Injectable()
export class CommentsService {
  constructor(
    private prisma: PrismaService,
    private realtimeEvents: RealtimeEventsService,
  ) {}

  async createComment(
    createCommentDto: CreateCommentDto,
    authorId: string,
    authorType: 'system' | 'tenant',
    tenantId: string,
    workspaceId?: string,
  ) {
    const { content, contentType, entityType, entityId, parentId, mentions } = createCommentDto;

    // 驗證實體是否存在
    await this.validateEntity(entityType, entityId, tenantId, workspaceId);

    // 如果是回覆，驗證父評論是否存在
    if (parentId) {
      const parentComment = await this.prisma.comments.findFirst({
        where: {
          id: parentId,
          tenant_id: tenantId,
          ...(workspaceId && { workspace_id: workspaceId }),
        },
      });

      if (!parentComment) {
        throw new NotFoundException('Parent comment not found');
      }
    }

    // 建立評論
    const comment = await this.prisma.comments.create({
      data: {
        content,
        content_type: contentType,
        entity_type: entityType,
        entity_id: entityId,
        parent_id: parentId,
        thread_id: parentId ? undefined : undefined, // 將在建立後設置
        author_id: authorId,
        author_type: authorType,
        tenant_id: tenantId,
        workspace_id: workspaceId,
      },
      include: {
        reactions: true,
        mentions: true,
        replies: {
          include: {
            reactions: true,
            mentions: true,
          },
        },
      },
    });

    // 如果是頂級評論，設置 threadId 為自己的 id
    if (!parentId) {
      await this.prisma.comments.update({
        where: { id: comment.id },
        data: { thread_id: comment.id },
      });
    } else {
      // 如果是回覆，設置 threadId 為父評論的 threadId
      const parentComment = await this.prisma.comments.findUnique({
        where: { id: parentId },
        select: { thread_id: true },
      });

      await this.prisma.comments.update({
        where: { id: comment.id },
        data: { thread_id: parentComment?.thread_id || parentId },
      });
    }

    // 處理提及
    if (mentions && mentions.length > 0) {
      await this.createMentions(comment.id, mentions, tenantId, workspaceId);
    }

    // 發送即時通知
    if (workspaceId) {
      await this.realtimeEvents.notifyCommentCreated({
        comment_id: comment.id,
        content: comment.content,
        entity_type: comment.entity_type,
        entity_id: comment.entity_id,
        author_id: comment.author_id,
        authorName: 'User', // 這裡可以從用戶資料中獲取真實姓名
        workspaceId,
        parentCommentId: comment.parent_id || undefined,
      });
    }

    return this.getCommentById(comment.id, tenantId, workspaceId);
  }

  async getCommentsByEntity(
    entityType: CommentEntityType,
    entityId: string,
    tenantId: string,
    workspaceId?: string,
    page = 1,
    limit = 20,
  ) {
    const skip = (page - 1) * limit;

    const [comments, total] = await Promise.all([
      this.prisma.comments.findMany({
        where: {
          entity_type: entityType,
          entity_id: entityId,
          tenant_id: tenantId,
          ...(workspaceId && { workspace_id: workspaceId }),
          parent_id: null, // 只獲取頂級評論
          is_deleted: false,
        },
        include: {
          reactions: true,
          mentions: true,
          replies: {
            where: { is_deleted: false },
            include: {
              reactions: true,
              mentions: true,
            },
            orderBy: { created_at: 'asc' },
          },
        },
        orderBy: { created_at: 'desc' },
        skip,
        take: limit,
      }),
      this.prisma.comments.count({
        where: {
          entity_type: entityType,
          entity_id: entityId,
          tenant_id: tenantId,
          ...(workspaceId && { workspace_id: workspaceId }),
          parent_id: null,
          is_deleted: false,
        },
      }),
    ]);

    return {
      comments,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getCommentById(commentId: string, tenantId: string, workspaceId?: string) {
    const comment = await this.prisma.comments.findFirst({
      where: {
        id: commentId,
        tenant_id: tenantId,
        ...(workspaceId && { workspace_id: workspaceId }),
        is_deleted: false,
      },
      include: {
        reactions: true,
        mentions: true,
        replies: {
          where: { is_deleted: false },
          include: {
            reactions: true,
            mentions: true,
          },
          orderBy: { created_at: 'asc' },
        },
      },
    });

    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    return comment;
  }

  async updateComment(
    commentId: string,
    updateCommentDto: UpdateCommentDto,
    authorId: string,
    authorType: 'system' | 'tenant',
    tenantId: string,
    workspaceId?: string,
  ) {
    const comment = await this.prisma.comments.findFirst({
      where: {
        id: commentId,
        tenant_id: tenantId,
        ...(workspaceId && { workspace_id: workspaceId }),
        is_deleted: false,
      },
    });

    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    // 檢查是否為評論作者
    if (comment.author_id !== authorId || comment.author_type !== authorType) {
      throw new ForbiddenException('You can only edit your own comments');
    }

    const { content, contentType, mentions } = updateCommentDto;

    // 更新評論
    const updatedComment = await this.prisma.comments.update({
      where: { id: commentId },
      data: {
        ...(content && { content }),
        ...(contentType && { content_type: contentType }),
        is_edited: true,
      },
      include: {
        reactions: true,
        mentions: true,
        replies: {
          include: {
            reactions: true,
            mentions: true,
          },
        },
      },
    });

    // 更新提及
    if (mentions !== undefined) {
      // 刪除舊的提及
      await this.prisma.comment_mentions.deleteMany({
        where: { comment_id: commentId },
      });

      // 建立新的提及
      if (mentions.length > 0) {
        await this.createMentions(commentId, mentions, tenantId, workspaceId);
      }
    }

    return this.getCommentById(commentId, tenantId, workspaceId);
  }

  async deleteComment(
    commentId: string,
    authorId: string,
    authorType: 'system' | 'tenant',
    tenantId: string,
    workspaceId?: string,
  ) {
    const comment = await this.prisma.comments.findFirst({
      where: {
        id: commentId,
        tenant_id: tenantId,
        ...(workspaceId && { workspace_id: workspaceId }),
        is_deleted: false,
      },
    });

    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    // 檢查是否為評論作者
    if (comment.author_id !== authorId || comment.author_type !== authorType) {
      throw new ForbiddenException('You can only delete your own comments');
    }

    // 軟刪除評論
    await this.prisma.comments.update({
      where: { id: commentId },
      data: {
        is_deleted: true,
        deleted_at: new Date(),
      },
    });

    return { message: 'Comment deleted successfully' };
  }

  async addReaction(
    commentId: string,
    addReactionDto: AddCommentReactionDto,
    userId: string,
    userType: 'system' | 'tenant',
    tenantId: string,
    workspaceId?: string,
  ) {
    const comment = await this.prisma.comments.findFirst({
      where: {
        id: commentId,
        tenant_id: tenantId,
        ...(workspaceId && { workspace_id: workspaceId }),
        is_deleted: false,
      },
    });

    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    const { reaction } = addReactionDto;

    // 檢查是否已經有相同的反應
    const existingReaction = await this.prisma.comment_reactions.findFirst({
      where: {
        comment_id: commentId,
        user_id: userId,
        user_type: userType,
      },
    });

    if (existingReaction) {
      if (existingReaction.reaction === reaction) {
        // 如果是相同反應，則移除
        await this.prisma.comment_reactions.delete({
          where: { id: existingReaction.id },
        });
        return { message: 'Reaction removed' };
      } else {
        // 如果是不同反應，則更新
        await this.prisma.comment_reactions.update({
          where: { id: existingReaction.id },
          data: { reaction },
        });
        return { message: 'Reaction updated' };
      }
    } else {
      // 建立新反應
      await this.prisma.comment_reactions.create({
        data: {
          comment_id: commentId,
          user_id: userId,
          user_type: userType,
          reaction,
          tenant_id: tenantId,
          workspace_id: workspaceId,
        },
      });
      return { message: 'Reaction added' };
    }
  }

  async removeReaction(
    commentId: string,
    userId: string,
    userType: 'system' | 'tenant',
    tenantId: string,
    workspaceId?: string,
  ) {
    const reaction = await this.prisma.comment_reactions.findFirst({
      where: {
        comment_id: commentId,
        user_id: userId,
        user_type: userType,
        tenant_id: tenantId,
        ...(workspaceId && { workspace_id: workspaceId }),
      },
    });

    if (!reaction) {
      throw new NotFoundException('Reaction not found');
    }

    await this.prisma.comment_reactions.delete({
      where: { id: reaction.id },
    });

    return { message: 'Reaction removed' };
  }

  private async validateEntity(
    entity_type: CommentEntityType,
    entity_id: string,
    tenant_id: string,
    workspace_id?: string,
  ) {
    let entity;

    switch (entity_type) {
      case CommentEntityType.PROJECT:
        entity = await this.prisma.projects.findFirst({
          where: {
            id: entity_id,
            tenant_id,
            ...(workspace_id && { workspace_id }),
          },
        });
        break;
      case CommentEntityType.TASK:
        entity = await this.prisma.tasks.findFirst({
          where: {
            id: entity_id,
            project: {
              tenant_id,
              ...(workspace_id && { workspace_id }),
            },
          },
        });
        break;
      case CommentEntityType.PROGRESS_ENTRY:
        entity = await this.prisma.progress_entries.findFirst({
          where: {
            id: entity_id,
            project: {
              tenant_id,
              ...(workspace_id && { workspace_id }),
            },
          },
        });
        break;
      case CommentEntityType.MILESTONE:
        entity = await this.prisma.project_milestones.findFirst({
          where: {
            id: entity_id,
            project: {
              tenant_id,
              ...(workspace_id && { workspace_id }),
            },
          },
        });
        break;
      case CommentEntityType.PHOTO:
        entity = await this.prisma.photos.findFirst({
          where: {
            id: entity_id,
            tenant_id,
            ...(workspace_id && { workspace_id }),
          },
        });
        break;
      default:
        throw new BadRequestException('Unsupported entity type');
    }

    if (!entity) {
      throw new NotFoundException(`${entity_type} not found`);
    }
  }

  private async createMentions(
    commentId: string,
    mentions: string[],
    tenantId: string,
    workspaceId?: string,
  ) {
    const mentionData = mentions.map((userId) => ({
      comment_id: commentId,
      user_id: userId,
      user_type: 'tenant' as const, // 假設都是租戶用戶
      tenant_id: tenantId,
      workspace_id: workspaceId,
    }));

    await this.prisma.comment_mentions.createMany({
      data: mentionData,
      skipDuplicates: true,
    });
  }
}
