import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { PrismaService } from '../../core/prisma/prisma.service';
import { StorageService } from '../../core/storage/storage.service';
import { SettingsService } from '../../admin/settings/settings.service';
import {
  UploadFileDto,
  UpdateFileDto,
  CreateShareDto,
  GrantFilePermissionDto,
  BulkGrantPermissionDto,
} from './dto';
import {
  FileCategory,
  FileEntityType,
  FileVisibility,
  FileStatus,
  FilePermission,
  AccessType,
  ShareType,
} from '@prisma/client';
import * as path from 'path';
import * as crypto from 'crypto';
// import sharp from 'sharp'; // 改為動態導入
import { createReadStream } from 'fs';
import { Readable } from 'stream';

@Injectable()
export class FilesService {
  constructor(
    private prisma: PrismaService,
    private storageService: StorageService,
    private settingsService: SettingsService,
    private eventEmitter: EventEmitter2,
  ) {}

  async uploadFile(
    workspaceId: string,
    userId: string,
    file: Express.Multer.File,
    uploadFileDto: UploadFileDto,
    ipAddress?: string,
  ) {
    const workspace = await this.prisma.workspaces.findUnique({
      where: { id: workspaceId },
      select: { tenant_id: true },
    });

    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    const tenantId = workspace.tenant_id;
    const uploaderType = 'tenant';

    const storageSettings = await this.settingsService.getStorageSettings();
    await this.validateFile(file, storageSettings);

    const category = uploadFileDto.category || this.determineFileCategory(file.mimetype);
    const fileName = uploadFileDto.name || file.originalname;
    const fileExtension = path.extname(file.originalname);
    const uniqueFileName = `${crypto.randomUUID()}${fileExtension}`;

    const filePath = await this.storageService.uploadFile({
      ...file,
      originalname: uniqueFileName,
    });

    let thumbnailPath: string | undefined;
    let previewPath: string | undefined;
    let metadata: any = {};

    if (category === FileCategory.IMAGE) {
      const result = await this.generateImagePreviews(file, uniqueFileName);
      thumbnailPath = result.thumbnailPath;
      previewPath = result.previewPath;
      metadata = result.metadata;
    }

    if (uploadFileDto.entityType && uploadFileDto.entityId) {
      await this.validateEntityAccess(
        uploadFileDto.entityType,
        uploadFileDto.entityId,
        tenantId,
        workspaceId,
      );
    }

    const sharedFile = await this.prisma.shared_files.create({
      data: {
        name: fileName,
        original_name: file.originalname,
        description: uploadFileDto.description,
        file_type: file.mimetype,
        file_extension: fileExtension,
        file_size: file.size,
        file_path: filePath,
        category,
        entity_type: uploadFileDto.entityType,
        entity_id: uploadFileDto.entityId,
        thumbnail_path: thumbnailPath,
        preview_path: previewPath,
        metadata,
        visibility: uploadFileDto.visibility || FileVisibility.PRIVATE,
        allow_download: uploadFileDto.allowDownload ?? true,
        allow_comment: uploadFileDto.allowComment ?? true,
        expires_at: uploadFileDto.expiresAt ? new Date(uploadFileDto.expiresAt) : null,
        uploader_id: userId,
        uploader_type: uploaderType,
        tenant_id: tenantId,
        workspace_id: workspaceId,
      },
      include: {
        permissions: true,
        shares: true,
      },
    });

    await this.logFileAccess(
      sharedFile.id,
      AccessType.VIEW,
      userId,
      uploaderType,
      tenantId,
      ipAddress,
    );

    // 發出 file.uploaded 事件以觸發 RAG 索引
    this.eventEmitter.emit('file.uploaded', {
      filePath: filePath,
      tenantId: tenantId,
      fileId: sharedFile.id,
      workspaceId: workspaceId,
      fileName: fileName,
      mimeType: file.mimetype,
    });

    return sharedFile;
  }

  async getFiles(
    workspaceId: string,
    userId: string,
    filters?: {
      entityType?: string;
      entityId?: string;
      category?: string;
      search?: string;
      page?: number;
      limit?: number;
    },
  ) {
    const workspace = await this.prisma.workspaces.findUnique({
      where: { id: workspaceId },
      select: { tenant_id: true },
    });

    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    const tenantId = workspace.tenant_id;
    const userType = 'tenant';

    return this.getFilesInternal(
      tenantId,
      workspaceId,
      userId,
      userType,
      {
        category: filters?.category as FileCategory,
        entityType: filters?.entityType as FileEntityType,
        entityId: filters?.entityId,
        search: filters?.search,
      },
      {
        page: filters?.page,
        limit: filters?.limit,
      },
    );
  }

  async getFilesInternal(
    tenantId: string,
    workspaceId?: string,
    userId?: string,
    userType?: string,
    filters?: {
      category?: FileCategory;
      entityType?: FileEntityType;
      entityId?: string;
      visibility?: FileVisibility;
      search?: string;
    },
    pagination?: {
      page?: number;
      limit?: number;
    },
  ) {
    const page = pagination?.page || 1;
    const limit = pagination?.limit || 20;
    const skip = (page - 1) * limit;

    const where: any = {
      tenant_id: tenantId,
      ...(workspaceId && { workspace_id: workspaceId }),
      is_deleted: false,
      status: FileStatus.ACTIVE,
    };

    if (filters?.category) where.category = filters.category;
    if (filters?.entityType) where.entityType = filters.entityType;
    if (filters?.entityId) where.entityId = filters.entityId;
    if (filters?.visibility) where.visibility = filters.visibility;

    if (filters?.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
        { originalName: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    if (userId && userType) {
      where.OR = [
        ...(where.OR || []),
        { uploader_id: userId, uploader_type: userType },
        { visibility: FileVisibility.PUBLIC },
        { visibility: FileVisibility.TENANT },
        ...(workspaceId ? [{ visibility: FileVisibility.WORKSPACE }] : []),
        {
          permissions: {
            some: {
              user_id: userId,
              user_type: userType,
              OR: [{ expires_at: null }, { expires_at: { gt: new Date() } }],
            },
          },
        },
      ];
    }

    const [files, total] = await this.prisma.$transaction([
      this.prisma.shared_files.findMany({
        where,
        skip,
        take: limit,
        orderBy: { created_at: 'desc' },
        include: {
          permissions: true,
        },
      }),
      this.prisma.shared_files.count({ where }),
    ]);

    return {
      data: files,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getFile(workspaceId: string, file_id: string, userId: string) {
    // 獲取工作區信息
    const workspace = await this.prisma.workspaces.findUnique({
      where: { id: workspaceId },
      select: { tenant_id: true },
    });

    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    const tenantId = workspace.tenant_id;
    const userType = 'tenant';

    return this.getFileById(file_id, tenantId, workspaceId, userId, userType);
  }

  async updateFile(
    workspaceId: string,
    file_id: string,
    userId: string,
    updateFileDto: UpdateFileDto,
  ) {
    // 獲取工作區信息
    const workspace = await this.prisma.workspaces.findUnique({
      where: { id: workspaceId },
      select: { tenant_id: true },
    });

    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    const tenantId = workspace.tenant_id;
    const userType = 'tenant';

    return this.updateFileInternal(file_id, updateFileDto, userId, userType, tenantId, workspaceId);
  }

  async updateFileInternal(
    file_id: string,
    updateFileDto: UpdateFileDto,
    userId: string,
    userType: 'system' | 'tenant',
    tenantId: string,
    workspaceId?: string,
  ) {
    // 檢查檔案是否存在和權限
    const file = await this.getFileById(file_id, tenantId, workspaceId, userId, userType);

    if (!file) {
      throw new NotFoundException('File not found');
    }

    // 檢查是否有編輯權限
    const hasPermission = await this.checkFileAccess(file, userId, userType, FilePermission.EDIT);
    if (!hasPermission) {
      throw new ForbiddenException('Insufficient permissions to edit this file');
    }

    // 更新檔案
    const updatedFile = await this.prisma.shared_files.update({
      where: { id: file_id },
      data: {
        name: updateFileDto.name,
        description: updateFileDto.description,
        visibility: updateFileDto.visibility,
        allow_download: updateFileDto.allowDownload,
        allow_comment: updateFileDto.allowComment,
        expires_at: updateFileDto.expiresAt ? new Date(updateFileDto.expiresAt) : null,
        updated_at: new Date(),
      },
      include: {
        permissions: true,
        shares: true,
      },
    });

    return updatedFile;
  }

  async deleteFile(workspaceId: string, file_id: string, userId: string) {
    // 獲取工作區信息
    const workspace = await this.prisma.workspaces.findUnique({
      where: { id: workspaceId },
      select: { tenant_id: true },
    });

    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    const tenantId = workspace.tenant_id;
    const userType = 'tenant';

    return this.deleteFileInternal(file_id, userId, userType, tenantId, workspaceId);
  }

  async deleteFileInternal(
    file_id: string,
    userId: string,
    userType: 'system' | 'tenant',
    tenantId: string,
    workspaceId?: string,
  ) {
    // 檢查檔案是否存在和權限
    const file = await this.getFileById(file_id, tenantId, workspaceId, userId, userType);

    if (!file) {
      throw new NotFoundException('File not found');
    }

    // 檢查是否有刪除權限
    const hasPermission = await this.checkFileAccess(file, userId, userType, FilePermission.DELETE);
    if (!hasPermission) {
      throw new ForbiddenException('Insufficient permissions to delete this file');
    }

    // 軟刪除檔案
    await this.prisma.shared_files.update({
      where: { id: file_id },
      data: {
        is_deleted: true,
        deleted_at: new Date(),
      },
    });

    // 記錄存取日誌
    await this.logFileAccess(file_id, AccessType.DOWNLOAD, userId, userType, tenantId);

    return { message: 'File deleted successfully' };
  }

  async downloadFile(workspaceId: string, file_id: string, userId: string, version?: number) {
    // 獲取工作區信息
    const workspace = await this.prisma.workspaces.findUnique({
      where: { id: workspaceId },
      select: { tenant_id: true },
    });

    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    const tenantId = workspace.tenant_id;
    const userType = 'tenant';

    // 獲取檔案信息
    const file = await this.getFileById(file_id, tenantId, workspaceId, userId, userType);

    if (!file) {
      throw new NotFoundException('File not found');
    }

    // 檢查下載權限
    if (!file.allow_download) {
      throw new ForbiddenException('File download is not allowed');
    }

    const hasPermission = await this.checkFileAccess(
      file,
      userId,
      userType,
      FilePermission.DOWNLOAD,
    );
    if (!hasPermission) {
      throw new ForbiddenException('Insufficient permissions to download this file');
    }

    // 記錄存取日誌
    await this.logFileAccess(file_id, AccessType.DOWNLOAD, userId, userType, tenantId);

    // 建立檔案流
    const stream = createReadStream(file.file_path);

    return {
      file: {
        mimeType: file.file_type,
        original_name: file.original_name,
        file_size: file.file_size,
      },
      stream,
    };
  }

  async getFilePreview(
    workspaceId: string,
    file_id: string,
    userId: string,
    size: 'small' | 'medium' | 'large' = 'medium',
  ) {
    // 獲取工作區信息
    const workspace = await this.prisma.workspaces.findUnique({
      where: { id: workspaceId },
      select: { tenant_id: true },
    });

    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    const tenantId = workspace.tenant_id;
    const userType = 'tenant';

    // 獲取檔案信息
    const file = await this.getFileById(file_id, tenantId, workspaceId, userId, userType);

    if (!file) {
      throw new NotFoundException('File not found');
    }

    // 檢查查看權限
    const hasPermission = await this.checkFileAccess(file, userId, userType, FilePermission.VIEW);
    if (!hasPermission) {
      throw new ForbiddenException('Insufficient permissions to view this file');
    }

    // 記錄存取日誌
    await this.logFileAccess(file_id, AccessType.VIEW, userId, userType, tenantId);

    // 根據檔案類型和大小返回適當的預覽
    let previewPath = file.file_path;
    if (file.category === FileCategory.IMAGE) {
      switch (size) {
        case 'small':
          previewPath = file.thumbnail_path || file.file_path;
          break;
        case 'medium':
          previewPath = file.preview_path || file.file_path;
          break;
        case 'large':
        default:
          previewPath = file.file_path;
          break;
      }
    }

    const stream = createReadStream(previewPath);

    return {
      file: {
        mimeType: file.file_type,
        original_name: file.original_name,
        file_size: file.file_size,
      },
      stream,
    };
  }

  async getFileVersions(workspaceId: string, file_id: string, userId: string) {
    // 獲取工作區信息
    const workspace = await this.prisma.workspaces.findUnique({
      where: { id: workspaceId },
      select: { tenant_id: true },
    });

    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    const tenantId = workspace.tenant_id;
    const userType = 'tenant';

    // 獲取檔案信息
    const file = await this.getFileById(file_id, tenantId, workspaceId, userId, userType);

    if (!file) {
      throw new NotFoundException('File not found');
    }

    // 檢查查看權限
    const hasPermission = await this.checkFileAccess(file, userId, userType, FilePermission.VIEW);
    if (!hasPermission) {
      throw new ForbiddenException('Insufficient permissions to view file versions');
    }

    // 獲取檔案版本（假設有版本表）
    // 這裡需要根據實際的版本表結構來實現
    return {
      file_id,
      versions: [], // 暫時返回空陣列
    };
  }

  async uploadNewVersion(
    workspaceId: string,
    file_id: string,
    userId: string,
    file: Express.Multer.File,
    description?: string,
  ) {
    // 獲取工作區信息
    const workspace = await this.prisma.workspaces.findUnique({
      where: { id: workspaceId },
      select: { tenant_id: true },
    });

    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    const tenantId = workspace.tenant_id;
    const userType = 'tenant';

    return this.createFileVersion(file_id, file, userId, userType, tenantId, workspaceId);
  }

  async bulkGrantPermissions(
    workspaceId: string,
    file_id: string,
    userId: string,
    bulkGrantDto: BulkGrantPermissionDto,
  ) {
    // 獲取工作區信息
    const workspace = await this.prisma.workspaces.findUnique({
      where: { id: workspaceId },
      select: { tenant_id: true },
    });

    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    const tenantId = workspace.tenant_id;

    // 實現批量授權邏輯
    const results: any[] = [];

    for (const targetUserId of bulkGrantDto.userIds) {
      try {
        const grantPermissionDto: GrantFilePermissionDto = {
          userId: targetUserId,
          userType: bulkGrantDto.userType,
          permission: bulkGrantDto.permission,
          expiresAt: bulkGrantDto.expiresAt,
        };

        const result = await this.grantPermission(
          file_id,
          grantPermissionDto,
          userId,
          tenantId,
          workspaceId,
        );
        results.push({ success: true, userId: targetUserId, result });
      } catch (error) {
        results.push({ success: false, userId: targetUserId, error: error.message });
      }
    }
    return { results };
  }

  async getFilePermissions(workspaceId: string, file_id: string, userId: string) {
    // 獲取工作區信息
    const workspace = await this.prisma.workspaces.findUnique({
      where: { id: workspaceId },
      select: { tenant_id: true },
    });

    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    const tenantId = workspace.tenant_id;
    const userType = 'tenant';

    // 直接查詢檔案權限
    const permissions = await this.prisma.file_permissions.findMany({
      where: {
        file_id,
        tenant_id: tenantId,
      },
    });

    return permissions;
  }

  async revokePermission(
    workspaceId: string,
    file_id: string,
    permissionId: string,
    userId: string,
  ) {
    // 實現撤銷權限邏輯
    await this.prisma.file_permissions.delete({
      where: { id: permissionId },
    });

    return { message: 'Permission revoked successfully' };
  }

  async createFileShare(
    workspaceId: string,
    file_id: string,
    userId: string,
    createShareDto: CreateShareDto,
  ) {
    // 獲取工作區信息
    const workspace = await this.prisma.workspaces.findUnique({
      where: { id: workspaceId },
      select: { tenant_id: true },
    });

    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    const tenantId = workspace.tenant_id;
    const userType = 'tenant';

    return this.createShare(file_id, createShareDto, userId, userType, tenantId, workspaceId);
  }

  async getFileShares(workspaceId: string, file_id: string, userId: string) {
    // 獲取工作區信息
    const workspace = await this.prisma.workspaces.findUnique({
      where: { id: workspaceId },
      select: { tenant_id: true },
    });

    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    const tenantId = workspace.tenant_id;

    // 獲取檔案分享
    const shares = await this.prisma.file_shares.findMany({
      where: {
        file_id,
        // 可以添加更多過濾條件
      },
      include: {
        // 根據需要包含相關數據
      },
    });

    return shares;
  }

  async deleteFileShare(workspaceId: string, file_id: string, shareId: string, userId: string) {
    // 刪除檔案分享
    await this.prisma.file_shares.delete({
      where: { id: shareId },
    });

    return { message: 'Share deleted successfully' };
  }

  async getAccessLogs(
    workspaceId: string,
    file_id: string,
    userId: string,
    pagination: { page: number; limit: number },
  ) {
    // 獲取工作區信息
    const workspace = await this.prisma.workspaces.findUnique({
      where: { id: workspaceId },
      select: { tenant_id: true },
    });

    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    const tenantId = workspace.tenant_id;

    return this.getFileAccessLogs(file_id, tenantId, workspaceId, pagination);
  }

  async getSharedFile(shareToken: string) {
    const share = (await this.prisma.file_shares.findUnique({
      where: { share_token: shareToken },
      include: { file: true },
    })) as any;

    if (!share) {
      throw new NotFoundException('Share not found');
    }

    // 檢查分享是否過期
    if (share.expires_at && share.expires_at < new Date()) {
      throw new ForbiddenException('Share has expired');
    }

    return share;
  }

  async downloadSharedFile(shareToken: string, password?: string) {
    const share = await this.getSharedFile(shareToken);

    // 檢查密碼（如果需要）
    if (share.password && share.password !== password) {
      throw new ForbiddenException('Invalid password');
    }

    // 記錄存取
    await this.logFileAccess(
      share.file_id,
      AccessType.DOWNLOAD,
      undefined,
      undefined,
      undefined,
      undefined,
      share.id,
    );

    const stream = createReadStream(share.file.file_path);

    return {
      file: {
        mimeType: share.file.file_type,
        original_name: share.file.original_name,
        file_size: share.file.file_size,
      },
      stream,
    };
  }

  async previewSharedFile(
    shareToken: string,
    password?: string,
    size: 'small' | 'medium' | 'large' = 'medium',
  ) {
    const share: any = await this.getSharedFile(shareToken);

    // 檢查密碼（如果需要）
    if (share.password && share.password !== password) {
      throw new ForbiddenException('Invalid password');
    }

    // 記錄存取
    await this.logFileAccess(
      share.file_id,
      AccessType.VIEW,
      undefined,
      undefined,
      undefined,
      undefined,
      share.id,
    );

    // 根據檔案類型和大小返回適當的預覽
    let previewPath = share.file.file_path;
    let stream: Readable;
    if (share.file.category === FileCategory.IMAGE) {
      switch (size) {
        case 'small':
          previewPath = share.file.thumbnail_path || share.file.file_path;
          break;
        case 'medium':
          previewPath = share.file.preview_path || share.file.file_path;
          break;
        case 'large':
        default:
          previewPath = share.file.file_path;
          break;
      }
    }

    stream = createReadStream(previewPath);

    return {
      file: {
        mimeType: share.file.file_type,
        original_name: share.file.original_name,
        file_size: share.file.file_size,
      },
      stream,
    };
  }

  async getFileById(
    file_id: string,
    tenantId: string,
    workspaceId?: string,
    userId?: string,
    userType?: string,
    ipAddress?: string,
  ) {
    const file = await this.prisma.shared_files.findFirst({
      where: {
        id: file_id,
        tenant_id: tenantId,
        ...(workspaceId && { workspace_id: workspaceId }),
        is_deleted: false,
      },
      include: {
        permissions: true,
        shares: {
          where: { is_active: true },
        },
        versions: {
          where: { is_deleted: false },
          orderBy: { version: 'desc' },
          take: 10,
        },
        parent_file: true,
        workspaces: true,
        _count: {
          select: {
            versions: true,
            access_logs: true,
          },
        },
      },
    });

    if (!file) {
      throw new NotFoundException('File not found');
    }

    // 檢查存取權限
    if (userId && userType) {
      const hasAccess = await this.checkFileAccess(file, userId, userType, FilePermission.VIEW);

      if (!hasAccess) {
        throw new ForbiddenException('Access denied');
      }

      // 記錄存取日誌
      await this.logFileAccess(file_id, AccessType.VIEW, userId, userType, tenantId, ipAddress);
    }

    return file;
  }

  async createFileVersion(
    file_id: string,
    newFile: Express.Multer.File,
    userId: string,
    userType: 'system' | 'tenant',
    tenantId: string,
    workspaceId?: string,
  ) {
    const originalFile = await this.getFileById(file_id, tenantId, workspaceId, userId, userType);

    // 檢查編輯權限
    const hasEditAccess = await this.checkFileAccess(
      originalFile,
      userId,
      userType,
      FilePermission.EDIT,
    );

    if (!hasEditAccess) {
      throw new ForbiddenException('Edit access denied');
    }

    // 驗證檔案類型一致性
    if (newFile.mimetype !== originalFile.file_type) {
      throw new BadRequestException('File type must match the original file');
    }

    // 獲取下一個版本號
    const latestVersion = await this.prisma.shared_files.findFirst({
      where: {
        OR: [{ id: file_id }, { parent_file_id: originalFile.parent_file_id || file_id }],
      },
      orderBy: { version: 'desc' },
    });

    const nextVersion = (latestVersion?.version || 0) + 1;

    // 上傳新版本檔案
    const fileExtension = path.extname(newFile.originalname);
    const uniqueFileName = `${crypto.randomUUID()}${fileExtension}`;
    const filePath = await this.storageService.uploadFile({
      ...newFile,
      originalname: uniqueFileName,
    });

    // 生成預覽（如果需要）
    let thumbnailPath: string | undefined;
    let previewPath: string | undefined;
    let metadata: any = {};

    if (originalFile.category === FileCategory.IMAGE) {
      const result = await this.generateImagePreviews(newFile, uniqueFileName);
      thumbnailPath = result.thumbnailPath;
      previewPath = result.previewPath;
      metadata = result.metadata;
    }

    // 將舊版本標記為非最新
    await this.prisma.shared_files.updateMany({
      where: {
        OR: [{ id: file_id }, { parent_file_id: originalFile.parent_file_id || file_id }],
      },
      data: { is_latest_version: false },
    });

    // 建立新版本
    const newVersion = await this.prisma.shared_files.create({
      data: {
        name: originalFile.name,
        original_name: newFile.originalname,
        description: originalFile.description,
        file_type: newFile.mimetype,
        file_extension: fileExtension,
        file_size: newFile.size,
        file_path: filePath,
        category: originalFile.category,
        entity_type: originalFile.entity_type,
        entity_id: originalFile.entity_id,
        version: nextVersion,
        parent_file_id: originalFile.parent_file_id || file_id,
        is_latest_version: true,
        thumbnail_path: thumbnailPath,
        preview_path: previewPath,
        metadata,
        visibility: originalFile.visibility,
        allow_download: originalFile.allow_download,
        allow_comment: originalFile.allow_comment,
        expires_at: originalFile.expires_at,
        uploader_id: userId,
        uploader_type: userType,
        tenant_id: tenantId,
        workspace_id: workspaceId,
      },
      include: {
        permissions: true,
        shares: true,
        parent_file: true,
      },
    });

    // 發出 file.uploaded 事件以觸發 RAG 索引（新版本）
    this.eventEmitter.emit('file.uploaded', {
      filePath: filePath,
      tenantId: tenantId,
      fileId: newVersion.id,
      workspaceId: workspaceId,
      fileName: originalFile.name,
      mimeType: newFile.mimetype,
    });

    return newVersion;
  }

  async createShare(
    file_id: string,
    createShareDto: CreateShareDto,
    userId: string,
    userType: 'system' | 'tenant',
    tenantId: string,
    workspaceId?: string,
  ) {
    const file = await this.getFileById(file_id, tenantId, workspaceId, userId, userType);

    // 檢查分享權限
    const hasShareAccess = await this.checkFileAccess(file, userId, userType, FilePermission.SHARE);

    if (!hasShareAccess) {
      throw new ForbiddenException('Share access denied');
    }

    // 生成分享令牌
    const shareToken = crypto.randomBytes(32).toString('hex');

    const share = await this.prisma.file_shares.create({
      data: {
        file_id: file_id,
        share_token: shareToken,
        share_type: createShareDto.shareType ?? ShareType.LINK,
        allow_download: createShareDto.allowDownload ?? true,
        allow_comment: createShareDto.allowComment ?? false,
        require_auth: createShareDto.requireAuth ?? false,
        password: createShareDto.password,
        max_downloads: createShareDto.maxDownloads,
        expires_at: createShareDto.expiresAt ? new Date(createShareDto.expiresAt) : null,
        shared_by: userId,
        shared_by_type: userType,
        tenant_id: tenantId,
      },
      include: {
        file: true,
      },
    });

    return share;
  }

  async grantPermission(
    file_id: string,
    grantPermissionDto: GrantFilePermissionDto,
    grantedBy: string,
    tenantId: string,
    workspaceId?: string,
  ) {
    const file = await this.getFileById(file_id, tenantId, workspaceId);

    // 檢查是否有管理權限
    const hasManageAccess = await this.checkFileAccess(
      file,
      grantedBy,
      'tenant', // 假設都是租戶用戶
      FilePermission.MANAGE,
    );

    if (!hasManageAccess) {
      throw new ForbiddenException('Manage access denied');
    }

    // 檢查權限是否已存在
    const existingPermission = await this.prisma.file_permissions.findFirst({
      where: {
        file_id: file_id,
        user_id: grantPermissionDto.userId,
        user_type: grantPermissionDto.userType ?? 'tenant',
      },
    });

    if (existingPermission) {
      return this.prisma.file_permissions.update({
        where: { id: existingPermission.id },
        data: {
          permission: grantPermissionDto.permission,
          expires_at: grantPermissionDto.expiresAt ? new Date(grantPermissionDto.expiresAt) : null,
          granted_by: grantedBy,
          granted_at: new Date(),
        },
      });
    } else {
      return this.prisma.file_permissions.create({
        data: {
          file_id: file_id,
          user_id: grantPermissionDto.userId,
          user_type: grantPermissionDto.userType ?? 'tenant',
          permission: grantPermissionDto.permission,
          granted_by: grantedBy,
          expires_at: grantPermissionDto.expiresAt ? new Date(grantPermissionDto.expiresAt) : null,
          tenant_id: tenantId,
        },
      });
    }
  }

  async getFileAccessLogs(
    file_id: string,
    tenantId: string,
    workspaceId?: string,
    pagination?: { page?: number; limit?: number },
  ) {
    const page = pagination?.page || 1;
    const limit = pagination?.limit || 50;
    const skip = (page - 1) * limit;

    const [logs, total] = await Promise.all([
      this.prisma.file_access_logs.findMany({
        where: {
          file_id,
          tenant_id: tenantId,
        },
        orderBy: { accessed_at: 'desc' },
        skip,
        take: limit,
      }),
      this.prisma.file_access_logs.count({
        where: {
          file_id,
          tenant_id: tenantId,
        },
      }),
    ]);

    return {
      logs,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  private async validateFile(file: Express.Multer.File, storageSettings: any) {
    // 檢查檔案大小
    const maxFileSize = (storageSettings.maxFileSize || 100) * 1024 * 1024; // MB to bytes
    if (file.size > maxFileSize) {
      throw new BadRequestException(`File size exceeds limit of ${storageSettings.maxFileSize}MB`);
    }

    // 檢查檔案類型
    if (storageSettings.allowedFileTypes && storageSettings.allowedFileTypes.length > 0) {
      const fileExtension = path.extname(file.originalname).toLowerCase();
      if (!storageSettings.allowedFileTypes.includes(fileExtension)) {
        throw new BadRequestException(`File type ${fileExtension} is not allowed`);
      }
    }
  }

  private determineFileCategory(mimeType: string): FileCategory {
    if (mimeType.startsWith('image/')) return FileCategory.IMAGE;
    if (mimeType.startsWith('video/')) return FileCategory.VIDEO;
    if (mimeType.startsWith('audio/')) return FileCategory.AUDIO;
    if (mimeType.includes('pdf')) return FileCategory.DOCUMENT;
    if (mimeType.includes('spreadsheet') || mimeType.includes('excel'))
      return FileCategory.SPREADSHEET;
    if (mimeType.includes('presentation') || mimeType.includes('powerpoint'))
      return FileCategory.PRESENTATION;
    if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('tar'))
      return FileCategory.ARCHIVE;
    return FileCategory.DOCUMENT;
  }

  private async generateImagePreviews(file: Express.Multer.File, fileName: string) {
    try {
      // 動態導入 Sharp
      const sharp = await import('sharp').then((m) => m.default);

      const baseName = path.parse(fileName).name;

      // 生成縮圖 (200x200)
      const thumbnailBuffer = await sharp(file.buffer)
        .resize(200, 200, { fit: 'cover' })
        .jpeg({ quality: 80 })
        .toBuffer();

      const thumbnailFileName = `${baseName}_thumb.jpg`;
      const thumbnailPath = await this.storageService.uploadFile({
        ...file,
        buffer: thumbnailBuffer,
        originalname: thumbnailFileName,
      });

      // 生成預覽 (800x600)
      const previewBuffer = await sharp(file.buffer)
        .resize(800, 600, { fit: 'inside', withoutEnlargement: true })
        .jpeg({ quality: 85 })
        .toBuffer();

      const previewFileName = `${baseName}_preview.jpg`;
      const previewPath = await this.storageService.uploadFile({
        ...file,
        buffer: previewBuffer,
        originalname: previewFileName,
      });

      // 獲取圖片元數據
      const metadata = await sharp(file.buffer).metadata();

      return {
        thumbnailPath,
        previewPath,
        metadata: {
          width: metadata.width,
          height: metadata.height,
          format: metadata.format,
          size: file.size,
        },
      };
    } catch (error) {
      console.error('Failed to generate image previews:', error);
      console.warn('Sharp module not available, skipping image preview generation');
      return {
        thumbnailPath: undefined,
        previewPath: undefined,
        metadata: {},
      };
    }
  }

  private async validateEntityAccess(
    entityType: FileEntityType,
    entityId: string,
    tenantId: string,
    workspaceId?: string,
  ) {
    // 根據實體類型驗證存取權限
    switch (entityType) {
      case FileEntityType.PROJECT:
        const project = await this.prisma.projects.findFirst({
          where: {
            id: entityId,
            tenant_id: tenantId,
            ...(workspaceId && { workspace_id: workspaceId }),
          },
        });
        if (!project) {
          throw new NotFoundException('Project not found');
        }
        break;

      case FileEntityType.TASK:
        const task = await this.prisma.tasks.findFirst({
          where: {
            id: entityId,
            project: {
              tenant_id: tenantId,
              ...(workspaceId && { workspace_id: workspaceId }),
            },
          },
        });
        if (!task) {
          throw new NotFoundException('Task not found');
        }
        break;

      // 其他實體類型的驗證...
    }
  }

  private async checkFileAccess(
    file: any,
    userId: string,
    userType: string,
    requiredPermission: FilePermission,
  ): Promise<boolean> {
    // 檔案上傳者總是有完整權限
    if (file.uploader_id === userId && file.uploader_type === userType) {
      return true;
    }

    // 檢查可見性設定
    switch (file.visibility) {
      case FileVisibility.PUBLIC:
        return true;
      case FileVisibility.TENANT:
        // 租戶內用戶可存取
        return true;
      case FileVisibility.WORKSPACE:
        // 工作區成員可存取（需要額外檢查）
        return true;
      case FileVisibility.PRIVATE:
        // 需要明確權限
        break;
    }

    // 檢查明確權限
    const permission = await this.prisma.file_permissions.findFirst({
      where: {
        file_id: file.id,
        user_id: userId,
        user_type: userType,
        OR: [{ expires_at: null }, { expires_at: { gt: new Date() } }],
      },
    });

    if (!permission) {
      return false;
    }

    // 檢查權限層級
    const permissionHierarchy = {
      [FilePermission.VIEW]: 1,
      [FilePermission.DOWNLOAD]: 2,
      [FilePermission.COMMENT]: 3,
      [FilePermission.EDIT]: 4,
      [FilePermission.SHARE]: 5,
      [FilePermission.DELETE]: 6,
      [FilePermission.MANAGE]: 7,
    };

    return permissionHierarchy[permission.permission] >= permissionHierarchy[requiredPermission];
  }

  private async logFileAccess(
    file_id: string,
    accessType: AccessType,
    userId?: string,
    userType?: string,
    tenantId?: string,
    ipAddress?: string,
    shareId?: string,
    userAgent?: string,
  ) {
    try {
      await this.prisma.file_access_logs.create({
        data: {
          file_id: file_id,
          share_id: shareId,
          user_id: userId,
          user_type: userType,
          ip_address: ipAddress || 'unknown',
          user_agent: userAgent,
          access_type: accessType,
          tenant_id: tenantId || 'unknown',
        },
      });
    } catch (error) {
      console.error('Failed to log file access:', error);
    }
  }
}
