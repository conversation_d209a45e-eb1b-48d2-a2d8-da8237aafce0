import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  Min,
  Max,
  IsArray,
  IsUrl,
  IsEnum,
  IsUUID,
} from 'class-validator';
import { ProgressType } from '@prisma/client';

export class CreateProgressEntryDto {
  @ApiProperty({
    description: 'The title of the progress entry.',
    example: 'Foundation pouring complete',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiPropertyOptional({
    description: 'A detailed description of the progress.',
    example: 'The foundation for sector A has been successfully poured.',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'The type of progress entry.',
    enum: ProgressType,
    example: ProgressType.TASK_UPDATE,
  })
  @IsEnum(ProgressType)
  progress_type: ProgressType;

  @ApiPropertyOptional({
    description: 'The progress percentage value (0-100).',
    example: 75.5,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  progress_value?: number;

  @ApiPropertyOptional({
    description: 'An optional status update message.',
    example: 'Completed',
  })
  @IsString()
  @IsOptional()
  status?: string;

  @ApiPropertyOptional({
    description: 'Additional notes or remarks.',
    example: 'Required additional materials, which arrived on time.',
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiPropertyOptional({
    description: 'An array of URLs to related photos.',
    type: [String],
    example: ['https://example.com/photo1.jpg', 'https://example.com/photo2.jpg'],
  })
  @IsArray()
  @IsUrl({}, { each: true })
  @IsOptional()
  photo_urls?: string[];

  @ApiPropertyOptional({
    description: 'ID of the related project.',
    example: 'clx..._project_id_...',
  })
  @IsUUID()
  @IsOptional()
  project_id?: string;

  @ApiPropertyOptional({
    description: 'ID of the related task.',
    example: 'clx..._task_id_...',
  })
  @IsUUID()
  @IsOptional()
  task_id?: string;

  @ApiPropertyOptional({
    description: 'The timestamp when the progress was recorded. Defaults to now if not provided.',
    type: 'string',
    format: 'date-time',
    example: '2024-05-31T10:00:00.000Z',
  })
  @IsOptional()
  @IsString()
  recorded_at?: string | Date;

  @ApiPropertyOptional({
    description: 'Any additional metadata associated with the entry.',
    type: 'object',
    additionalProperties: true,
    example: { custom_field: 'custom_value' },
  })
  @IsOptional()
  metadata?: any;
}
