import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ProgressType } from '@prisma/client';

export class ProgressEntryResponseDto {
  @ApiProperty({
    description: '進度條目 ID',
    example: 'clxxxxx',
  })
  id: string;

  @ApiProperty({
    description: '進度條目標題',
    example: '完成前端頁面設計',
  })
  title: string;

  @ApiPropertyOptional({
    description: '進度條目描述',
    example: '完成了用戶登錄頁面和主頁面的設計',
  })
  description?: string;

  @ApiProperty({
    description: '進度類型',
    enum: ProgressType,
    example: ProgressType.TASK_UPDATE,
  })
  progress_type: ProgressType;

  @ApiPropertyOptional({
    description: '進度百分比 (0-100)',
    example: 75,
  })
  progress_value?: number;

  @ApiPropertyOptional({
    description: '狀態更新',
    example: 'in-progress',
  })
  status?: string;

  @ApiPropertyOptional({
    description: '備註',
    example: '需要進一步優化響應式設計',
  })
  notes?: string;

  @ApiPropertyOptional({
    description: '相關照片 URLs',
    type: [String],
    example: ['https://example.com/photo1.jpg', 'https://example.com/photo2.jpg'],
  })
  photoUrls?: string[];

  @ApiPropertyOptional({
    description: '關聯的項目 ID',
    example: 'clxxxxx',
  })
  projectId?: string;

  @ApiPropertyOptional({
    description: '關聯的任務 ID',
    example: 'clxxxxx',
  })
  taskId?: string;

  @ApiProperty({
    description: '記錄者用戶 ID',
    example: 'clxxxxx',
  })
  userId: string;

  @ApiProperty({
    description: '租戶 ID',
    example: 'clxxxxx',
  })
  tenantId: string;

  @ApiProperty({
    description: '實際發生時間',
    example: '2024-06-07T10:30:00Z',
  })
  recorded_at: Date;

  @ApiProperty({
    description: '建立時間',
    example: '2024-06-07T10:30:00Z',
  })
  created_at: Date;

  @ApiProperty({
    description: '更新時間',
    example: '2024-06-07T10:30:00Z',
  })
  updated_at: Date;

  @ApiPropertyOptional({
    description: '額外的元數據',
  })
  metadata?: any;

  @ApiPropertyOptional({
    description: '關聯的項目信息',
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      status: { type: 'string' },
    },
  })
  project?: {
    id: string;
    name: string;
    status: string;
  };

  @ApiPropertyOptional({
    description: '關聯的任務信息',
    type: 'object',
    properties: {
      id: { type: 'string' },
      title: { type: 'string' },
      status: { type: 'string' },
    },
  })
  task?: {
    id: string;
    title: string;
    status: string;
  };
}
