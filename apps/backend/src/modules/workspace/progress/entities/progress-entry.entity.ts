import { progress_entries, ProgressType } from '@prisma/client';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';

export class ProgressEntryEntity implements progress_entries {
  @ApiProperty({
    description: 'The unique identifier for the progress entry.',
    example: 'clx..._progress_id_...',
  })
  id: string;

  @ApiProperty({
    description: 'The title of the progress entry.',
    example: 'Foundation pouring complete',
  })
  title: string;

  @ApiPropertyOptional({
    description: 'The description of the progress entry.',
    example: 'Completed the steel frame structure for the first floor.',
  })
  description: string | null;

  @ApiProperty({
    enum: ProgressType,
    description: 'The type of progress.',
    example: ProgressType.TASK_UPDATE,
  })
  progress_type: ProgressType;

  @ApiPropertyOptional({
    description: 'A numerical value representing progress, e.g., percentage.',
    example: 75,
  })
  progress_value: number | null;

  @ApiPropertyOptional({
    description: 'The status of the task or project, if applicable.',
    example: 'In Progress',
  })
  status: string | null;

  @ApiPropertyOptional({
    description: 'Additional notes or comments.',
    example: 'Encountered a delay due to weather.',
  })
  notes: string | null;

  @ApiPropertyOptional({
    description: 'A list of URLs to photos associated with the progress entry.',
    type: [String],
    example: ['https://example.com/photo1.jpg'],
  })
  photo_urls: string[];

  @ApiPropertyOptional({
    description: 'Any additional metadata associated with the entry.',
    type: 'object',
    example: { custom_field: 'custom_value' },
    additionalProperties: true,
  })
  metadata: any; // Prisma.JsonValue is 'any' in practice

  @ApiProperty({
    description: 'ID of the related project.',
    example: 'clx..._project_id_...',
  })
  project_id: string | null;

  @ApiProperty({
    description: 'ID of the related task.',
    example: 'clx..._task_id_...',
  })
  task_id: string | null;

  @ApiProperty({
    description: 'ID of the user who recorded the entry.',
    example: 'clx..._user_id_...',
  })
  user_id: string;

  @Exclude()
  tenant_id: string;

  @ApiProperty({
    description: 'The timestamp when the progress was recorded.',
  })
  recorded_at: Date;

  @ApiProperty({
    description: 'The timestamp when the entry was created.',
  })
  created_at: Date;

  @ApiProperty({
    description: 'The timestamp when the entry was last updated.',
  })
  updated_at: Date;

  constructor(partial: Partial<ProgressEntryEntity>) {
    Object.assign(this, partial);
  }
}
