import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { RolesGuard } from '../../core/auth/guards/roles.guard';
import { Roles } from '../../core/auth/decorators/roles.decorator';
import { Role } from '../../../common/enums/role.enum';
import { ProgressService } from './progress.service';
import {
  CreateProgressEntryDto,
  UpdateProgressEntryDto,
  ProgressEntryResponseDto,
  CreateMilestoneDto,
  UpdateMilestoneDto,
  MilestoneResponseDto,
  ProgressReportResponseDto,
} from './dto';
import { ProgressType, MilestoneStatus, ReportType } from '@prisma/client';
import { ProgressEntryEntity } from './entities/progress-entry.entity';
// import { PoliciesGuard } from '@/casl/guards/policies.guard';
// import { CheckPolicies } from '@/casl/decorators/check-policies.decorator';
// import { AppAbility } from '@/casl/casl-ability.factory';

@ApiTags('Progress Tracking')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('workspace/progress')
export class ProgressController {
  constructor(private readonly progressService: ProgressService) {}

  // ==================== 進度條目管理 ====================

  @Post('entries')
  @Roles(Role.TENANT_ADMIN, Role.TENANT_MANAGER, Role.TENANT_USER)
  @ApiOperation({ summary: '建立進度條目' })
  @ApiResponse({
    status: 201,
    description: '進度條目建立成功',
    type: ProgressEntryResponseDto,
  })
  async createProgressEntry(
    @Body() createProgressEntryDto: CreateProgressEntryDto,
    @Request() req: any,
  ): Promise<ProgressEntryResponseDto> {
    return this.progressService.createProgressEntry(
      createProgressEntryDto,
      req.user.id,
      req.user.tenantId,
    );
  }

  @Get('entries')
  @Roles(Role.TENANT_ADMIN, Role.TENANT_MANAGER, Role.TENANT_USER)
  @ApiOperation({ summary: '獲取進度條目列表' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'projectId', required: false, type: String })
  @ApiQuery({ name: 'taskId', required: false, type: String })
  @ApiQuery({ name: 'progress_type', required: false, enum: ProgressType })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiResponse({
    status: 200,
    description: '進度條目列表獲取成功',
  })
  async findAllProgressEntries(
    @Request() req: any,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('projectId') projectId?: string,
    @Query('taskId') taskId?: string,
    @Query('progress_type') progress_type?: ProgressType,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.progressService.findAllProgressEntries(
      req.user.tenantId,
      page ? Number(page) : 1,
      limit ? Number(limit) : 10,
      projectId,
      taskId,
      progress_type,
      startDate,
      endDate,
    );
  }

  @Get('entries/:id')
  @Roles(Role.TENANT_ADMIN, Role.TENANT_MANAGER, Role.TENANT_USER)
  @ApiOperation({ summary: '獲取單個進度條目' })
  @ApiResponse({
    status: 200,
    description: '進度條目獲取成功',
    type: ProgressEntryResponseDto,
  })
  async findOneProgressEntry(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<ProgressEntryResponseDto> {
    return this.progressService.findOneProgressEntry(id, req.user.tenantId);
  }

  @Patch('entries/:id')
  @Roles(Role.TENANT_ADMIN, Role.TENANT_MANAGER, Role.TENANT_USER)
  @ApiOperation({ summary: '更新進度條目' })
  @ApiResponse({
    status: 200,
    description: '進度條目更新成功',
    type: ProgressEntryResponseDto,
  })
  async updateProgressEntry(
    @Param('id') id: string,
    @Body() updateProgressEntryDto: UpdateProgressEntryDto,
    @Request() req: any,
  ): Promise<ProgressEntryResponseDto> {
    return this.progressService.updateProgressEntry(id, updateProgressEntryDto, req.user.tenantId);
  }

  @Delete('entries/:id')
  @Roles(Role.TENANT_ADMIN, Role.TENANT_MANAGER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '刪除進度條目' })
  @ApiResponse({
    status: 204,
    description: '進度條目刪除成功',
  })
  async removeProgressEntry(@Param('id') id: string, @Request() req: any): Promise<void> {
    return this.progressService.removeProgressEntry(id, req.user.tenantId);
  }

  // ==================== 里程碑管理 ====================

  @Post('milestones')
  @Roles(Role.TENANT_ADMIN, Role.TENANT_MANAGER)
  @ApiOperation({ summary: '建立項目里程碑' })
  @ApiResponse({
    status: 201,
    description: '里程碑建立成功',
    type: MilestoneResponseDto,
  })
  async createMilestone(
    @Body() createMilestoneDto: CreateMilestoneDto,
    @Request() req: any,
  ): Promise<MilestoneResponseDto> {
    return this.progressService.createMilestone(createMilestoneDto, req.user.id, req.user.tenantId);
  }

  @Get('milestones')
  @Roles(Role.TENANT_ADMIN, Role.TENANT_MANAGER, Role.TENANT_USER)
  @ApiOperation({ summary: '獲取里程碑列表' })
  @ApiQuery({ name: 'projectId', required: false, type: String })
  @ApiQuery({ name: 'status', required: false, enum: MilestoneStatus })
  @ApiResponse({
    status: 200,
    description: '里程碑列表獲取成功',
  })
  async findAllMilestones(
    @Request() req: any,
    @Query('projectId') projectId?: string,
    @Query('status') status?: MilestoneStatus,
  ): Promise<MilestoneResponseDto[]> {
    return this.progressService.findAllMilestones(req.user.tenantId, projectId, status);
  }

  @Patch('milestones/:id')
  @Roles(Role.TENANT_ADMIN, Role.TENANT_MANAGER)
  @ApiOperation({ summary: '更新里程碑' })
  @ApiResponse({
    status: 200,
    description: '里程碑更新成功',
    type: MilestoneResponseDto,
  })
  async updateMilestone(
    @Param('id') id: string,
    @Body() updateMilestoneDto: UpdateMilestoneDto,
    @Request() req: any,
  ): Promise<MilestoneResponseDto> {
    return this.progressService.updateMilestone(id, updateMilestoneDto, req.user.tenantId);
  }

  // ==================== 進度報告 ====================

  @Post('reports/generate')
  @Roles(Role.TENANT_ADMIN, Role.TENANT_MANAGER)
  @ApiOperation({ summary: '生成進度報告' })
  @ApiQuery({ name: 'projectId', required: false, type: String })
  @ApiQuery({ name: 'reportType', required: true, enum: ReportType })
  @ApiResponse({
    status: 201,
    description: '進度報告生成成功',
    type: ProgressReportResponseDto,
  })
  async generateProgressReport(
    @Request() req: any,
    @Query('projectId') projectId?: string,
    @Query('reportType') reportType?: ReportType,
  ): Promise<ProgressReportResponseDto> {
    return this.progressService.generateProgressReport(
      req.user.tenantId,
      projectId || null,
      reportType || ReportType.WEEKLY,
      req.user.id,
    );
  }

  // ==================== 統計與分析 ====================

  @Get('stats/overview')
  @Roles(Role.TENANT_ADMIN, Role.TENANT_MANAGER, Role.TENANT_USER)
  @ApiOperation({ summary: '獲取進度統計概覽' })
  @ApiQuery({ name: 'projectId', required: false, type: String })
  @ApiResponse({
    status: 200,
    description: '進度統計獲取成功',
    schema: {
      type: 'object',
      properties: {
        summary: {
          type: 'object',
          properties: {
            totalProjects: { type: 'number' },
            totalTasks: { type: 'number' },
            totalProgressEntries: { type: 'number' },
            totalMilestones: { type: 'number' },
          },
        },
        progress: {
          type: 'object',
          properties: {
            averageProjectProgress: { type: 'number', nullable: true },
            completedTasks: { type: 'number' },
            inProgressTasks: { type: 'number' },
            pendingTasks: { type: 'number' },
            overdueTasks: { type: 'number' },
          },
        },
        milestones: {
          type: 'object',
          properties: {
            completed: { type: 'number' },
            pending: { type: 'number' },
            overdue: { type: 'number' },
          },
        },
        recentActivity: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              title: { type: 'string' },
              type: { type: 'string' },
              date: { type: 'string', format: 'date-time' },
              projectName: { type: 'string' },
              taskTitle: { type: 'string' },
            },
          },
        },
      },
    },
  })
  async getProgressOverview(@Request() req: any, @Query('projectId') projectId?: string) {
    return this.progressService.getProgressOverview(req.user.tenantId, projectId);
  }

  @Post('workspace/:workspaceId/progress')
  // @UseGuards(PoliciesGuard)
  // @CheckPolicies((ability: AppAbility) => ability.can('create', 'Progress'))
  @ApiOperation({ summary: 'Create a new progress entry' })
  @ApiResponse({
    status: 201,
    description: 'The progress entry has been successfully created.',
    type: ProgressEntryEntity,
  })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  async create(
    @Request() req,
    @Body() createProgressEntryDto: CreateProgressEntryDto,
  ): Promise<ProgressEntryEntity> {
    const { tenantId, id: userId } = req.user;
    return this.progressService.create(createProgressEntryDto, tenantId, userId);
  }
}
