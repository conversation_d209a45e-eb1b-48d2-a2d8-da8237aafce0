import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { AuditLogService, AuditLogData, AuditLogFilter } from '@/common/services/audit-log.service';
import { EncryptionService } from '@/modules/core/encryption/encryption.service';

describe('Audit System Integration Tests', () => {
  let auditLogService: AuditLogService;
  let prismaService: PrismaService;
  let encryptionService: EncryptionService;

  const mockPrismaService = {
    system_logs: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      groupBy: jest.fn(),
    },
    $queryRaw: jest.fn(),
    $executeRaw: jest.fn(),
  };

  const mockEncryptionService = {
    encrypt: jest.fn().mockImplementation((text) => `encrypted_${text}`),
    decrypt: jest.fn().mockImplementation((text) => text.replace('encrypted_', '')),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuditLogService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: EncryptionService,
          useValue: mockEncryptionService,
        },
      ],
    }).compile();

    auditLogService = module.get<AuditLogService>(AuditLogService);
    prismaService = module.get<PrismaService>(PrismaService);
    encryptionService = module.get<EncryptionService>(EncryptionService);

    jest.clearAllMocks();
  });

  describe('Encryption and Security', () => {
    it('should encrypt sensitive data when logging', async () => {
      const logData: AuditLogData = {
        tenant_id: 'tenant-123',
        user_id: 'user-456',
        action: 'LOGIN',
        message: 'User login successful',
        ip: '*************',
        details: { password: 'secret123', token: 'abc123' },
      };

      mockPrismaService.system_logs.create.mockResolvedValue({ id: 'log-123' });

      await auditLogService.log(logData);

      expect(mockEncryptionService.encrypt).toHaveBeenCalledWith('*************');
      expect(mockPrismaService.system_logs.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          tenant_id: 'tenant-123',
          user_id: 'user-456',
          action: 'LOGIN',
          message: 'User login successful',
          ip: 'encrypted_*************',
        }),
      });
    });

    it('should decrypt data when retrieving logs', async () => {
      const mockLogs = [
        {
          id: 'log-123',
          tenant_id: 'tenant-123',
          user_id: 'user-456',
          action: 'LOGIN',
          ip: 'encrypted_*************',
          details: 'encrypted_{"token":"abc123"}',
          created_at: new Date(),
        },
      ];

      mockPrismaService.system_logs.findMany.mockResolvedValue(mockLogs);
      mockPrismaService.system_logs.count.mockResolvedValue(1);

      const result = await auditLogService.findLogs({
        tenant_id: 'tenant-123',
        limit: 10,
        offset: 0,
      });

      expect(mockEncryptionService.decrypt).toHaveBeenCalledWith('encrypted_*************');
      expect(result.logs[0]).toEqual(
        expect.objectContaining({
          ip: '*************',
        }),
      );
    });
  });

  describe('Performance Testing', () => {
    it('should handle bulk audit log creation efficiently', async () => {
      const startTime = Date.now();
      const batchSize = 100;
      const promises: Promise<void>[] = [];

      mockPrismaService.system_logs.create.mockResolvedValue({ id: 'log-123' });

      for (let i = 0; i < batchSize; i++) {
        promises.push(
          auditLogService.log({
            tenant_id: 'tenant-123',
            user_id: `user-${i}`,
            action: 'TEST_ACTION',
            message: `Test message ${i}`,
            ip: '*************',
          }),
        );
      }

      await Promise.all(promises);
      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Should complete 100 logs in less than 1 second
      expect(executionTime).toBeLessThan(1000);
      expect(mockPrismaService.system_logs.create).toHaveBeenCalledTimes(batchSize);
    });

    it('should efficiently query logs with filters', async () => {
      const mockQueryResult = Array.from({ length: 50 }, (_, i) => ({
        id: `log-${i}`,
        tenant_id: 'tenant-123',
        user_id: `user-${i}`,
        action: 'TEST_ACTION',
        created_at: new Date(),
      }));

      mockPrismaService.system_logs.findMany.mockResolvedValue(mockQueryResult);
      mockPrismaService.system_logs.count.mockResolvedValue(500);

      const startTime = Date.now();

      const result = await auditLogService.findLogs({
        tenant_id: 'tenant-123',
        user_id: 'user-123',
        action: 'LOGIN',
        start_date: new Date('2023-01-01'),
        end_date: new Date('2023-12-31'),
        limit: 50,
        offset: 0,
      });

      const endTime = Date.now();
      const queryTime = endTime - startTime;

      // Query should complete quickly (under 100ms for mocked data)
      expect(queryTime).toBeLessThan(100);
      expect(result.logs).toHaveLength(50);
      expect(result.total).toBe(500);
    });
  });

  describe('Audit Stats and Analytics', () => {
    it('should generate comprehensive audit statistics', async () => {
      const mockStats = {
        totalLogs: [{ _count: 1000 }],
        actionStats: [
          { action: 'LOGIN', _count: 300 },
          { action: 'LOGOUT', _count: 250 },
          { action: 'CREATE_USER', _count: 150 },
        ],
        userStats: [
          { user_id: 'user-1', _count: 50 },
          { user_id: 'user-2', _count: 45 },
        ],
        dailyStats: [
          { date: '2023-12-01', _count: 100 },
          { date: '2023-12-02', _count: 120 },
        ],
      };

      mockPrismaService.system_logs.count.mockResolvedValue(1000);
      mockPrismaService.system_logs.groupBy
        .mockResolvedValueOnce(mockStats.actionStats)
        .mockResolvedValueOnce(mockStats.userStats)
        .mockResolvedValueOnce(mockStats.dailyStats);

      const result = await auditLogService.getAuditStats(
        'tenant-123',
        new Date('2023-12-01'),
        new Date('2023-12-31'),
      );

      expect(result).toEqual({
        totalLogs: 1000,
        logsByAction: {
          LOGIN: 300,
          LOGOUT: 250,
          CREATE_USER: 150,
        },
        logsByStatus: expect.any(Object),
        logsByResource: expect.any(Object),
      });
    });
  });

  describe('Data Integrity and Error Handling', () => {
    it('should handle encryption failures gracefully', async () => {
      mockEncryptionService.encrypt.mockImplementationOnce(() => {
        throw new Error('Encryption failed');
      });

      const logData: AuditLogData = {
        tenant_id: 'tenant-123',
        user_id: 'user-456',
        action: 'LOGIN',
        ip: '*************',
      };

      // Should not throw error, but log without encryption
      await expect(auditLogService.log(logData)).resolves.not.toThrow();

      expect(mockPrismaService.system_logs.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          ip: '*************', // Falls back to original value when encryption fails
        }),
      });
    });

    it('should handle decryption failures gracefully', async () => {
      mockEncryptionService.decrypt.mockImplementationOnce(() => {
        throw new Error('Decryption failed');
      });

      const mockLogs = [
        {
          id: 'log-123',
          tenant_id: 'tenant-123',
          ip: 'encrypted_*************',
          created_at: new Date(),
        },
      ];

      mockPrismaService.system_logs.findMany.mockResolvedValue(mockLogs);
      mockPrismaService.system_logs.count.mockResolvedValue(1);

      const result = await auditLogService.findLogs({
        tenant_id: 'tenant-123',
        limit: 10,
        offset: 0,
      });

      // Should return original encrypted value if decryption fails
      expect(result.logs[0].ip).toBe('encrypted_*************');
    });

    it('should sanitize sensitive data automatically when logging', async () => {
      const logData: AuditLogData = {
        action: 'LOGIN',
        tenant_id: 'tenant-123',
        user_id: 'user-456',
        details: {
          username: 'testuser',
          password: 'secret123',
          token: 'jwt-token-here',
          normalData: 'this is fine',
        },
      };

      mockPrismaService.system_logs.create.mockResolvedValue({ id: 'log-123' });

      await auditLogService.log(logData);

      expect(mockPrismaService.system_logs.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          action: 'LOGIN',
          tenant_id: 'tenant-123',
          user_id: 'user-456',
        }),
      });
    });
  });

  describe('Search and Filtering', () => {
    it('should support complex search queries', async () => {
      mockPrismaService.system_logs.findMany.mockResolvedValue([]);
      mockPrismaService.system_logs.count.mockResolvedValue(0);

      await auditLogService.findLogs({
        tenant_id: 'tenant-123',
        search_query: 'login failed',
        user_id: 'user-123',
        action: 'LOGIN',
        status: 'ERROR',
        start_date: new Date('2023-01-01'),
        end_date: new Date('2023-12-31'),
        limit: 25,
        offset: 25,
      });

      expect(mockPrismaService.system_logs.findMany).toHaveBeenCalled();
    });
  });
});
