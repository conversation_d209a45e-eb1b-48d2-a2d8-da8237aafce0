@import './variables.css';
@import './fullcalendar.css';
@import './utilities.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* HorizAI 專用語意化色彩 - 基於 shadcn/ui 標準 */
    /* 功能色調 - 擴展 shadcn/ui 標準 */
    --info: 221.2 83.2% 53.3%; /* 使用 primary 色調 */
    --info-foreground: 210 40% 98%;
    --success: 142.1 76.2% 36.3%; /* 綠色 */
    --success-foreground: 210 40% 98%;
    --warning: 32.2 95% 44%; /* 橙色 */
    --warning-foreground: 210 40% 98%;
    --error: 0 84.2% 60.2%; /* 使用 destructive 色調 */
    --error-foreground: 210 40% 98%;

    /* 圖表顏色 - 優化版本 */
    --chart-1: 142.1 76.2% 36.3%; /* Success Green */
    --chart-2: 221.2 83.2% 53.3%; /* Primary Blue */
    --chart-3: 32.2 95% 44%; /* Warning Orange */
    --chart-4: 0 84.2% 60.2%; /* Error Red */
    --chart-5: 271.5 81.3% 55.9%; /* Purple */

    /* Sidebar 色彩 - 基於 shadcn/ui 標準 */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 221.2 83.2% 53.3%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 221.2 83.2% 53.3%;
  }

  .dark {
    /* HorizAI 專用語意化色彩 - 深色模式 */
    /* 功能色調 - 深色模式版本 */
    --info: 217.2 91.2% 59.8%; /* 使用 primary 深色版本 */
    --info-foreground: 222.2 47.4% 11.2%;
    --success: 142.1 76.2% 36.3%; /* 保持綠色 */
    --success-foreground: 210 40% 98%;
    --warning: 32.2 95% 44%; /* 保持橙色 */
    --warning-foreground: 210 40% 98%;
    --error: 0 62.8% 30.6%; /* 使用 destructive 深色版本 */
    --error-foreground: 210 40% 98%;

    /* 圖表顏色 - 深色模式版本 */
    --chart-1: 142.1 76.2% 45%; /* Success Green - 稍微亮一點 */
    --chart-2: 217.2 91.2% 59.8%; /* Primary Blue - 深色版本 */
    --chart-3: 32.2 95% 55%; /* Warning Orange - 稍微亮一點 */
    --chart-4: 0 62.8% 50%; /* Error Red - 稍微亮一點 */
    --chart-5: 271.5 81.3% 65%; /* Purple - 稍微亮一點 */

    /* Sidebar 色彩 - 深色模式 */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 222.2 47.4% 11.2%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* macOS 風格捲軸 */
  ::-webkit-scrollbar {
    @apply w-[8px] h-[8px];
  }

  /* 隱藏捲軸軌道 */
  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  /* 捲軸滑塊樣式 */
  ::-webkit-scrollbar-thumb {
    @apply bg-zinc-400/40 hover:bg-zinc-400/60 active:bg-zinc-400/80
           dark:bg-zinc-500/40 dark:hover:bg-zinc-500/60 dark:active:bg-zinc-500/80
           rounded-full transition-colors;
  }

  /* 初始隱藏捲軸 */
  ::-webkit-scrollbar-thumb {
    visibility: hidden;
  }

  /* 滾動時顯示捲軸 */
  *:hover::-webkit-scrollbar-thumb,
  *:focus::-webkit-scrollbar-thumb,
  *:focus-within::-webkit-scrollbar-thumb {
    visibility: visible;
  }

  /* Firefox 捲軸樣式 */
  * {
    scrollbar-width: thin;
    scrollbar-color: transparent transparent;
  }

  *:hover,
  *:focus,
  *:focus-within {
    scrollbar-color: rgba(161, 161, 170, 0.4) transparent;
  }

  .dark *:hover,
  .dark *:focus,
  .dark *:focus-within {
    scrollbar-color: rgba(113, 113, 122, 0.4) transparent;
  }
}