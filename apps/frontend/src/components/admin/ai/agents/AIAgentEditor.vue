<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Textarea } from '@/components/ui/textarea';
import { Copy, Trash2, Sparkles, Link, Settings, Zap } from 'lucide-vue-next';
// 已移除舊的 AIBot import，現在使用 AIAgent
import { computed, watch } from 'vue';
import SystemPromptInput from '@/components/admin/ai/shared/SystemPromptInput.vue';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { RouterLink } from 'vue-router';
import { useAuthStore } from '@horizai/auth';
import type { AiBotProviderType } from '@/types/models/ai.model';
import {
  AgentResponseFormat as EditorResponseFormat,
  type AIAgent,
} from '@/types/models/ai-agent.model';
import { useNotification } from '@/composables/shared/useNotification';
import { useUtils } from '@/composables/shared/useUtils';
import { useAIAgentEditor } from '@/composables/admin/ai/agents/useAIAgentEditor';
import { useAIValidation } from '@/composables/admin/ai/shared/useAIValidation';
import { Separator } from '@/components/ui/separator';

const props = defineProps<{
  bot: AIAgent | null;
  editData: AIAgent;
  isEditing: boolean;
  availableModels: { id: string; name: string; is_enabled?: boolean }[];
  botScenes: { value: string; label: string; description?: string }[];
  canEditCoreProps?: boolean;
  canDelete?: boolean;
  scopeDescription: string;
  isSystemBot: boolean;
  systemFeatureInfo: { name: string } | null;
  systemFeatures: { key: string; name: string }[];
  updateSystemFeature: (val: string) => void;
  updateEditData: (key: string, value: any) => void;
  responseFormats: { value: string; label: string }[];
  availableKeys?: {
    id: string;
    name: string;
    api_key?: string;
    provider: string;
    api_url?: string | null;
    is_enabled: boolean;
    created_at?: string;
    updated_at?: string;
    last_test?: string | null;
  }[];
}>();

const emit = defineEmits<{
  'confirm-delete': [];
  'update:editData': [data: AIAgent];
  'duplicate-agent': [];
  'update:isDirty': [value: boolean];
}>();

const authStore = useAuthStore();
const notification = useNotification();
const { copyToClipboard } = useUtils();
const {
  nameInput,
  getActiveModels,
  getLastUpdatedAt,
  getSelectedKeyDetails,
  createFieldUpdateHandler,
  createSystemFeatureSelectHandler,
  focusNameInput,
} = useAIAgentEditor();

// AI 金鑰驗證邏輯
const availableKeysRef = computed(() => props.availableKeys || []);
const selectedKeyIdRef = computed(() => props.editData.key_id);

const {
  hasAnyValidKey,
  hasValidSelectedKey,
  validKeyCount,
  availableProviders,
  validateBotSavePrerequisites,
  showKeySetupNotification,
  getOperationReadiness,
} = useAIValidation(availableKeysRef, selectedKeyIdRef);

// 檢查金鑰配置狀態
const keyConfigStatus = computed(() => getOperationReadiness('save', props.editData));
const canSaveBot = computed(() => keyConfigStatus.value.isReady);

// 執行類型選項
const executionTypes = [
  { value: 'tools', label: '工具型 Agent', description: '使用預定義工具執行任務' },
  { value: 'graph', label: '圖譜型 Agent', description: '使用複雜工作流程執行任務' },
];

// 過濾激活模型列表
const activeModels = computed(() => getActiveModels(props.availableModels));

// 最後更新時間
const lastUpdatedAt = getLastUpdatedAt(props.bot);

// 選中金鑰詳細資訊
const selectedKeyDetails = getSelectedKeyDetails(props.editData, props.availableKeys);

// 處理欄位更新
const handleFieldUpdate = createFieldUpdateHandler<AIAgent>(props.updateEditData);

// 處理場景變更，自動填入描述
const handleSceneChange = (newScene: string) => {
  // 更新場景
  handleFieldUpdate('scene', newScene);

  // 如果當前描述為空，自動填入場景對應的描述
  if (!props.editData.description?.trim()) {
    const sceneInfo = props.botScenes.find((s) => s.value === newScene);
    if (sceneInfo && 'description' in sceneInfo && sceneInfo.description) {
      handleFieldUpdate('description', sceneInfo.description);
    }
  }
};

// 處理執行類型變更
const handleExecutionTypeChange = (newType: string) => {
  handleFieldUpdate('execution_type', newType);
};

// 處理系統功能選擇
const handleSystemFeatureSelect = createSystemFeatureSelectHandler(
  props.systemFeatures,
  props.updateSystemFeature,
  props.updateEditData,
);

// 暴露 focus 方法給父組件
defineExpose({
  focus: focusNameInput,
});
</script>

<template>
  <div class="flex-1 overflow-auto w-full">
    <div v-if="bot" class="space-y-6 max-w-4xl mx-auto p-6 pb-20">
      <!-- 基本資訊 -->
      <div
        v-if="bot.scope !== 'WORKSPACE'"
        class="mb-4 p-4 bg-card border border-border rounded-lg"
      >
        <div class="flex flex-col gap-1.5">
          <div class="flex items-center flex-wrap gap-2">
            <Badge variant="default" class="text-xs px-2 py-0.5">系統 Agent</Badge>
            <Badge variant="secondary" class="text-xs px-2 py-0.5">系統功能專用</Badge>
            <span
              class="text-sm text-muted-foreground font-normal truncate"
              :title="scopeDescription"
            >
              {{ scopeDescription }}
            </span>
          </div>
          <div
            v-if="isSystemBot && systemFeatureInfo"
            class="flex items-center gap-1.5 text-xs text-muted-foreground"
          >
            <Link class="h-3.5 w-3.5 text-primary shrink-0" />
            <p class="leading-snug">
              <span>綁定系統功能：</span>
              <RouterLink
                :to="{
                  path: '/admin/ai-settings',
                  query: { highlight_feature: editData.scene },
                }"
                class="text-primary hover:underline font-medium"
                :title="`前往 ${systemFeatureInfo.name} 功能設定`"
              >
                {{ systemFeatureInfo.name }}
              </RouterLink>
            </p>
          </div>
        </div>
      </div>

      <div class="space-y-6">
        <!-- 系統功能選擇 (僅系統 Agent 顯示) -->
        <div v-if="isSystemBot" class="space-y-2">
          <Label>系統功能</Label>
          <Select
            :model-value="editData.scene"
            @update:model-value="(val) => handleSystemFeatureSelect(val as string | undefined)"
            :disabled="true"
          >
            <SelectTrigger class="w-full">
              <SelectValue placeholder="選擇系統功能">
                {{
                  systemFeatures.find((f) => f.key === editData.scene)?.name || editData.name || ''
                }}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem v-for="feature in systemFeatures" :key="feature.key" :value="feature.key">
                {{ feature.name }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- Agent 名稱 -->
        <div class="space-y-2">
          <Label for="name">Agent 名稱 *</Label>
          <Input
            id="name"
            ref="nameInput"
            :model-value="editData.name"
            @update:model-value="(val) => handleFieldUpdate('name', val)"
            placeholder="輸入 Agent 名稱"
            :disabled="!canEditCoreProps"
          />
        </div>

        <!-- Agent 描述 -->
        <div class="space-y-2">
          <Label for="description">Agent 描述</Label>
          <Textarea
            id="description"
            :model-value="editData.description"
            @update:model-value="(val) => handleFieldUpdate('description', val)"
            placeholder="描述此 Agent 的用途和功能"
            :rows="3"
            :disabled="!canEditCoreProps"
          />
        </div>

        <Separator />

        <!-- 執行配置 -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium flex items-center gap-2">
            <Settings class="h-5 w-5" />
            執行配置
          </h3>

          <!-- 執行類型選擇 -->
          <div class="space-y-3">
            <Label>執行類型</Label>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div
                v-for="type in executionTypes"
                :key="type.value"
                class="border rounded-lg p-4 cursor-pointer transition-colors hover:bg-muted/50"
                :class="{
                  'border-primary bg-primary/5': editData.execution_type === type.value,
                  'border-border': editData.execution_type !== type.value,
                }"
                @click="handleExecutionTypeChange(type.value)"
              >
                <div class="flex items-start gap-3">
                  <div
                    class="w-4 h-4 rounded-full border-2 mt-0.5 transition-colors"
                    :class="{
                      'border-primary bg-primary': editData.execution_type === type.value,
                      'border-muted-foreground': editData.execution_type !== type.value,
                    }"
                  >
                    <div
                      v-if="editData.execution_type === type.value"
                      class="w-2 h-2 bg-white rounded-full m-0.5"
                    ></div>
                  </div>
                  <div class="flex-1">
                    <div class="font-medium">{{ type.label }}</div>
                    <div class="text-sm text-muted-foreground mt-1">
                      {{ type.description }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 工具配置 (工具型 Agent) -->
          <div v-if="editData.execution_type === 'tools'" class="space-y-3">
            <Label>可用工具</Label>
            <div class="border rounded-lg p-4">
              <p class="text-sm text-muted-foreground mb-3">選擇此 Agent 可以使用的工具</p>
              <!-- 這裡將來會添加工具選擇界面 -->
              <div class="text-center text-muted-foreground py-8">工具選擇界面待實現</div>
            </div>
          </div>

          <!-- 圖譜配置 (圖譜型 Agent) -->
          <div v-if="editData.execution_type === 'graph'" class="space-y-3">
            <Label>工作流程圖譜</Label>
            <div class="border rounded-lg p-4">
              <p class="text-sm text-muted-foreground mb-3">選擇或自定義工作流程圖譜</p>
              <!-- 這裡將來會添加圖譜選擇界面 -->
              <div class="text-center text-muted-foreground py-8">圖譜選擇界面待實現</div>
            </div>
          </div>
        </div>

        <Separator />

        <!-- 大腦配置 -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium flex items-center gap-2">
            <Zap class="h-5 w-5" />
            大腦配置
          </h3>

          <!-- AI 模型選擇 -->
          <div class="space-y-2">
            <Label for="model">AI 模型 *</Label>
            <Select
              :model-value="editData.model_id"
              @update:model-value="(val) => handleFieldUpdate('model_id', val)"
            >
              <SelectTrigger>
                <SelectValue placeholder="選擇 AI 模型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="model in activeModels" :key="model.id" :value="model.id">
                  {{ model.name }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- API 金鑰選擇 -->
          <div class="space-y-2">
            <Label for="key">API 金鑰 *</Label>
            <Select
              :model-value="editData.key_id"
              @update:model-value="(val) => handleFieldUpdate('key_id', val)"
            >
              <SelectTrigger>
                <SelectValue placeholder="選擇 API 金鑰" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="key in availableKeys"
                  :key="key.id"
                  :value="key.id"
                  :disabled="!key.is_enabled"
                >
                  {{ key.name }}
                  <Badge v-if="!key.is_enabled" variant="secondary" class="ml-2"> 已停用 </Badge>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 系統提示詞 -->
          <div class="space-y-2">
            <Label for="system-prompt">系統提示詞</Label>
            <SystemPromptInput
              :model-value="editData.system_prompt || ''"
              @update:model-value="(val) => handleFieldUpdate('system_prompt', val)"
              placeholder="定義 Agent 的角色、行為和指令..."
            />
          </div>

          <!-- 參數設定 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 溫度 -->
            <div class="space-y-2">
              <Label for="temperature">
                溫度 (Temperature): {{ editData.temperature || 0.7 }}
              </Label>
              <Slider
                :model-value="[editData.temperature || 0.7]"
                @update:model-value="(val) => handleFieldUpdate('temperature', val[0])"
                :min="0"
                :max="2"
                :step="0.1"
              />
            </div>

            <!-- 最大 Tokens -->
            <div class="space-y-2">
              <Label for="max-tokens">最大 Tokens</Label>
              <Input
                id="max-tokens"
                type="number"
                :model-value="editData.max_tokens || 2048"
                @update:model-value="(val) => handleFieldUpdate('max_tokens', parseInt(val))"
                :min="1"
                :max="32000"
              />
            </div>
          </div>

          <!-- 回應格式 -->
          <div class="space-y-2">
            <Label for="response-format">回應格式</Label>
            <Select
              :model-value="editData.response_format"
              @update:model-value="(val) => handleFieldUpdate('response_format', val)"
            >
              <SelectTrigger>
                <SelectValue placeholder="選擇回應格式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="format in responseFormats"
                  :key="format.value"
                  :value="format.value"
                >
                  {{ format.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- 操作區域 -->
        <div class="flex items-center justify-between pt-6 border-t">
          <div class="flex items-center gap-2">
            <Button
              v-if="canDelete && isEditing"
              variant="destructive"
              @click="emit('confirm-delete')"
            >
              <Trash2 class="h-4 w-4 mr-2" />
              刪除 Agent
            </Button>
            <Button v-if="isEditing" variant="outline" @click="emit('duplicate-agent')">
              <Copy class="h-4 w-4 mr-2" />
              複製 Agent
            </Button>
          </div>

          <div v-if="lastUpdatedAt" class="text-xs text-muted-foreground">
            最後更新：{{ lastUpdatedAt }}
          </div>
        </div>
      </div>
    </div>
    <div v-else class="flex items-center justify-center h-full">
      <p class="text-muted-foreground">沒有可編輯的 AI 助理資料。</p>
    </div>
  </div>
</template>

<style scoped>
.transition-colors {
  transition: all 0.2s ease;
}
</style>
