<script setup lang="ts">
import { Bot, Search, Plus, X, ChevronDown } from 'lucide-vue-next';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import type { AIAgent } from '@/types/models/ai-agent.model';
import { DEFAULT_AGENT_SCENES } from '@/types/models/ai-agent.model';
import { ref, computed, watch } from 'vue';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import type { AiSystemFeatureDefinition } from '@/types/models/ai.model';

const props = defineProps<{
  agentList: AIAgent[];
  selectedAgentId: string;
  isLoading: boolean;
  scope?: string;
  systemFeatureDefinitions?: AiSystemFeatureDefinition[];
}>();

const emit = defineEmits<{
  select: [id: string];
  create: [scope: string];
}>();

// 系統功能列表
const systemFeatures = computed(() => {
  if (!props.systemFeatureDefinitions) return [];
  return props.systemFeatureDefinitions.filter((feature) => feature.is_system_level);
});

// 取得系統功能的 Agent
const getSystemFeatureAgent = (feature: { key: string; name: string }) => {
  return props.agentList.find(
    (agent) =>
      agent.scope === 'SYSTEM' && (agent.scene === feature.key || agent.name.includes(feature.name)),
  );
};

// 過濾後的 Agent 列表
const filteredAgentList = computed(() => {
  if (!searchQuery.value.trim()) {
    if (props.scope === 'SYSTEM') {
      const systemFeatureAgents = systemFeatures.value
        .map((feature) =>
          props.agentList.find(
            (agent) =>
              agent.scope === 'SYSTEM' &&
              (agent.scene === feature.key || agent.name.includes(feature.name)),
          ),
        )
        .filter(Boolean) as AIAgent[];

      const otherSystemAgents = props.agentList.filter(
        (agent) =>
          agent.scope === 'SYSTEM' &&
          !systemFeatures.value.some((f) => agent.scene === f.key || agent.name.includes(f.name)),
      );
      return [...systemFeatureAgents, ...otherSystemAgents].sort((a, b) =>
        a.name.localeCompare(b.name),
      );
    }
    return props.agentList
      .filter((agent) => agent.scope === props.scope || props.scope === 'all')
      .sort((a, b) => a.name.localeCompare(b.name));
  }

  const query = searchQuery.value.toLowerCase();
  let filtered = props.agentList;

  if (props.scope !== 'all') {
    filtered = filtered.filter((agent) => agent.scope === props.scope);
  }

  return filtered
    .filter(
      (agent) =>
        agent.name.toLowerCase().includes(query) ||
        (agent.description && agent.description.toLowerCase().includes(query)),
    )
    .sort((a, b) => a.name.localeCompare(b.name));
});

// 監聽 scope 變化，自動選擇第一個系統功能的 Agent
watch(
  () => props.scope,
  (newScope) => {
    if (newScope === 'SYSTEM') {
      // 找到第一個系統功能的 Agent
      const firstSystemFeature = systemFeatures.value[0];
      if (firstSystemFeature) {
        const systemAgent = getSystemFeatureAgent(firstSystemFeature);
        if (systemAgent) {
          emit('select', systemAgent.id);
        }
      }
    } else if (newScope && newScope !== 'all') {
      const firstAgentInScope = props.agentList.find((agent) => agent.scope === newScope);
      if (firstAgentInScope) {
        emit('select', firstAgentInScope.id);
      }
    }
  },
);

const searchQuery = ref('');

const getSceneLabel = (scene: string): string => {
  const found = DEFAULT_AGENT_SCENES.find((s) => s.value === scene);
  return found ? found.label : scene;
};

const selectAgent = (id: string) => {
  emit('select', id);
};

const createAgent = (scope: string) => {
  emit('create', scope);
};

// 取得範圍標籤的顏色
const getScopeBadgeVariant = (scope: string) => {
  switch (scope) {
    case 'SYSTEM':
      return 'default';
    case 'TENANT_TEMPLATE':
      return 'secondary';
    default:
      return 'outline';
  }
};

// 取得範圍標籤的文字
const getScopeLabel = (scope: string) => {
  switch (scope) {
    case 'SYSTEM':
      return '系統';
    case 'TENANT_TEMPLATE':
      return '租戶模板';
    case 'WORKSPACE':
      return '工作區';
    default:
      return scope;
  }
};
</script>

<template>
  <div class="flex flex-col h-full bg-background">
    <!-- 搜尋與新增按鈕 -->
    <div
      class="p-4 border-b border-border flex items-center gap-3 sticky top-0 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
    >
      <div class="relative flex-1 group">
        <Search
          class="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground transition-colors group-focus-within:text-primary"
        />
        <Input
          v-model="searchQuery"
          placeholder="搜尋 Agent..."
          class="pl-9 pr-9 h-10 transition-shadow focus-visible:ring-primary"
        />
        <button
          v-if="searchQuery"
          class="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"
          @click="searchQuery = ''"
        >
          <X class="h-4 w-4" />
        </button>
      </div>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="icon" class="h-10 w-10 transition-colors">
            <Plus class="h-4 w-4" />
            <span class="sr-only">新增 Agent</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" class="w-56">
          <DropdownMenuLabel class="text-sm font-medium">系統類別</DropdownMenuLabel>
          <DropdownMenuItem @click="createAgent('SYSTEM')" class="gap-2">
            <Plus class="h-4 w-4" />
            <span>新增系統 Agent</span>
          </DropdownMenuItem>
          <DropdownMenuItem @click="createAgent('TENANT_TEMPLATE')" class="gap-2">
            <Plus class="h-4 w-4" />
            <span>新增租戶模板 Agent</span>
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuLabel class="text-sm font-medium">一般類別</DropdownMenuLabel>
          <DropdownMenuItem @click="createAgent('WORKSPACE')" class="gap-2">
            <Plus class="h-4 w-4" />
            <span>新增工作區 Agent</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>

    <!-- Agent 列表 -->
    <div class="flex-1 overflow-y-auto p-3 space-y-2">
      <!-- 系統功能列表 -->
      <template v-if="scope === 'SYSTEM' && !isLoading">
        <div class="mb-4">
          <h3 class="text-sm font-medium text-muted-foreground mb-2 px-1">系統功能專用 Agent</h3>
          <ul class="space-y-2">
            <li v-for="feature in systemFeatures" :key="feature.key" class="group relative">
              <div
                :class="[
                  'flex items-start gap-3 p-3 rounded-lg cursor-pointer transition-all',
                  'hover:bg-accent hover:shadow-sm',
                  selectedAgentId === getSystemFeatureAgent(feature)?.id
                    ? 'bg-primary/10 shadow-sm ring-1 ring-primary/20'
                    : '',
                ]"
                @click="getSystemFeatureAgent(feature) && selectAgent(getSystemFeatureAgent(feature)!.id)"
              >
                <div
                  :class="[
                    'p-2 rounded-lg transition-colors',
                    selectedAgentId === getSystemFeatureAgent(feature)?.id
                      ? 'bg-primary/20'
                      : 'bg-muted group-hover:bg-primary/10',
                  ]"
                >
                  <Bot
                    :class="[
                      'h-5 w-5 flex-shrink-0',
                      selectedAgentId === getSystemFeatureAgent(feature)?.id
                        ? 'text-primary'
                        : 'text-muted-foreground group-hover:text-primary',
                    ]"
                  />
                </div>

                <div class="min-w-0 flex-1 pt-1">
                  <div class="flex items-center gap-2 mb-1.5">
                    <span
                      :class="[
                        'font-medium truncate transition-colors',
                        selectedAgentId === getSystemFeatureAgent(feature)?.id ? 'text-primary' : '',
                      ]"
                      >{{ feature.name }}</span
                    >
                    <Badge variant="default" class="text-[10px] px-1.5 h-4"> 系統 </Badge>
                  </div>
                  <p class="text-sm text-muted-foreground truncate leading-relaxed">
                    {{ getSystemFeatureAgent(feature)?.description || '系統功能專用 Agent' }}
                  </p>
                </div>
              </div>
            </li>
          </ul>
        </div>

        <!-- 其他系統 Agent -->
        <div
          v-if="
            filteredAgentList.some(
              (agent) =>
                agent.scope === 'SYSTEM' &&
                !systemFeatures.some((f) => agent.scene === f.key || agent.name.includes(f.name)),
            )
          "
        >
          <h3 class="text-sm font-medium text-muted-foreground mb-2 px-1">其他系統 Agent</h3>
          <ul class="space-y-2">
            <li
              v-for="agent in filteredAgentList.filter(
                (a) =>
                  a.scope === 'SYSTEM' &&
                  !systemFeatures.some((f) => a.scene === f.key || a.name.includes(f.name)),
              )"
              :key="agent.id"
              class="group relative"
            >
              <!-- 使用現有的 Agent 項目模板 -->
              <div
                :class="[
                  'flex items-start gap-3 p-3 rounded-lg cursor-pointer transition-all',
                  'hover:bg-accent hover:shadow-sm',
                  selectedAgentId === agent.id ? 'bg-primary/10 shadow-sm ring-1 ring-primary/20' : '',
                ]"
                @click="selectAgent(agent.id)"
              >
                <div
                  :class="[
                    'p-2 rounded-lg transition-colors',
                    selectedAgentId === agent.id
                      ? 'bg-primary/20'
                      : 'bg-muted group-hover:bg-primary/10',
                  ]"
                >
                  <Bot
                    :class="[
                      'h-5 w-5 flex-shrink-0',
                      selectedAgentId === agent.id
                        ? 'text-primary'
                        : 'text-muted-foreground group-hover:text-primary',
                    ]"
                  />
                </div>

                <div class="min-w-0 flex-1 pt-1">
                  <div class="flex items-center gap-2 mb-1.5">
                    <span
                      :class="[
                        'font-medium truncate transition-colors',
                        selectedAgentId === agent.id ? 'text-primary' : '',
                      ]"
                      >{{ agent.name }}</span
                    >
                    <Badge variant="outline" class="text-[10px] px-1.5 h-4"> 系統 </Badge>
                  </div>
                  <p class="text-sm text-muted-foreground truncate leading-relaxed">
                    {{ agent.description || '無描述' }}
                  </p>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </template>

      <template v-if="isLoading">
        <div v-for="i in 5" :key="i" class="space-y-4">
          <div class="flex items-start gap-3 p-3">
            <Skeleton class="h-10 w-10 rounded-lg flex-shrink-0" />
            <div class="flex-1 space-y-2">
              <Skeleton class="h-5 w-[120px]" />
              <Skeleton class="h-4 w-[200px]" />
            </div>
          </div>
        </div>
      </template>

      <template v-else-if="agentList.length === 0">
        <div class="flex flex-col items-center justify-center h-[300px] text-muted-foreground">
          <div class="bg-muted/30 p-4 rounded-full mb-4">
            <Bot class="h-8 w-8" />
          </div>
          <span class="text-lg font-medium mb-1">尚無 AI Agent</span>
          <p class="text-sm text-muted-foreground">點擊右上角的 + 按鈕來新增 Agent</p>
        </div>
      </template>

      <template v-else>
        <!-- 非系統 Agent 列表 -->
        <div v-if="scope !== 'SYSTEM' && filteredAgentList.length > 0">
          <ul class="space-y-2">
            <li
              v-for="agent in filteredAgentList"
              :key="agent.id"
              @click="selectAgent(agent.id)"
              class="group relative"
            >
              <div
                :class="[
                  'flex items-start gap-3 p-3 rounded-lg cursor-pointer transition-all',
                  'hover:bg-accent hover:shadow-sm',
                  selectedAgentId === agent.id ? 'bg-primary/10 shadow-sm ring-1 ring-primary/20' : '',
                ]"
              >
                <div
                  :class="[
                    'p-2 rounded-lg transition-colors',
                    selectedAgentId === agent.id
                      ? 'bg-primary/20'
                      : 'bg-muted group-hover:bg-primary/10',
                  ]"
                >
                  <Bot
                    :class="[
                      'h-5 w-5 flex-shrink-0',
                      selectedAgentId === agent.id
                        ? 'text-primary'
                        : 'text-muted-foreground group-hover:text-primary',
                    ]"
                  />
                </div>

                <div class="min-w-0 flex-1 pt-1">
                  <div class="flex items-center gap-2 mb-1.5">
                    <span
                      :class="[
                        'font-medium truncate transition-colors',
                        selectedAgentId === agent.id ? 'text-primary' : '',
                      ]"
                      >{{ agent.name }}</span
                    >
                    <Badge
                      v-if="agent.scope"
                      :variant="getScopeBadgeVariant(agent.scope)"
                      class="text-[10px] px-1.5 h-4"
                    >
                      {{ getScopeLabel(agent.scope) }}
                    </Badge>
                  </div>
                  <p class="text-sm text-muted-foreground truncate leading-relaxed">
                    {{ agent.description || '無描述' }}
                  </p>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped>
.transition-all {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
