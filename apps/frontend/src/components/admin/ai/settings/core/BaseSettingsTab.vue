<template>
  <div class="space-y-6">
    <!-- 載入狀態 -->
    <div v-if="isLoading" class="flex items-center justify-center py-8">
      <div class="flex items-center space-x-3">
        <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
        <span class="text-sm text-zinc-600 dark:text-zinc-400">載入設定中...</span>
      </div>
    </div>

    <!-- 錯誤狀態 -->
    <div v-else-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
      <div class="flex items-center space-x-3">
        <AlertCircle class="w-4 h-4 text-red-500 flex-shrink-0" />
        <div>
          <h3 class="font-medium text-sm text-red-900 dark:text-red-100">載入失敗</h3>
          <p class="text-xs text-red-700 dark:text-red-300 mt-0.5">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- 主要設定內容 -->
    <div v-else-if="aiGlobalSettings" class="space-y-5">
      <!-- 全域啟用設定 -->
      <Card class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 shadow-sm">
        <div class="p-4 border-b border-gray-200 dark:border-zinc-700">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-blue-100 dark:bg-blue-800 rounded-md">
              <Power class="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">全域 AI 控制</h3>
              <p class="text-sm text-zinc-600 dark:text-zinc-400">控制整個系統的 AI 功能啟用狀態</p>
            </div>
          </div>
        </div>
        
        <div class="p-4">
          <div class="flex items-center justify-between">
            <div class="space-y-2 flex-1">
              <Label for="global-ai-switch" class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                啟用 AI 功能
              </Label>
              <p class="text-xs text-zinc-500 dark:text-zinc-400">
                關閉此選項將停用所有 AI 功能，包括所有機器人和 API 存取
              </p>
            </div>
            
            <div class="flex flex-col items-end space-y-2 ml-4">
              <Switch
                id="global-ai-switch"
                :model-value="aiGlobalSettings.is_ai_globally_enabled"
                @update:model-value="handleSwitchChange"
                :disabled="isUpdating"
              />
              <div class="flex items-center space-x-1.5 text-xs">
                <div :class="[
                  'w-2 h-2 rounded-full',
                  aiGlobalSettings.is_ai_globally_enabled ? 'bg-green-500' : 'bg-red-500'
                ]"></div>
                <span class="text-zinc-500 dark:text-zinc-400">
                  {{ aiGlobalSettings.is_ai_globally_enabled ? "已啟用" : "已停用" }}
                </span>
                <span v-if="isUpdating" class="text-amber-500">(更新中...)</span>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <!-- 配額設定 -->
      <Card class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 shadow-sm">
        <div class="p-4 border-b border-gray-200 dark:border-zinc-700">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-green-100 dark:bg-green-800 rounded-md">
                <BarChart3 class="w-4 h-4 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">使用配額設定</h3>
                <p class="text-sm text-zinc-600 dark:text-zinc-400">設定系統每月的使用限制</p>
              </div>
            </div>
            
            <Button
              @click="handleSaveQuotaSettings"
              :disabled="isUpdating"
              size="sm"
            >
              <Save class="w-4 h-4 mr-2" />
              儲存配額
            </Button>
          </div>
        </div>
        
        <div class="p-4 space-y-4">
          <!-- Token 配額 -->
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <Coins class="w-4 h-4 text-amber-500" />
              <Label for="global-monthly-tokens" class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                每月 Token 配額
              </Label>
            </div>
            <Input
              id="global-monthly-tokens"
              type="text"
              v-model="tokensInputValue"
              @update:model-value="handleTokensChange"
              placeholder="例如：1000000 (留空表示不限制)"
              :disabled="isUpdating"
              class="h-10 text-sm"
            />
            <p class="text-xs text-zinc-500 dark:text-zinc-400 flex items-center space-x-1">
              <Info class="w-3 h-3 flex-shrink-0" />
              <span>整個系統每月可使用的總 Token 數量上限</span>
            </p>
          </div>

          <!-- API 呼叫配額 -->
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <Activity class="w-4 h-4 text-blue-500" />
              <Label for="global-monthly-calls" class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                每月 API 呼叫配額
              </Label>
            </div>
            <Input
              id="global-monthly-calls"
              type="text"
              v-model="callsInputValue"
              @update:model-value="handleCallsChange"
              placeholder="例如：10000 (留空表示不限制)"
              :disabled="isUpdating"
              class="h-10 text-sm"
            />
            <p class="text-xs text-zinc-500 dark:text-zinc-400 flex items-center space-x-1">
              <Info class="w-3 h-3 flex-shrink-0" />
              <span>整個系統每月可進行的總 API 呼叫次數上限</span>
            </p>
          </div>
        </div>
      </Card>


    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch, ref } from "vue";
import { 
  Power, BarChart3, Save, Coins, Activity, Info, 
  AlertCircle
} from "lucide-vue-next";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useAiGlobalSettings } from "@/composables/admin/ai/settings/useAiGlobalSettings";

// 使用 composable
const {
  aiGlobalSettings,
  loading: isLoading,
  isUpdating,
  error,
  fetch: fetchGlobalSettings,
  changeGlobalEnabled,
  changeQuotaTokens,
  changeQuotaCalls,
  saveQuotaSettings,
} = useAiGlobalSettings();

// 使用 ref 來處理雙向綁定
const tokensInputValue = ref('');
const callsInputValue = ref('');

// 監聽數據變化，同步到 input
watch(
  () => aiGlobalSettings.value?.global_monthly_quota_tokens,
  (newValue) => {
    tokensInputValue.value = newValue != null ? newValue.toString() : '';
  },
  { immediate: true }
);

watch(
  () => aiGlobalSettings.value?.global_monthly_quota_calls,
  (newValue) => {
    callsInputValue.value = newValue != null ? newValue.toString() : '';
  },
  { immediate: true }
);

// 處理輸入變化
const handleTokensChange = (value: string | number) => {
  const stringValue = String(value);
  tokensInputValue.value = stringValue;
  changeQuotaTokens(stringValue);
};

const handleCallsChange = (value: string | number) => {
  const stringValue = String(value);
  callsInputValue.value = stringValue;
  changeQuotaCalls(stringValue);
};

// 處理開關變化
const handleSwitchChange = async (value: boolean) => {
  if (isUpdating.value) {
    return;
  }

  try {
    await changeGlobalEnabled(value);
  } catch (error) {
    console.error("更新全域 AI 設定失敗:", error);
  }
};

// 處理配額設定保存
const handleSaveQuotaSettings = async () => {
  try {
    await saveQuotaSettings();
  } catch (error) {
    console.error("保存配額設定失敗:", error);
  }
};

// 組件掛載時載入設定
onMounted(async () => {
  await fetchGlobalSettings();
});
</script> 