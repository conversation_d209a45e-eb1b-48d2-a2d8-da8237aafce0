<template>
  <div class="space-y-6">
    <!-- 操作按鈕區域 -->
    <div class="flex justify-end">
      <Button
        variant="outline"
        size="sm"
        @click="$emit('create-key')"
      >
        <Plus class="h-4 w-4 mr-2" />
        新增金鑰
      </Button>
    </div>

    <!-- 載入狀態 -->
    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <div class="flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
        <span class="text-zinc-600 dark:text-zinc-400">載入金鑰列表中...</span>
      </div>
    </div>

    <!-- 空狀態 -->
    <div v-if="!isLoading && aiKeys.length === 0" class="flex flex-col items-center justify-center py-16">
      <div class="flex flex-col items-center space-y-6 text-center max-w-lg">
        <!-- 視覺圖示 -->
        <div class="relative">
          <div class="p-6 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-2xl">
            <Key class="w-12 h-12 text-blue-600 dark:text-blue-400" />
          </div>
          <div class="absolute -top-1 -right-1 p-1 bg-yellow-100 dark:bg-yellow-900/50 rounded-full">
            <AlertTriangle class="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
          </div>
        </div>
        
        <!-- 文字內容 -->
        <div class="space-y-2">
          <h3 class="text-xl font-semibold text-zinc-900 dark:text-zinc-100">尚未設定 API 金鑰</h3>
          <p class="text-sm text-zinc-600 dark:text-zinc-400 leading-relaxed">
            API 金鑰是連接 AI 服務的核心憑證。您需要新增至少一個有效的 API 金鑰<br />
            才能開始使用智能功能，如對話助理、內容生成等。
          </p>
        </div>

        <!-- 支援的提供商 -->
        <div class="flex items-center space-x-4 py-3 px-4 bg-zinc-50 dark:bg-zinc-800 rounded-lg">
          <span class="text-xs text-zinc-500 dark:text-zinc-400 font-medium">支援：</span>
          <div class="flex items-center space-x-3">
            <div class="flex items-center space-x-1">
              <Brain class="w-4 h-4 text-green-600" />
              <span class="text-xs font-medium text-zinc-700 dark:text-zinc-300">OpenAI</span>
            </div>
            <div class="flex items-center space-x-1">
              <Zap class="w-4 h-4 text-orange-600" />
              <span class="text-xs font-medium text-zinc-700 dark:text-zinc-300">Anthropic</span>
            </div>
            <div class="flex items-center space-x-1">
              <Globe class="w-4 h-4 text-blue-600" />
              <span class="text-xs font-medium text-zinc-700 dark:text-zinc-300">Google</span>
            </div>
          </div>
        </div>
        
        <!-- 操作按鈕 -->
        <Button 
          @click="$emit('create-key')" 
          class="mt-4 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white"
          size="sm"
        >
          <Plus class="w-4 h-4 mr-2" />
          新增第一個 API 金鑰
        </Button>
      </div>
    </div>

    <!-- 金鑰列表 -->
    <div v-else class="space-y-4">
      <!-- 金鑰卡片 -->
      <div
        v-for="key in aiKeys"
        :key="key.id"
        class="border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-zinc-800 p-6 hover:shadow-sm transition-shadow duration-200"
      >
        <div class="flex items-start justify-between">
          <!-- 左側內容 -->
          <div class="flex-1">
            <!-- 標題行 -->
            <div class="flex items-center space-x-3 mb-2">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-zinc-100">
                {{ key.name }}
              </h3>
              <Badge variant="outline" class="text-xs">
                {{ getProviderDisplayName(key.provider) }}
              </Badge>
              <Badge 
                :variant="key.is_enabled ? 'default' : 'secondary'" 
                :class="key.is_enabled ? 'bg-black text-white' : 'bg-gray-100 text-gray-600'"
                class="text-xs"
              >
                {{ key.is_enabled ? 'active' : 'inactive' }}
              </Badge>
            </div>
            
            <!-- 描述文字 -->
            <p class="text-sm text-gray-600 dark:text-zinc-400 mb-3">
              {{ getKeyDescription(key) }}
            </p>
            
            <!-- 額外資訊 -->
            <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-zinc-500 mb-4">
              <span>
                Created: {{ formatDate(key.created_at) }}
              </span>
              <span v-if="key.last_test">
                Last tested: {{ formatLastTest(key.last_test) }}
              </span>
              <span v-if="key.api_url">
                Endpoint: {{ getApiUrlDisplay(key) }}
              </span>
            </div>
            

          </div>
          
          <!-- 右側控制項 -->
          <div class="flex items-center space-x-3 ml-6">
            <!-- 開關 -->
            <div class="flex items-center">
              <label class="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  :checked="key.is_enabled"
                  @change="toggleKeyStatus(key)"
                  class="sr-only peer"
                >
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-black"></div>
              </label>
            </div>
            
            <!-- 編輯按鈕 -->
            <Button
              @click="$emit('edit-key', key)"
              variant="outline"
              size="sm"
              class="text-gray-600 dark:text-zinc-400 hover:text-gray-900 dark:hover:text-zinc-100"
            >
              編輯
            </Button>
            
            <!-- 刪除按鈕 -->
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  class="text-gray-400 hover:text-red-600 p-2"
                >
                  <Trash2 class="w-4 h-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent class="max-w-md">
                <AlertDialogHeader>
                  <div class="flex items-center space-x-3">
                    <div class="p-2 bg-red-100 dark:bg-red-900 rounded-full">
                      <AlertTriangle class="w-5 h-5 text-red-600 dark:text-red-400" />
                    </div>
                    <div>
                      <AlertDialogTitle class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">
                        確認刪除金鑰
                      </AlertDialogTitle>
                    </div>
                  </div>
                  <AlertDialogDescription class="text-sm text-zinc-600 dark:text-zinc-400 mt-3">
                    您即將刪除 API 金鑰：
                    <span class="font-semibold text-zinc-900 dark:text-zinc-100">「{{ key.name }}」</span>
                    <br /><br />
                    此操作無法復原，所有使用此金鑰的 AI 功能將會停止運作。
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter class="gap-2 pt-4">
                  <AlertDialogCancel class="px-4">取消</AlertDialogCancel>
                  <AlertDialogAction 
                    @click="$emit('delete-key', key.id)"
                    class="bg-red-500 hover:bg-red-600 text-white px-4"
                  >
                    <Trash2 class="w-4 h-4 mr-2" />
                    確認刪除
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { 
  Key, Edit, Trash2, Info, Copy, Plus, AlertTriangle,
  Brain, Zap, Globe, Bot
} from "lucide-vue-next";
import { useNotification } from "@/composables/shared/useNotification";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Props 定義
interface Props {
  aiKeys: any[];
  isLoading: boolean;
  keyProviders: any[];
}

const props = defineProps<Props>();

// Emits 定義
const emit = defineEmits<{
  'edit-key': [key: any];
  'delete-key': [keyId: string];
  'create-key': [];
  'toggle-status': [key: any];
}>();

// 狀態管理
const showDeleteDialog = ref(false);
const keyToDelete = ref<any>(null);
const notification = useNotification();

// 提供商配置
const getProviderConfig = (provider: string) => {
  const configs = {
    'openai': { 
      icon: Brain, 
      color: 'bg-gradient-to-br from-green-500 to-emerald-600',
      name: 'OpenAI'
    },
    'anthropic': { 
      icon: Zap, 
      color: 'bg-gradient-to-br from-orange-500 to-red-600',
      name: 'Anthropic'
    },
    'google': { 
      icon: Globe, 
      color: 'bg-gradient-to-br from-blue-500 to-indigo-600',
      name: 'Google'
    },
    'default': { 
      icon: Bot, 
      color: 'bg-gradient-to-br from-zinc-500 to-zinc-600',
      name: 'Unknown'
    }
  };
  return configs[provider as keyof typeof configs] || configs.default;
};

// 獲取提供商顯示名稱
const getProviderDisplayName = (provider: string) => {
  const names = {
    'openai': 'OpenAI',
    'anthropic': 'Anthropic', 
    'google-gemini': 'Google Gemini',
    'google': 'Google',
    'openai-compatible': 'OpenAI Compatible',
    'default': provider.charAt(0).toUpperCase() + provider.slice(1)
  };
  
  return names[provider as keyof typeof names] || names.default;
};

// 遮罩 API 金鑰
const maskApiKey = (apiKey: string) => {
  if (!apiKey) return '';
  if (apiKey.length <= 8) return apiKey;
  return `${apiKey.substring(0, 4)}${'*'.repeat(apiKey.length - 8)}${apiKey.substring(apiKey.length - 4)}`;
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 複製到剪貼板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    notification.toast.success('已複製', 'API 金鑰已複製到剪貼板');
  } catch (error) {
    notification.toast.error('複製失敗', '無法複製到剪貼板');
  }
};

// 處理刪除金鑰
const handleDeleteKey = (key: any) => {
  keyToDelete.value = key;
  showDeleteDialog.value = true;
};

// 確認刪除
const confirmDelete = () => {
  if (keyToDelete.value) {
    emit('delete-key', keyToDelete.value.id);
    showDeleteDialog.value = false;
    keyToDelete.value = null;
  }
};

// 獲取金鑰描述
const getKeyDescription = (key: any) => {
  // 如果金鑰有自定義描述，優先使用
  if (key.description) {
    return key.description;
  }
  
  // 根據供應商提供預設描述
  const descriptions = {
    'openai': 'Most capable model for complex tasks',
    'anthropic': 'Advanced AI model for reasoning and analysis', 
    'google-gemini': 'Powerful multimodal AI for text, images, and code',
    'google': 'Powerful language model for various applications',
    'openai-compatible': 'OpenAI-compatible third-party or local deployment',
    'default': 'AI language model for general tasks'
  };
  
  return descriptions[key.provider as keyof typeof descriptions] || descriptions.default;
};

// 獲取金鑰成本
const getKeyCost = (key: any) => {
  const costs = {
    'openai': '$0.03/1K tokens',
    'anthropic': '$0.25/1K tokens', 
    'google-gemini': '$0.125/1K tokens',
    'google': '$0.125/1K tokens',
    'openai-compatible': 'Varies by provider',
    'default': 'See provider pricing'
  };
  return costs[key.provider as keyof typeof costs] || costs.default;
};

// 切換金鑰狀態
const toggleKeyStatus = (key: any) => {
  emit('toggle-status', key);
};

// 格式化最後測試時間
const formatLastTest = (lastTest: string | null) => {
  if (!lastTest) return 'Never tested';
  
  try {
    const date = new Date(lastTest);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        year: diffDays > 365 ? 'numeric' : undefined 
      });
    }
  } catch {
    return 'Unknown';
  }
};

// 獲取 API URL 顯示
const getApiUrlDisplay = (key: any) => {
  if (!key.api_url) {
    return 'Default';
  }
  
  try {
    const url = new URL(key.api_url);
    return url.hostname;
  } catch {
    return key.api_url;
  }
};
</script> 