<template>
  <Sidebar ref="sidebarRef" collapsible="icon">
    <!-- LOGO 區塊 -->
    <div
      class="h-14 flex items-center justify-center px-3 border-b border-gray-200 dark:border-gray-700"
    >
      <!-- 收合狀態時顯示的LOGO圖示 -->
      <div
        class="w-8 h-8 flex items-center justify-center group-data-[collapsible=icon]:flex hidden"
      >
        <img
          src="@/assets/images/logo-icon.svg"
          alt="HorizAI Logo"
          class="w-6 h-6 hidden dark:block text-white"
        />
        <img
          src="@/assets/images/logo-icon.svg"
          alt="HorizAI Logo"
          class="w-6 h-6 block dark:hidden text-emerald-600"
        />
      </div>

      <!-- 展開狀態時顯示的完整LOGO -->
      <div class="h-6 w-full group-data-[collapsible=icon]:hidden flex items-center justify-center">
        <img
          src="@/assets/images/logo-light.svg"
          alt="HorizAI Logo"
          class="h-full hidden dark:block"
        />
        <img
          src="@/assets/images/logo.svg"
          alt="HorizAI Logo"
          class="h-full block dark:hidden text-gray-800"
        />
      </div>
    </div>

    <!-- 選單內容區域 -->
    <SidebarContent class="pt-2">
      <template v-for="(group, idx) in menu" :key="group.group">
        <SidebarGroup v-if="group.items.some((i) => i.show)" class="mb-1">
          <SidebarGroupLabel
            class="group-data-[collapsible=icon]:hidden px-3 text-xs font-medium text-gray-500 dark:text-gray-400 mb-1"
          >
            {{ group.group }}
          </SidebarGroupLabel>
          <SidebarGroupContent class="space-y-1">
            <SidebarMenu>
              <SidebarMenuItem v-for="item in group.items.filter((i) => i.show)" :key="item.title">
                <TooltipProvider v-if="isIconMode">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <SidebarMenuButton asChild :active="isActive(item.to)">
                        <router-link
                          :to="item.to"
                          class="flex items-center gap-3 w-full py-2 px-3 rounded-md transition-colors hover:bg-gray-100 dark:hover:bg-gray-800"
                          :class="{
                            'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100':
                              isActive(item.to),
                          }"
                        >
                          <component :is="item.icon" class="h-4 w-4 flex-shrink-0" />
                          <span
                            class="truncate group-data-[collapsible=icon]:hidden text-sm font-medium"
                          >
                            {{ item.title }}
                          </span>
                        </router-link>
                      </SidebarMenuButton>
                    </TooltipTrigger>
                    <TooltipContent side="right" class="font-normal">
                      {{ item.title }}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <SidebarMenuButton v-else asChild :active="isActive(item.to)">
                  <router-link
                    :to="item.to"
                    class="flex items-center gap-3 w-full py-2 px-3 rounded-md transition-colors hover:bg-gray-100 dark:hover:bg-gray-800"
                    :class="{
                      'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100': isActive(
                        item.to,
                      ),
                    }"
                  >
                    <component :is="item.icon" class="h-4 w-4 flex-shrink-0" />
                    <span class="truncate group-data-[collapsible=icon]:hidden text-sm font-medium">
                      {{ item.title }}
                    </span>
                  </router-link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        <SidebarSeparator
          v-if="idx < menu.length - 1"
          class="my-2 border-gray-200 dark:border-gray-700"
        />
      </template>
    </SidebarContent>
  </Sidebar>
</template>

<script setup lang="ts">
import { usePermission } from '@/composables/admin/usePermission';
import { useRoute } from 'vue-router';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarSeparator,
} from '@/components/ui/sidebar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  LayoutDashboard,
  Building2,
  Users as UsersIcon,
  FolderKanban,
  CreditCard,
  Receipt as ReceiptIcon,
  Settings2,
  UserCog,
  Shield,
  Lock,
  WrenchIcon,
  FileText,
  RefreshCw,
  Home,
  TestTube,
  Table,
  MessageSquare,
} from 'lucide-vue-next';
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { Actions, Subjects } from '@horizai/permissions';
import { loggers, debugConfig } from '@/config/debug.config';

const logger = loggers.component;
const { can, isSuperAdmin, isSystemAdmin, currentUser, ability } = usePermission();
const route = useRoute();

function isActive(path: string) {
  return route.path === path;
}

const menu = computed(() => {
  // 只在開發環境或明確啟用時才輸出調試信息
  if (debugConfig.enableComponentLogs) {
    logger.debug('AdminSidebar 權限檢查:', {
      isSuperAdmin: isSuperAdmin.value,
      isSystemAdmin: isSystemAdmin.value,
      userId: currentUser.value?.id,
      abilityExists: !!ability.value,
      abilityRulesCount: ability.value?.rules?.length || 0,
    });
  }

  return [
    // 1. 主要功能
    {
      group: '主要功能',
      items: [
        {
          title: '儀表板',
          icon: LayoutDashboard,
          to: '/admin/dashboard',
          show: true,
        },
        {
          title: 'AI 管理中心',
          icon: Home,
          to: '/admin/ai-dashboard',
          show:
            isSuperAdmin.value ||
            isSystemAdmin.value ||
            can(Actions.MANAGE, Subjects.SYSTEM_SETTINGS),
        },
      ],
    },

    // 2. 租戶管理
    {
      group: '租戶管理',
      items: [
        { title: '租戶列表', icon: Building2, to: '/admin/tenants', show: true },
        {
          title: '租戶使用者',
          icon: UsersIcon,
          to: '/admin/tenant-users',
          show:
            isSuperAdmin.value || isSystemAdmin.value || can(Actions.MANAGE, Subjects.TENANT_USER),
        },
        {
          title: '工作區管理',
          icon: FolderKanban,
          to: '/admin/workspaces',
          show: true,
        },
      ],
    },

    // 3. 訂閱管理
    {
      group: '訂閱管理',
      items: [
        {
          title: '訂閱方案',
          icon: CreditCard,
          to: '/admin/plans',
          show: isSuperAdmin.value || isSystemAdmin.value || can(Actions.MANAGE, Subjects.PLAN),
        },
        {
          title: '訂閱訂單',
          icon: ReceiptIcon,
          to: '/admin/orders',
          show: isSuperAdmin.value || isSystemAdmin.value || can(Actions.MANAGE, Subjects.ORDER),
        },
      ],
    },

    // 4. 系統設定
    {
      group: '系統設定',
      items: [
        {
          title: '系統使用者',
          icon: UserCog,
          to: '/admin/system-users',
          show:
            isSuperAdmin.value || isSystemAdmin.value || can(Actions.MANAGE, Subjects.SYSTEM_USER),
        },
        {
          title: '角色權限',
          icon: Shield,
          to: '/admin/roles',
          show: isSuperAdmin.value || isSystemAdmin.value || can(Actions.MANAGE, Subjects.ROLE),
        },
        {
          title: 'Line 整合',
          icon: Settings2,
          to: '/admin/line-settings',
          show:
            isSuperAdmin.value ||
            isSystemAdmin.value ||
            can(Actions.MANAGE, Subjects.SYSTEM_SETTINGS),
        },
        {
          title: '系統設定',
          icon: WrenchIcon,
          to: '/admin/settings',
          show:
            isSuperAdmin.value ||
            isSystemAdmin.value ||
            can(Actions.MANAGE, Subjects.SYSTEM_SETTINGS),
        },
        {
          title: '系統日誌',
          icon: FileText,
          to: '/admin/system-logs',
          show: isSuperAdmin.value || isSystemAdmin.value || can(Actions.READ, Subjects.SYSTEM_LOG),
        },
      ],
    },

    // 5. 開發工具（僅在開發環境或超級管理員可見）
    {
      group: '開發工具',
      items: [
        {
          title: '表單系統測試',
          icon: TestTube,
          to: '/admin/forms/test',
          show:
            (process.env.NODE_ENV === 'development' || isSuperAdmin.value) &&
            (isSuperAdmin.value ||
              isSystemAdmin.value ||
              can(Actions.MANAGE, Subjects.SYSTEM_SETTINGS)),
        },
        {
          title: '表格系統測試',
          icon: Table,
          to: '/admin/tables/test',
          show:
            (process.env.NODE_ENV === 'development' || isSuperAdmin.value) &&
            (isSuperAdmin.value ||
              isSystemAdmin.value ||
              can(Actions.MANAGE, Subjects.SYSTEM_SETTINGS)),
        },
        {
          title: '對話框系統測試',
          icon: MessageSquare,
          to: '/admin/dialogs/test',
          show:
            (process.env.NODE_ENV === 'development' || isSuperAdmin.value) &&
            (isSuperAdmin.value ||
              isSystemAdmin.value ||
              can(Actions.MANAGE, Subjects.SYSTEM_SETTINGS)),
        },
      ],
    },
  ];
});

// 取得 Sidebar 是否為 icon 狀態
const sidebarRef = ref<any>(null);
const isIconMode = ref(false);

function updateIconMode() {
  const el = sidebarRef.value?.$el ?? sidebarRef.value;
  if (el && typeof el.getAttribute === 'function') {
    isIconMode.value = el.getAttribute('data-collapsible') === 'icon';
  }
}

onMounted(() => {
  updateIconMode();
  const el = sidebarRef.value?.$el ?? sidebarRef.value;
  if (el && typeof el.getAttribute === 'function') {
    const observer = new MutationObserver(updateIconMode);
    observer.observe(el, {
      attributes: true,
      attributeFilter: ['data-collapsible'],
    });
    onBeforeUnmount(() => observer.disconnect());
  }
});
</script>

<style scoped>
:deep(svg) {
  color: currentColor;
}

/* 確保選單項目有適當的過渡效果 */
:deep(.sidebar-menu-button) {
  transition: all 0.2s ease;
}

/* 活躍狀態樣式 */
:deep(.sidebar-menu-button[data-active='true']) {
  background-color: rgb(243 244 246);
  color: rgb(17 24 39);
}

:deep(.dark .sidebar-menu-button[data-active='true']) {
  background-color: rgb(31 41 55);
  color: rgb(249 250 251);
}
</style>
