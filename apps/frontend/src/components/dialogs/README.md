# HorizAI 統一對話框系統

## 📋 概述

HorizAI 統一對話框系統提供了完整的模態框、Sheet 和表單對話框解決方案，基於 shadcn/ui 組件構建，支援區域主題差異、載入狀態管理和無障礙設計。

## 🏗️ 架構設計

### 核心組件

```
/components/dialogs/
├── index.ts                    # 統一導出和全域服務
├── UnifiedDialog.vue           # 統一對話框組件
├── UnifiedSheet.vue            # 統一 Sheet 組件
├── UnifiedFormDialog.vue       # 統一表單對話框組件
├── DialogTestPage.vue          # 測試頁面
└── README.md                   # 文檔
```

### Composables

```
/composables/dialogs/
├── useDialogState.ts           # 對話框狀態管理
├── useConfirmDialog.ts         # 確認對話框
└── useFormDialog.ts            # 表單對話框
```

## 🎯 核心功能

### 1. 統一對話框狀態管理

- **多種對話框類型**：info、success、warning、error、confirm、custom
- **靈活的大小配置**：xs、sm、md、lg、xl、full
- **位置控制**：center、top、bottom
- **自動關閉**：支援自動關閉延遲
- **鍵盤支援**：ESC 關閉、Enter 確認

### 2. 確認對話框系統

- **基本確認**：簡單的是/否確認
- **刪除確認**：危險操作的特殊確認
- **狀態變更確認**：狀態切換確認
- **批量操作確認**：多項目操作確認
- **異步操作確認**：帶載入狀態的確認

### 3. 表單對話框

- **多種模式**：create、edit、view
- **兩種類型**：dialog、sheet
- **表單驗證**：整合統一表單驗證系統
- **載入狀態**：提交過程的載入管理
- **錯誤處理**：完整的錯誤處理機制

### 4. 區域主題支援

- **Admin 區域**：緊湊型設計，預設使用 Sheet
- **Workspace 區域**：舒適型設計，預設使用 Dialog
- **自動主題檢測**：根據當前區域自動應用主題

## 🚀 快速開始

### 基本對話框

```vue
<script setup lang="ts">
import { useDialogState } from '@/composables/dialogs/useDialogState'

const dialog = useDialogState()

// 顯示資訊對話框
const showInfo = async () => {
  await dialog.showInfo('標題', '描述內容')
}

// 顯示確認對話框
const showConfirm = async () => {
  const result = await dialog.open({
    type: 'confirm',
    title: '確認操作',
    description: '您確定要執行此操作嗎？',
    showCancel: true,
  })
  
  if (result) {
    console.log('使用者確認')
  }
}
</script>
```

### 確認對話框

```vue
<script setup lang="ts">
import { useConfirmDialog } from '@/composables/dialogs/useConfirmDialog'

const confirmDialog = useConfirmDialog()

// 刪除確認
const deleteUser = async (userName: string) => {
  const confirmed = await confirmDialog.confirmDelete({
    itemName: userName,
    itemType: '使用者',
    onDelete: async () => {
      // 執行刪除邏輯
      await userService.delete(userName)
    }
  })
}

// 狀態變更確認
const changeStatus = async () => {
  await confirmDialog.confirmStatusChange(
    '使用者名稱',
    '啟用',
    '停用',
    async () => {
      // 執行狀態變更
    }
  )
}
</script>
```

### 表單對話框

```vue
<template>
  <UnifiedFormDialog
    v-model:open="formOpen"
    type="sheet"
    mode="create"
    title="建立使用者"
    :initial-values="formValues"
    @submit="handleSubmit"
  >
    <FormField
      type="text"
      label="姓名"
      v-model="formValues.name"
      required
    />
    <FormField
      type="email"
      label="電子郵件"
      v-model="formValues.email"
      required
    />
  </UnifiedFormDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { UnifiedFormDialog } from '@/components/dialogs'
import { FormField } from '@/components/forms'

const formOpen = ref(false)
const formValues = ref({
  name: '',
  email: '',
})

const handleSubmit = async (values: any) => {
  // 處理表單提交
  await userService.create(values)
}
</script>
```

### 全域服務

```vue
<script setup lang="ts">
import { $confirm, $dialog } from '@/components/dialogs'

// 使用全域確認服務
const deleteItem = async () => {
  const confirmed = await $confirm.delete(
    '項目名稱',
    async () => {
      // 刪除邏輯
    }
  )
}

// 使用全域對話框服務
const showMessage = async () => {
  await $dialog.success('操作成功', '您的操作已成功完成')
}
</script>
```

## 🎨 組件 API

### UnifiedDialog

| 屬性 | 類型 | 預設值 | 說明 |
|------|------|--------|------|
| `open` | `boolean` | `false` | 是否開啟 |
| `type` | `DialogType` | `'info'` | 對話框類型 |
| `size` | `DialogSize` | `'md'` | 對話框大小 |
| `title` | `string` | - | 對話框標題 |
| `description` | `string` | - | 對話框描述 |
| `confirmText` | `string` | `'確認'` | 確認按鈕文字 |
| `cancelText` | `string` | `'取消'` | 取消按鈕文字 |
| `showCancel` | `boolean` | `true` | 是否顯示取消按鈕 |
| `loading` | `boolean` | `false` | 是否載入中 |
| `error` | `string` | - | 錯誤訊息 |

### UnifiedSheet

| 屬性 | 類型 | 預設值 | 說明 |
|------|------|--------|------|
| `open` | `boolean` | `false` | 是否開啟 |
| `side` | `'top' \| 'right' \| 'bottom' \| 'left'` | `'right'` | Sheet 側邊 |
| `size` | `'sm' \| 'md' \| 'lg' \| 'xl' \| 'full'` | `'lg'` | Sheet 大小 |
| `mode` | `FormDialogMode` | `'create'` | Sheet 模式 |
| `title` | `string` | - | Sheet 標題 |
| `closeOnOutsideClick` | `boolean` | `false` | 是否可點擊外部關閉 |

### UnifiedFormDialog

| 屬性 | 類型 | 預設值 | 說明 |
|------|------|--------|------|
| `open` | `boolean` | `false` | 是否開啟 |
| `type` | `'dialog' \| 'sheet'` | `'sheet'` | 對話框類型 |
| `mode` | `'create' \| 'edit' \| 'view'` | `'create'` | 表單模式 |
| `initialValues` | `object` | - | 表單初始值 |

## 🎯 最佳實踐

### 1. 選擇合適的對話框類型

```typescript
// ✅ 簡單確認使用 Dialog
await $dialog.info('提示', '操作已完成')

// ✅ 複雜表單使用 Sheet
<UnifiedFormDialog type="sheet" mode="create" />

// ✅ 危險操作使用確認對話框
await $confirm.delete('使用者', deleteAction)
```

### 2. 區域主題適配

```typescript
// ✅ 使用區域配置
import { getRegionDialogConfig } from '@/components/dialogs'

const config = getRegionDialogConfig('admin')
// Admin: { type: 'sheet', size: 'lg', closeOnOutsideClick: false }

const workspaceConfig = getRegionDialogConfig('workspace')
// Workspace: { type: 'dialog', size: 'md', closeOnOutsideClick: true }
```

### 3. 錯誤處理

```typescript
// ✅ 使用 withLoading 包裝異步操作
const dialog = useDialogState()

const handleSubmit = async () => {
  try {
    await dialog.withLoading(async () => {
      await submitForm()
    })
  } catch (error) {
    // 錯誤會自動設定到 dialog.error
  }
}
```

### 4. 無障礙設計

```vue
<!-- ✅ 提供適當的 ARIA 標籤 -->
<UnifiedDialog
  :title="title"
  :description="description"
  role="dialog"
  aria-modal="true"
/>
```

## 🧪 測試

### 測試頁面

訪問 `/admin/dialogs/test` 查看完整的測試頁面，包含：

- 基本對話框測試
- 確認對話框測試
- 表單對話框測試
- 載入和錯誤處理測試
- 區域主題切換測試

### 單元測試

```typescript
import { useDialogState } from '@/composables/dialogs/useDialogState'

describe('useDialogState', () => {
  it('should open and close dialog', async () => {
    const dialog = useDialogState()
    
    const promise = dialog.open({
      title: 'Test',
      type: 'info'
    })
    
    expect(dialog.isOpen.value).toBe(true)
    
    dialog.confirm()
    const result = await promise
    
    expect(result).toBe(true)
    expect(dialog.isOpen.value).toBe(false)
  })
})
```

## 🔧 進階配置

### 自定義對話框配置

```typescript
// 建立自定義配置
export const customDialogConfigs = {
  userDelete: (userName: string) => ({
    title: '刪除使用者',
    description: `確定要刪除「${userName}」嗎？`,
    type: 'error' as const,
    confirmText: '刪除',
    isDangerous: true,
  })
}
```

### 全域插件配置

```typescript
// main.ts
import { DialogPlugin } from '@/components/dialogs'

app.use(DialogPlugin)

// 現在可以在組件中使用
this.$confirm.delete(...)
this.$dialog.success(...)
```

## 📚 相關文檔

- [統一表單系統](../forms/README.md)
- [區域主題系統](../../composables/useRegionTheme.ts)
- [shadcn/ui Dialog](https://ui.shadcn.com/docs/components/dialog)
- [shadcn/ui Sheet](https://ui.shadcn.com/docs/components/sheet)

## 🤝 貢獻指南

1. 遵循現有的程式碼風格和架構模式
2. 新增功能時請同時更新測試和文檔
3. 確保支援區域主題差異
4. 維持無障礙設計標準
5. 提交前執行完整的測試套件

---

**版本：** 1.0.0  
**最後更新：** 2024-12-19  
**維護者：** HorizAI Design System Team
