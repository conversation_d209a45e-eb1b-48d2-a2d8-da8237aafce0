<!--
  統一對話框組件
  
  基於 shadcn/ui Dialog 組件的統一對話框解決方案
  支援不同類型、大小和區域主題差異
  整合載入狀態和錯誤處理
-->

<template>
  <Dialog :open="isOpen" @update:open="handleOpenChange">
    <DialogContent :class="dialogClasses" @pointer-down-outside="handleOutsideClick">
      <!-- 對話框標題區 -->
      <DialogHeader v-if="showHeader" :class="headerClasses">
        <div class="flex items-center space-x-3">
          <!-- 圖示 -->
          <div v-if="iconComponent" :class="iconContainerClasses">
            <component :is="iconComponent" :class="iconClasses" />
          </div>
          
          <!-- 標題和描述 -->
          <div class="flex-1 min-w-0">
            <DialogTitle :class="titleClasses">
              {{ title }}
            </DialogTitle>
            <DialogDescription v-if="description" :class="descriptionClasses">
              {{ description }}
            </DialogDescription>
          </div>
          
          <!-- 載入指示器 -->
          <div v-if="isLoading" class="flex-shrink-0">
            <Loader2 class="w-4 h-4 animate-spin text-muted-foreground" />
          </div>
        </div>
      </DialogHeader>

      <!-- 對話框內容 -->
      <div :class="contentClasses">
        <!-- 錯誤訊息 -->
        <div v-if="error" :class="errorClasses">
          <AlertCircle class="w-4 h-4 mr-2 flex-shrink-0" />
          <span>{{ error }}</span>
        </div>

        <!-- 主要內容插槽 -->
        <slot 
          :is-loading="isLoading"
          :error="error"
          :clear-error="clearError"
          :set-loading="setLoading"
        />
      </div>

      <!-- 對話框操作區 -->
      <DialogFooter v-if="showFooter" :class="footerClasses">
        <slot name="footer" :is-loading="isLoading" :can-confirm="canConfirm" :can-cancel="canCancel">
          <!-- 預設按鈕 -->
          <div class="flex items-center justify-end space-x-3 w-full">
            <!-- 取消按鈕 -->
            <Button
              v-if="showCancel"
              variant="outline"
              :disabled="!canCancel"
              @click="handleCancel"
            >
              {{ cancelText }}
            </Button>
            
            <!-- 確認按鈕 -->
            <Button
              :variant="confirmVariant"
              :disabled="!canConfirm"
              @click="handleConfirm"
            >
              <Loader2 
                v-if="isLoading" 
                class="w-4 h-4 mr-2 animate-spin" 
              />
              {{ confirmText }}
            </Button>
          </div>
        </slot>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { 
  Loader2, 
  AlertCircle, 
  Info, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  HelpCircle 
} from 'lucide-vue-next'
import { useRegionTheme } from '@/composables/useRegionTheme'
import type { DialogType, DialogSize } from '@/composables/dialogs/useDialogState'

// ============================================================================
// 類型定義
// ============================================================================

interface Props {
  /** 是否開啟 */
  open: boolean
  /** 對話框類型 */
  type?: DialogType
  /** 對話框大小 */
  size?: DialogSize
  /** 對話框標題 */
  title: string
  /** 對話框描述 */
  description?: string
  /** 確認按鈕文字 */
  confirmText?: string
  /** 取消按鈕文字 */
  cancelText?: string
  /** 是否顯示取消按鈕 */
  showCancel?: boolean
  /** 是否顯示標題區 */
  showHeader?: boolean
  /** 是否顯示底部操作區 */
  showFooter?: boolean
  /** 是否載入中 */
  loading?: boolean
  /** 錯誤訊息 */
  error?: string | null
  /** 自定義圖示 */
  icon?: any
  /** 是否可點擊外部關閉 */
  closeOnOutsideClick?: boolean
  /** 確認按鈕變體 */
  confirmVariant?: 'default' | 'destructive' | 'outline' | 'secondary'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  size: 'md',
  confirmText: '確認',
  cancelText: '取消',
  showCancel: true,
  showHeader: true,
  showFooter: true,
  loading: false,
  closeOnOutsideClick: true,
  confirmVariant: 'default',
})

// ============================================================================
// Emits
// ============================================================================

const emit = defineEmits<{
  'update:open': [value: boolean]
  'confirm': []
  'cancel': []
  'close': []
}>()

// ============================================================================
// Composables
// ============================================================================

const { isAdmin } = useRegionTheme()

// ============================================================================
// 計算屬性
// ============================================================================

const isOpen = computed(() => props.open)
const isLoading = computed(() => props.loading)
const error = computed(() => props.error)

const canConfirm = computed(() => !props.loading)
const canCancel = computed(() => !props.loading)

// 圖示組件
const iconComponent = computed(() => {
  if (props.icon) return props.icon
  
  switch (props.type) {
    case 'info': return Info
    case 'success': return CheckCircle
    case 'warning': return AlertTriangle
    case 'error': return XCircle
    case 'confirm': return HelpCircle
    default: return null
  }
})

// 樣式類別
const dialogClasses = computed(() => {
  const baseClasses = ['gap-0']
  
  // 大小
  switch (props.size) {
    case 'xs':
      baseClasses.push('max-w-xs')
      break
    case 'sm':
      baseClasses.push('max-w-sm')
      break
    case 'md':
      baseClasses.push('max-w-md')
      break
    case 'lg':
      baseClasses.push('max-w-lg')
      break
    case 'xl':
      baseClasses.push('max-w-xl')
      break
    case 'full':
      baseClasses.push('max-w-4xl w-full')
      break
  }
  
  return baseClasses.join(' ')
})

const headerClasses = computed(() => {
  const baseClasses = ['p-6 pb-4']
  
  if (isAdmin.value) {
    baseClasses.push('border-b border-gray-200 dark:border-gray-700')
  } else {
    baseClasses.push('border-b border-zinc-200/50 dark:border-zinc-800/50')
  }
  
  return baseClasses.join(' ')
})

const iconContainerClasses = computed(() => {
  const baseClasses = ['flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center']
  
  switch (props.type) {
    case 'info':
      baseClasses.push('bg-blue-100 dark:bg-blue-900/20')
      break
    case 'success':
      baseClasses.push('bg-green-100 dark:bg-green-900/20')
      break
    case 'warning':
      baseClasses.push('bg-yellow-100 dark:bg-yellow-900/20')
      break
    case 'error':
      baseClasses.push('bg-red-100 dark:bg-red-900/20')
      break
    case 'confirm':
      baseClasses.push('bg-gray-100 dark:bg-gray-800')
      break
    default:
      baseClasses.push('bg-gray-100 dark:bg-gray-800')
  }
  
  return baseClasses.join(' ')
})

const iconClasses = computed(() => {
  const baseClasses = ['w-5 h-5']
  
  switch (props.type) {
    case 'info':
      baseClasses.push('text-blue-600 dark:text-blue-400')
      break
    case 'success':
      baseClasses.push('text-green-600 dark:text-green-400')
      break
    case 'warning':
      baseClasses.push('text-yellow-600 dark:text-yellow-400')
      break
    case 'error':
      baseClasses.push('text-red-600 dark:text-red-400')
      break
    case 'confirm':
      baseClasses.push('text-gray-600 dark:text-gray-400')
      break
    default:
      baseClasses.push('text-gray-600 dark:text-gray-400')
  }
  
  return baseClasses.join(' ')
})

const titleClasses = computed(() => {
  return isAdmin.value 
    ? 'text-lg font-semibold text-gray-900 dark:text-gray-100'
    : 'text-lg font-semibold text-zinc-900 dark:text-zinc-100'
})

const descriptionClasses = computed(() => {
  return 'text-sm text-muted-foreground mt-1'
})

const contentClasses = computed(() => {
  const baseClasses = ['px-6']
  
  if (props.showFooter) {
    baseClasses.push('pb-4')
  } else {
    baseClasses.push('pb-6')
  }
  
  return baseClasses.join(' ')
})

const errorClasses = computed(() => {
  return 'flex items-center p-3 mb-4 text-sm text-red-800 bg-red-50 rounded-lg dark:bg-red-900/20 dark:text-red-400'
})

const footerClasses = computed(() => {
  const baseClasses = ['px-6 pb-6 pt-2']
  
  if (isAdmin.value) {
    baseClasses.push('border-t border-gray-200 dark:border-gray-700')
  } else {
    baseClasses.push('border-t border-zinc-200/50 dark:border-zinc-800/50')
  }
  
  return baseClasses.join(' ')
})

// ============================================================================
// 方法
// ============================================================================

const handleOpenChange = (open: boolean) => {
  emit('update:open', open)
  if (!open) {
    emit('close')
  }
}

const handleConfirm = () => {
  if (canConfirm.value) {
    emit('confirm')
  }
}

const handleCancel = () => {
  if (canCancel.value) {
    emit('cancel')
  }
}

const handleOutsideClick = (event: Event) => {
  if (!props.closeOnOutsideClick) {
    event.preventDefault()
  }
}

const clearError = () => {
  // 這個方法會通過插槽傳遞給父組件
}

const setLoading = (loading: boolean) => {
  // 這個方法會通過插槽傳遞給父組件
}
</script>
