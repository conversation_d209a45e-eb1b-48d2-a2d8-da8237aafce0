<!--
  統一表單對話框組件
  
  整合表單驗證、提交處理和錯誤管理的統一表單對話框
  支援 Dialog 和 Sheet 兩種模式
  基於 UnifiedForm 組件提供完整的表單功能
-->

<template>
  <!-- Dialog 模式 -->
  <UnifiedDialog
    v-if="type === 'dialog'"
    :open="isOpen"
    :type="dialogType"
    :size="size"
    :title="title"
    :description="description"
    :confirm-text="submitText"
    :cancel-text="cancelText"
    :show-cancel="showCancel"
    :loading="isSubmitting"
    :error="error"
    :close-on-outside-click="closeOnOutsideClick"
    :confirm-variant="confirmVariant"
    @update:open="handleOpenChange"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="py-4">
      <slot 
        :values="values"
        :errors="formErrors"
        :is-valid="isValid"
        :is-dirty="isDirty"
        :is-readonly="isReadonly"
        :set-field-value="setFieldValue"
        :validate-field="validateField"
        :clear-field-error="clearFieldError"
      />
    </div>
  </UnifiedDialog>

  <!-- Sheet 模式 -->
  <UnifiedSheet
    v-else
    :open="isOpen"
    :side="sheetSide"
    :size="sheetSize"
    :mode="mode"
    :title="title"
    :description="description"
    :confirm-text="submitText"
    :cancel-text="cancelText"
    :show-cancel="showCancel"
    :loading="isSubmitting"
    :error="error"
    :close-on-outside-click="closeOnOutsideClick"
    :confirm-variant="confirmVariant"
    @update:open="handleOpenChange"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <slot 
      :values="values"
      :errors="formErrors"
      :is-valid="isValid"
      :is-dirty="isDirty"
      :is-readonly="isReadonly"
      :set-field-value="setFieldValue"
      :validate-field="validateField"
      :clear-field-error="clearFieldError"
    />

    <template #secondary-actions>
      <slot name="secondary-actions" />
    </template>
  </UnifiedSheet>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import UnifiedDialog from './UnifiedDialog.vue'
import UnifiedSheet from './UnifiedSheet.vue'
import { useFormDialog, type FormDialogOptions, type FormDialogMode } from '@/composables/dialogs/useFormDialog'
import type { DialogType, DialogSize } from '@/composables/dialogs/useDialogState'

// ============================================================================
// 類型定義
// ============================================================================

interface Props<T = any> {
  /** 是否開啟 */
  open: boolean
  /** 對話框類型 */
  type?: 'dialog' | 'sheet'
  /** 對話框模式 */
  mode?: FormDialogMode
  /** 對話框大小 */
  size?: DialogSize
  /** Sheet 側邊 */
  sheetSide?: 'top' | 'right' | 'bottom' | 'left'
  /** Sheet 大小 */
  sheetSize?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  /** 對話框標題 */
  title: string
  /** 對話框描述 */
  description?: string
  /** 表單初始值 */
  initialValues: T
  /** 提交按鈕文字 */
  submitText?: string
  /** 取消按鈕文字 */
  cancelText?: string
  /** 是否顯示取消按鈕 */
  showCancel?: boolean
  /** 是否可點擊外部關閉 */
  closeOnOutsideClick?: boolean
  /** 確認按鈕變體 */
  confirmVariant?: 'default' | 'destructive' | 'outline' | 'secondary'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'sheet',
  mode: 'create',
  size: 'md',
  sheetSide: 'right',
  sheetSize: 'lg',
  submitText: '確認',
  cancelText: '取消',
  showCancel: true,
  closeOnOutsideClick: false,
  confirmVariant: 'default',
})

// ============================================================================
// Emits
// ============================================================================

const emit = defineEmits<{
  'update:open': [value: boolean]
  'submit': [values: any]
  'cancel': []
  'success': [result: any, values: any]
  'error': [error: any]
}>()

// ============================================================================
// Composables
// ============================================================================

const formDialog = useFormDialog()

// ============================================================================
// 計算屬性
// ============================================================================

const isOpen = computed(() => props.open)
const mode = computed(() => props.mode)
const type = computed(() => props.type)
const title = computed(() => props.title)
const description = computed(() => props.description)
const submitText = computed(() => {
  if (props.submitText !== '確認') return props.submitText
  
  switch (props.mode) {
    case 'create': return '建立'
    case 'edit': return '更新'
    case 'view': return '關閉'
    default: return '確認'
  }
})
const cancelText = computed(() => props.cancelText)
const showCancel = computed(() => props.showCancel && props.mode !== 'view')
const closeOnOutsideClick = computed(() => props.closeOnOutsideClick)
const confirmVariant = computed(() => props.confirmVariant)
const size = computed(() => props.size)
const sheetSide = computed(() => props.sheetSide)
const sheetSize = computed(() => props.sheetSize)

// 表單狀態
const values = computed(() => formDialog.values.value)
const formErrors = computed(() => formDialog.formErrors.value)
const isValid = computed(() => formDialog.isValid.value)
const isDirty = computed(() => formDialog.isDirty.value)
const isSubmitting = computed(() => formDialog.isSubmitting.value)
const error = computed(() => formDialog.error.value)
const isReadonly = computed(() => formDialog.isReadonly.value)

// 對話框類型（用於 Dialog 模式）
const dialogType = computed<DialogType>(() => {
  switch (props.mode) {
    case 'create': return 'info'
    case 'edit': return 'info'
    case 'view': return 'info'
    default: return 'info'
  }
})

// ============================================================================
// 方法
// ============================================================================

const handleOpenChange = (open: boolean) => {
  emit('update:open', open)
}

const handleSubmit = async () => {
  if (props.mode === 'view') {
    handleCancel()
    return
  }

  try {
    await formDialog.submit()
  } catch (error) {
    // 錯誤已在 formDialog 中處理
  }
}

const handleCancel = () => {
  formDialog.cancel()
  emit('cancel')
}

const setFieldValue = (field: string, value: any) => {
  formDialog.setFieldValue(field, value)
}

const validateField = (field: string) => {
  return formDialog.validateField(field)
}

const clearFieldError = (field: string) => {
  formDialog.clearFieldError(field)
}

// ============================================================================
// 監聽器
// ============================================================================

// 監聽 open 狀態變化
watch(isOpen, async (newOpen) => {
  if (newOpen) {
    // 開啟表單對話框
    const options: FormDialogOptions = {
      mode: props.mode,
      type: props.type,
      title: props.title,
      description: props.description,
      initialValues: props.initialValues,
      onSubmit: async (values) => {
        emit('submit', values)
        return values
      },
      onSuccess: (result, values) => {
        emit('success', result, values)
      },
      onError: (error) => {
        emit('error', error)
      },
    }

    try {
      await formDialog.open(options)
    } catch (error) {
      console.error('表單對話框開啟失敗:', error)
    }
  } else {
    // 關閉表單對話框
    formDialog.close()
  }
})

// ============================================================================
// 暴露給父組件的方法
// ============================================================================

defineExpose({
  // 表單狀態
  values,
  formErrors,
  isValid,
  isDirty,
  isSubmitting,
  error,
  isReadonly,
  
  // 表單方法
  setFieldValue,
  validateField,
  clearFieldError,
  submit: handleSubmit,
  cancel: handleCancel,
  
  // 對話框方法
  close: () => formDialog.close(),
  reset: () => formDialog.reset(),
  setError: (error: string) => formDialog.setError(error),
  setLoading: (loading: boolean) => formDialog.setLoading(loading),
})
</script>
