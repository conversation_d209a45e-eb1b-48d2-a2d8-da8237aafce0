<!--
  統一 Sheet 組件
  
  基於 shadcn/ui Sheet 組件的統一側邊欄對話框解決方案
  適用於 CRUD 操作、複雜表單、資料篩選等場景
  支援區域主題差異和完整的載入狀態管理
-->

<template>
  <Sheet :open="isOpen" @update:open="handleOpenChange">
    <SheetContent 
      :side="side"
      :class="sheetClasses"
      :close-on-outside-click="closeOnOutsideClick"
    >
      <!-- Sheet 標題區 -->
      <SheetHeader v-if="showHeader" :class="headerClasses">
        <div class="flex items-center space-x-3">
          <!-- 圖示 -->
          <div v-if="iconComponent" :class="iconContainerClasses">
            <component :is="iconComponent" :class="iconClasses" />
          </div>
          
          <!-- 標題和描述 -->
          <div class="flex-1 min-w-0">
            <SheetTitle :class="titleClasses">
              {{ title }}
            </SheetTitle>
            <SheetDescription v-if="description" :class="descriptionClasses">
              {{ description }}
            </SheetDescription>
          </div>
          
          <!-- 載入指示器 -->
          <div v-if="isLoading" class="flex-shrink-0">
            <Loader2 class="w-4 h-4 animate-spin text-muted-foreground" />
          </div>
        </div>
      </SheetHeader>

      <!-- Sheet 內容區 -->
      <div :class="contentClasses">
        <!-- 錯誤訊息 -->
        <div v-if="error" :class="errorClasses">
          <AlertCircle class="w-4 h-4 mr-2 flex-shrink-0" />
          <span>{{ error }}</span>
        </div>

        <!-- 主要內容插槽 -->
        <slot 
          :is-loading="isLoading"
          :error="error"
          :clear-error="clearError"
          :set-loading="setLoading"
        />
      </div>

      <!-- Sheet 操作區 -->
      <SheetFooter v-if="showFooter" :class="footerClasses">
        <slot name="footer" :is-loading="isLoading" :can-confirm="canConfirm" :can-cancel="canCancel">
          <!-- 預設按鈕 -->
          <div class="flex items-center justify-between w-full">
            <!-- 左側：次要操作 -->
            <div class="flex items-center space-x-2">
              <slot name="secondary-actions" />
            </div>
            
            <!-- 右側：主要操作 -->
            <div class="flex items-center space-x-3">
              <!-- 取消按鈕 -->
              <Button
                v-if="showCancel"
                variant="outline"
                :disabled="!canCancel"
                @click="handleCancel"
              >
                {{ cancelText }}
              </Button>
              
              <!-- 確認按鈕 -->
              <Button
                :variant="confirmVariant"
                :disabled="!canConfirm"
                @click="handleConfirm"
              >
                <Loader2 
                  v-if="isLoading" 
                  class="w-4 h-4 mr-2 animate-spin" 
                />
                {{ confirmText }}
              </Button>
            </div>
          </div>
        </slot>
      </SheetFooter>
    </SheetContent>
  </Sheet>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Sheet, 
  SheetContent, 
  SheetDescription, 
  SheetFooter, 
  SheetHeader, 
  SheetTitle 
} from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import { 
  Loader2, 
  AlertCircle, 
  Plus, 
  Edit, 
  Eye, 
  Filter,
  Settings 
} from 'lucide-vue-next'
import { useRegionTheme } from '@/composables/useRegionTheme'
import type { FormDialogMode } from '@/composables/dialogs/useFormDialog'

// ============================================================================
// 類型定義
// ============================================================================

type SheetSide = 'top' | 'right' | 'bottom' | 'left'
type SheetSize = 'sm' | 'md' | 'lg' | 'xl' | 'full'

interface Props {
  /** 是否開啟 */
  open: boolean
  /** Sheet 側邊 */
  side?: SheetSide
  /** Sheet 大小 */
  size?: SheetSize
  /** Sheet 模式 */
  mode?: FormDialogMode | 'filter' | 'settings' | 'custom'
  /** Sheet 標題 */
  title: string
  /** Sheet 描述 */
  description?: string
  /** 確認按鈕文字 */
  confirmText?: string
  /** 取消按鈕文字 */
  cancelText?: string
  /** 是否顯示取消按鈕 */
  showCancel?: boolean
  /** 是否顯示標題區 */
  showHeader?: boolean
  /** 是否顯示底部操作區 */
  showFooter?: boolean
  /** 是否載入中 */
  loading?: boolean
  /** 錯誤訊息 */
  error?: string | null
  /** 自定義圖示 */
  icon?: any
  /** 是否可點擊外部關閉 */
  closeOnOutsideClick?: boolean
  /** 確認按鈕變體 */
  confirmVariant?: 'default' | 'destructive' | 'outline' | 'secondary'
}

const props = withDefaults(defineProps<Props>(), {
  side: 'right',
  size: 'lg',
  mode: 'create',
  confirmText: '確認',
  cancelText: '取消',
  showCancel: true,
  showHeader: true,
  showFooter: true,
  loading: false,
  closeOnOutsideClick: false, // Sheet 預設不可點擊外部關閉
  confirmVariant: 'default',
})

// ============================================================================
// Emits
// ============================================================================

const emit = defineEmits<{
  'update:open': [value: boolean]
  'confirm': []
  'cancel': []
  'close': []
}>()

// ============================================================================
// Composables
// ============================================================================

const { isAdmin } = useRegionTheme()

// ============================================================================
// 計算屬性
// ============================================================================

const isOpen = computed(() => props.open)
const isLoading = computed(() => props.loading)
const error = computed(() => props.error)

const canConfirm = computed(() => !props.loading)
const canCancel = computed(() => !props.loading)

// 圖示組件
const iconComponent = computed(() => {
  if (props.icon) return props.icon
  
  switch (props.mode) {
    case 'create': return Plus
    case 'edit': return Edit
    case 'view': return Eye
    case 'filter': return Filter
    case 'settings': return Settings
    default: return null
  }
})

// 樣式類別
const sheetClasses = computed(() => {
  const baseClasses = ['flex flex-col overflow-hidden']
  
  // 大小
  switch (props.size) {
    case 'sm':
      baseClasses.push('w-full sm:max-w-sm')
      break
    case 'md':
      baseClasses.push('w-full sm:max-w-md')
      break
    case 'lg':
      baseClasses.push('w-full sm:max-w-lg')
      break
    case 'xl':
      baseClasses.push('w-full sm:max-w-xl')
      break
    case 'full':
      baseClasses.push('w-full sm:max-w-4xl')
      break
  }
  
  return baseClasses.join(' ')
})

const headerClasses = computed(() => {
  const baseClasses = ['p-6 pb-4 flex-shrink-0']
  
  if (isAdmin.value) {
    baseClasses.push('border-b border-gray-200 dark:border-gray-700')
  } else {
    baseClasses.push('border-b border-zinc-200/50 dark:border-zinc-800/50')
  }
  
  return baseClasses.join(' ')
})

const iconContainerClasses = computed(() => {
  const baseClasses = ['flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center']
  
  switch (props.mode) {
    case 'create':
      baseClasses.push('bg-green-100 dark:bg-green-900/20')
      break
    case 'edit':
      baseClasses.push('bg-blue-100 dark:bg-blue-900/20')
      break
    case 'view':
      baseClasses.push('bg-gray-100 dark:bg-gray-800')
      break
    case 'filter':
      baseClasses.push('bg-purple-100 dark:bg-purple-900/20')
      break
    case 'settings':
      baseClasses.push('bg-orange-100 dark:bg-orange-900/20')
      break
    default:
      baseClasses.push('bg-gray-100 dark:bg-gray-800')
  }
  
  return baseClasses.join(' ')
})

const iconClasses = computed(() => {
  const baseClasses = ['w-5 h-5']
  
  switch (props.mode) {
    case 'create':
      baseClasses.push('text-green-600 dark:text-green-400')
      break
    case 'edit':
      baseClasses.push('text-blue-600 dark:text-blue-400')
      break
    case 'view':
      baseClasses.push('text-gray-600 dark:text-gray-400')
      break
    case 'filter':
      baseClasses.push('text-purple-600 dark:text-purple-400')
      break
    case 'settings':
      baseClasses.push('text-orange-600 dark:text-orange-400')
      break
    default:
      baseClasses.push('text-gray-600 dark:text-gray-400')
  }
  
  return baseClasses.join(' ')
})

const titleClasses = computed(() => {
  return isAdmin.value 
    ? 'text-lg font-semibold text-gray-900 dark:text-gray-100'
    : 'text-lg font-semibold text-zinc-900 dark:text-zinc-100'
})

const descriptionClasses = computed(() => {
  return 'text-sm text-muted-foreground mt-1'
})

const contentClasses = computed(() => {
  return 'flex-1 overflow-y-auto px-6 py-4'
})

const errorClasses = computed(() => {
  return 'flex items-center p-3 mb-4 text-sm text-red-800 bg-red-50 rounded-lg dark:bg-red-900/20 dark:text-red-400'
})

const footerClasses = computed(() => {
  const baseClasses = ['p-6 pt-4 flex-shrink-0']
  
  if (isAdmin.value) {
    baseClasses.push('border-t border-gray-200 dark:border-gray-700')
  } else {
    baseClasses.push('border-t border-zinc-200/50 dark:border-zinc-800/50')
  }
  
  return baseClasses.join(' ')
})

// ============================================================================
// 方法
// ============================================================================

const handleOpenChange = (open: boolean) => {
  emit('update:open', open)
  if (!open) {
    emit('close')
  }
}

const handleConfirm = () => {
  if (canConfirm.value) {
    emit('confirm')
  }
}

const handleCancel = () => {
  if (canCancel.value) {
    emit('cancel')
  }
}

const clearError = () => {
  // 這個方法會通過插槽傳遞給父組件
}

const setLoading = (loading: boolean) => {
  // 這個方法會通過插槽傳遞給父組件
}
</script>
