/**
 * HorizAI 統一對話框系統
 *
 * 導出所有對話框相關組件和工具函數
 * 提供統一的模態框、Sheet 和表單對話框解決方案
 *
 * <AUTHOR> Design System Team
 * @version 1.0.0
 */

// ============================================================================
// 組件導出
// ============================================================================

export { default as UnifiedDialog } from './UnifiedDialog.vue'
export { default as UnifiedSheet } from './UnifiedSheet.vue'
export { default as UnifiedFormDialog } from './UnifiedFormDialog.vue'

// ============================================================================
// Composables 導出
// ============================================================================

export {
  useDialogState,
  type DialogType,
  type DialogSize,
  type DialogPosition,
  type DialogOptions,
  type DialogState,
  type DialogActions,
} from '@/composables/dialogs/useDialogState'

export {
  useConfirmDialog,
  type ConfirmDialogOptions,
  type DeleteConfirmOptions,
  type AsyncConfirmOptions,
} from '@/composables/dialogs/useConfirmDialog'

export {
  useFormDialog,
  type FormDialogMode,
  type FormDialogType,
  type FormDialogOptions,
  type FormDialogState,
} from '@/composables/dialogs/useFormDialog'

// ============================================================================
// 全域對話框服務
// ============================================================================

import { useConfirmDialog } from '@/composables/dialogs/useConfirmDialog'
import { useDialogState } from '@/composables/dialogs/useDialogState'

// 全域對話框實例
let globalConfirmDialog: ReturnType<typeof useConfirmDialog> | null = null
let globalDialogState: ReturnType<typeof useDialogState> | null = null

/**
 * 初始化全域對話框服務
 */
export function initializeDialogService() {
  if (!globalConfirmDialog) {
    globalConfirmDialog = useConfirmDialog()
  }
  if (!globalDialogState) {
    globalDialogState = useDialogState()
  }
}

/**
 * 全域確認對話框服務
 */
export const $confirm = {
  // 基本確認
  confirm: (title: string, description?: string) => {
    initializeDialogService()
    return globalConfirmDialog!.confirm({ title, description })
  },

  // 刪除確認
  delete: (itemName: string, onDelete: () => Promise<void>, itemType = '項目') => {
    initializeDialogService()
    return globalConfirmDialog!.confirmDelete({ itemName, itemType, onDelete })
  },

  // 狀態變更確認
  statusChange: (itemName: string, fromStatus: string, toStatus: string, changeAction: () => Promise<void>) => {
    initializeDialogService()
    return globalConfirmDialog!.confirmStatusChange(itemName, fromStatus, toStatus, changeAction)
  },

  // 批量操作確認
  batch: (items: any[], action: string, batchAction: (items: any[]) => Promise<void>) => {
    initializeDialogService()
    return globalConfirmDialog!.confirmBatch(items, action, batchAction)
  },

  // 離開頁面確認
  leave: (hasUnsavedChanges = true) => {
    initializeDialogService()
    return globalConfirmDialog!.confirmLeave(hasUnsavedChanges)
  },

  // 重置確認
  reset: (resetAction: () => void) => {
    initializeDialogService()
    return globalConfirmDialog!.confirmReset(resetAction)
  },
}

/**
 * 全域訊息對話框服務
 */
export const $dialog = {
  // 資訊對話框
  info: (title: string, description?: string) => {
    initializeDialogService()
    return globalDialogState!.showInfo(title, description)
  },

  // 成功對話框
  success: (title: string, description?: string) => {
    initializeDialogService()
    return globalDialogState!.showSuccess(title, description)
  },

  // 警告對話框
  warning: (title: string, description?: string) => {
    initializeDialogService()
    return globalDialogState!.showWarning(title, description)
  },

  // 錯誤對話框
  error: (title: string, description?: string) => {
    initializeDialogService()
    return globalDialogState!.showError(title, description)
  },
}

// ============================================================================
// 常用對話框配置
// ============================================================================

/**
 * 使用者操作確認配置
 */
export const userDialogConfigs = {
  delete: (userName: string) => ({
    title: '刪除使用者',
    description: `確定要刪除使用者「${userName}」嗎？此操作無法復原，該使用者的所有資料將被永久刪除。`,
    confirmText: '刪除',
    type: 'error' as const,
    isDangerous: true,
  }),

  deactivate: (userName: string) => ({
    title: '停用使用者',
    description: `確定要停用使用者「${userName}」嗎？停用後該使用者將無法登入系統。`,
    confirmText: '停用',
    type: 'warning' as const,
  }),

  activate: (userName: string) => ({
    title: '啟用使用者',
    description: `確定要啟用使用者「${userName}」嗎？啟用後該使用者將可以正常登入系統。`,
    confirmText: '啟用',
    type: 'info' as const,
  }),

  resetPassword: (userName: string) => ({
    title: '重置密碼',
    description: `確定要重置使用者「${userName}」的密碼嗎？系統將生成新的臨時密碼並發送給使用者。`,
    confirmText: '重置',
    type: 'warning' as const,
  }),
}

// ============================================================================
// 表單對話框配置
// ============================================================================

/**
 * 創建基本的表單對話框配置
 */
export function createFormDialogConfig<T>(options: {
  mode: FormDialogMode
  title: string
  description?: string
  type?: 'dialog' | 'sheet'
  size?: DialogSize | 'sm' | 'md' | 'lg' | 'xl' | 'full'
}) {
  return {
    type: options.type || 'sheet',
    mode: options.mode,
    title: options.title,
    description: options.description,
    size: options.size || 'lg',
    showCancel: options.mode !== 'view',
    closeOnOutsideClick: false,
  }
}

/**
 * 獲取區域特定的對話框配置
 */
export function getRegionDialogConfig(region: 'admin' | 'workspace') {
  if (region === 'admin') {
    return {
      type: 'sheet' as const,
      size: 'lg' as const,
      sheetSide: 'right' as const,
      closeOnOutsideClick: false,
    }
  } else {
    return {
      type: 'dialog' as const,
      size: 'md' as const,
      closeOnOutsideClick: true,
    }
  }
}

// ============================================================================
// 常數導出
// ============================================================================

export const DIALOG_TYPES = ['info', 'success', 'warning', 'error', 'confirm', 'custom'] as const
export const DIALOG_SIZES = ['xs', 'sm', 'md', 'lg', 'xl', 'full'] as const
export const DIALOG_POSITIONS = ['center', 'top', 'bottom'] as const
export const FORM_DIALOG_MODES = ['create', 'edit', 'view'] as const
export const FORM_DIALOG_TYPES = ['dialog', 'sheet'] as const


/**
 * 租戶操作確認配置
 */
export const tenantDialogConfigs = {
  delete: (tenantName: string) => ({
    title: '刪除租戶',
    description: `確定要刪除租戶「${tenantName}」嗎？此操作無法復原，該租戶的所有資料（包括使用者、訂單等）將被永久刪除。`,
    confirmText: '刪除',
    type: 'error' as const,
    isDangerous: true,
  }),

  suspend: (tenantName: string) => ({
    title: '暫停租戶',
    description: `確定要暫停租戶「${tenantName}」嗎？暫停後該租戶的所有使用者將無法登入系統。`,
    confirmText: '暫停',
    type: 'warning' as const,
  }),

  activate: (tenantName: string) => ({
    title: '啟用租戶',
    description: `確定要啟用租戶「${tenantName}」嗎？啟用後該租戶的使用者將可以正常使用系統。`,
    confirmText: '啟用',
    type: 'info' as const,
  }),
}

/**
 * 訂單操作確認配置
 */
export const orderDialogConfigs = {
  cancel: (orderNumber: string) => ({
    title: '取消訂單',
    description: `確定要取消訂單「${orderNumber}」嗎？取消後將無法恢復，如已付款將進行退款處理。`,
    confirmText: '取消訂單',
    type: 'warning' as const,
  }),

  refund: (orderNumber: string, amount: number) => ({
    title: '退款處理',
    description: `確定要對訂單「${orderNumber}」進行退款嗎？退款金額：NT$ ${amount.toLocaleString()}`,
    confirmText: '確認退款',
    type: 'warning' as const,
  }),

  approve: (orderNumber: string) => ({
    title: '審核通過',
    description: `確定要審核通過訂單「${orderNumber}」嗎？通過後將開始提供服務。`,
    confirmText: '審核通過',
    type: 'info' as const,
  }),
}

// ============================================================================
// 表單對話框配置
// ============================================================================

/**
 * 創建基本的表單對話框配置
 */
export function createFormDialogConfig<T>(options: {
  mode: FormDialogMode
  title: string
  description?: string
  type?: 'dialog' | 'sheet'
  size?: DialogSize | 'sm' | 'md' | 'lg' | 'xl' | 'full'
}) {
  return {
    type: options.type || 'sheet',
    mode: options.mode,
    title: options.title,
    description: options.description,
    size: options.size || 'lg',
    showCancel: options.mode !== 'view',
    closeOnOutsideClick: false,
  }
}

/**
 * 獲取區域特定的對話框配置
 */
export function getRegionDialogConfig(region: 'admin' | 'workspace') {
  if (region === 'admin') {
    return {
      type: 'sheet' as const,
      size: 'lg' as const,
      sheetSide: 'right' as const,
      closeOnOutsideClick: false,
    }
  } else {
    return {
      type: 'dialog' as const,
      size: 'md' as const,
      closeOnOutsideClick: true,
    }
  }
}

// ============================================================================
// 類型導出
// ============================================================================

export type {
  DialogType,
  DialogSize,
  DialogPosition,
  DialogOptions,
  DialogState,
  DialogActions,
  ConfirmDialogOptions,
  DeleteConfirmOptions,
  AsyncConfirmOptions,
  FormDialogMode,
  FormDialogType,
  FormDialogOptions,
  FormDialogState,
} from '@/composables/dialogs/useDialogState'

// ============================================================================
// 常數導出
// ============================================================================


