<!--
  統一表單欄位組件
  
  基於 shadcn/ui 和 HorizAI Design System
  支援多種輸入類型和驗證狀態
  整合區域主題差異和無障礙設計
-->

<template>
  <FormItem :class="containerClasses">
    <!-- 標籤 -->
    <FormLabel 
      v-if="label"
      :for="fieldId"
      :class="labelClasses"
      :required="required"
    >
      {{ label }}
      <span v-if="required" class="text-destructive ml-1" aria-label="必填">*</span>
    </FormLabel>

    <!-- 輸入控制項 -->
    <FormControl>
      <!-- 文字輸入 -->
      <Input
        v-if="type === 'text' || type === 'email' || type === 'password'"
        :id="fieldId"
        :type="type"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :class="inputClasses"
        :value="modelValue"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
        :aria-describedby="ariaDescribedBy"
        :aria-invalid="hasError"
      />

      <!-- 數字輸入 -->
      <Input
        v-else-if="type === 'number'"
        :id="fieldId"
        type="number"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :class="inputClasses"
        :value="modelValue"
        :min="min"
        :max="max"
        :step="step"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
        :aria-describedby="ariaDescribedBy"
        :aria-invalid="hasError"
      />

      <!-- 文字區域 -->
      <Textarea
        v-else-if="type === 'textarea'"
        :id="fieldId"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :class="textareaClasses"
        :value="modelValue"
        :rows="rows"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
        :aria-describedby="ariaDescribedBy"
        :aria-invalid="hasError"
      />

      <!-- 選擇器 -->
      <Select
        v-else-if="type === 'select'"
        :value="modelValue"
        @update:value="handleSelectChange"
        :disabled="disabled"
      >
        <SelectTrigger 
          :id="fieldId"
          :class="selectClasses"
          :aria-describedby="ariaDescribedBy"
          :aria-invalid="hasError"
        >
          <SelectValue :placeholder="placeholder" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem
            v-for="option in options"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </SelectItem>
        </SelectContent>
      </Select>

      <!-- 複選框 -->
      <div v-else-if="type === 'checkbox'" class="flex items-center space-x-2">
        <Checkbox
          :id="fieldId"
          :checked="modelValue"
          :disabled="disabled"
          @update:checked="handleCheckboxChange"
          :aria-describedby="ariaDescribedBy"
          :aria-invalid="hasError"
        />
        <Label
          v-if="checkboxLabel"
          :for="fieldId"
          :class="checkboxLabelClasses"
        >
          {{ checkboxLabel }}
        </Label>
      </div>

      <!-- 單選按鈕組 -->
      <RadioGroup
        v-else-if="type === 'radio'"
        :value="modelValue"
        @update:value="handleRadioChange"
        :disabled="disabled"
        :class="radioGroupClasses"
      >
        <div
          v-for="option in options"
          :key="option.value"
          class="flex items-center space-x-2"
        >
          <RadioGroupItem
            :id="`${fieldId}-${option.value}`"
            :value="option.value"
            :aria-describedby="ariaDescribedBy"
          />
          <Label :for="`${fieldId}-${option.value}`">
            {{ option.label }}
          </Label>
        </div>
      </RadioGroup>

      <!-- 日期輸入 -->
      <Input
        v-else-if="type === 'date'"
        :id="fieldId"
        type="date"
        :disabled="disabled"
        :readonly="readonly"
        :class="inputClasses"
        :value="modelValue"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
        :aria-describedby="ariaDescribedBy"
        :aria-invalid="hasError"
      />
    </FormControl>

    <!-- 說明文字 -->
    <FormDescription v-if="description" :class="descriptionClasses">
      {{ description }}
    </FormDescription>

    <!-- 錯誤訊息 -->
    <FormMessage v-if="hasError" :class="errorClasses">
      {{ error }}
    </FormMessage>
  </FormItem>
</template>

<script setup lang="ts">
import { computed, useId } from 'vue'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  FormControl,
  FormDescription,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { useRegionTheme } from '@/composables/useRegionTheme'

// ============================================================================
// 類型定義
// ============================================================================

interface SelectOption {
  label: string
  value: string | number
}

interface Props {
  // 基本屬性
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'date'
  modelValue?: any
  label?: string
  placeholder?: string
  description?: string
  error?: string
  
  // 狀態
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  
  // 選項（用於 select 和 radio）
  options?: SelectOption[]
  
  // 數字輸入專用
  min?: number
  max?: number
  step?: number
  
  // 文字區域專用
  rows?: number
  
  // 複選框專用
  checkboxLabel?: string
  
  // 樣式
  size?: 'sm' | 'default' | 'lg'
  variant?: 'default' | 'outline'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  size: 'default',
  variant: 'default',
  rows: 3,
  required: false,
  disabled: false,
  readonly: false,
})

// ============================================================================
// Emits
// ============================================================================

const emit = defineEmits<{
  'update:modelValue': [value: any]
  'blur': [event: Event]
  'focus': [event: Event]
}>()

// ============================================================================
// Composables
// ============================================================================

const { getFormClasses } = useRegionTheme()
const fieldId = useId()

// ============================================================================
// 計算屬性
// ============================================================================

const hasError = computed(() => Boolean(props.error))

const ariaDescribedBy = computed(() => {
  const ids = []
  if (props.description) ids.push(`${fieldId}-description`)
  if (hasError.value) ids.push(`${fieldId}-error`)
  return ids.length > 0 ? ids.join(' ') : undefined
})

// 樣式類別
const containerClasses = computed(() => {
  return getFormClasses?.('container') || 'space-y-2'
})

const labelClasses = computed(() => {
  const baseClasses = ['text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70']
  
  if (hasError.value) {
    baseClasses.push('text-destructive')
  }
  
  return baseClasses.join(' ')
})

const inputClasses = computed(() => {
  const baseClasses = [
    'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm',
    'ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium',
    'placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2',
    'focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
  ]
  
  if (hasError.value) {
    baseClasses.push('border-destructive focus-visible:ring-destructive')
  }
  
  if (props.size === 'sm') {
    baseClasses.push('h-8 px-2 text-xs')
  } else if (props.size === 'lg') {
    baseClasses.push('h-12 px-4 text-base')
  }
  
  return baseClasses.join(' ')
})

const textareaClasses = computed(() => {
  const baseClasses = [
    'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm',
    'ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none',
    'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
    'disabled:cursor-not-allowed disabled:opacity-50'
  ]
  
  if (hasError.value) {
    baseClasses.push('border-destructive focus-visible:ring-destructive')
  }
  
  return baseClasses.join(' ')
})

const selectClasses = computed(() => {
  const baseClasses = [
    'flex h-10 w-full items-center justify-between rounded-md border border-input',
    'bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground',
    'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
    'disabled:cursor-not-allowed disabled:opacity-50'
  ]
  
  if (hasError.value) {
    baseClasses.push('border-destructive focus:ring-destructive')
  }
  
  return baseClasses.join(' ')
})

const checkboxLabelClasses = computed(() => {
  return 'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
})

const radioGroupClasses = computed(() => {
  return 'grid gap-2'
})

const descriptionClasses = computed(() => {
  return 'text-sm text-muted-foreground'
})

const errorClasses = computed(() => {
  return 'text-sm font-medium text-destructive'
})

// ============================================================================
// 事件處理
// ============================================================================

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement | HTMLTextAreaElement
  emit('update:modelValue', target.value)
}

const handleBlur = (event: Event) => {
  emit('blur', event)
}

const handleFocus = (event: Event) => {
  emit('focus', event)
}

const handleSelectChange = (value: string) => {
  emit('update:modelValue', value)
}

const handleCheckboxChange = (checked: boolean) => {
  emit('update:modelValue', checked)
}

const handleRadioChange = (value: string) => {
  emit('update:modelValue', value)
}
</script>
