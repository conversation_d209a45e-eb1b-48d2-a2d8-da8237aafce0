<!--
  表單欄位組合組件
  
  提供常見的表單欄位組合模式
  支援響應式佈局和區域主題差異
-->

<template>
  <div :class="containerClasses">
    <!-- 組標題 -->
    <div v-if="title || description" :class="headerClasses">
      <h3 v-if="title" :class="titleClasses">
        {{ title }}
      </h3>
      <p v-if="description" :class="descriptionClasses">
        {{ description }}
      </p>
    </div>

    <!-- 欄位內容 -->
    <div :class="contentClasses">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRegionTheme } from '@/composables/useRegionTheme'

// ============================================================================
// 類型定義
// ============================================================================

interface Props {
  title?: string
  description?: string
  layout?: 'vertical' | 'horizontal' | 'grid-2' | 'grid-3' | 'inline'
  spacing?: 'compact' | 'comfortable'
  border?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  layout: 'vertical',
  border: false,
})

// ============================================================================
// Composables
// ============================================================================

const { isAdmin } = useRegionTheme()

// ============================================================================
// 計算屬性
// ============================================================================

const containerClasses = computed(() => {
  const baseClasses = []
  
  // 邊框
  if (props.border) {
    if (isAdmin.value) {
      baseClasses.push('border border-gray-200 dark:border-gray-700 rounded-lg p-4')
    } else {
      baseClasses.push('border border-zinc-200/50 dark:border-zinc-800/50 rounded-lg p-6')
    }
  }
  
  return baseClasses.join(' ')
})

const headerClasses = computed(() => {
  const spacing = props.spacing || (isAdmin.value ? 'compact' : 'comfortable')
  return spacing === 'compact' ? 'space-y-1 mb-3' : 'space-y-2 mb-4'
})

const titleClasses = computed(() => {
  return 'text-base font-medium text-foreground'
})

const descriptionClasses = computed(() => {
  return 'text-sm text-muted-foreground'
})

const contentClasses = computed(() => {
  const baseClasses = []
  const spacing = props.spacing || (isAdmin.value ? 'compact' : 'comfortable')
  
  switch (props.layout) {
    case 'horizontal':
      baseClasses.push('flex flex-col sm:flex-row sm:items-end')
      baseClasses.push(spacing === 'compact' ? 'gap-3' : 'gap-4')
      break
      
    case 'grid-2':
      baseClasses.push('grid grid-cols-1 md:grid-cols-2')
      baseClasses.push(spacing === 'compact' ? 'gap-3' : 'gap-4')
      break
      
    case 'grid-3':
      baseClasses.push('grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3')
      baseClasses.push(spacing === 'compact' ? 'gap-3' : 'gap-4')
      break
      
    case 'inline':
      baseClasses.push('flex flex-wrap items-end')
      baseClasses.push(spacing === 'compact' ? 'gap-2' : 'gap-3')
      break
      
    default: // vertical
      baseClasses.push(spacing === 'compact' ? 'space-y-3' : 'space-y-4')
  }
  
  return baseClasses.join(' ')
})
</script>
