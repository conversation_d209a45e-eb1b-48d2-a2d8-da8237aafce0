<!--
  表單系統測試頁面
  
  用於測試和展示統一表單系統的功能
  包含各種表單組件和驗證場景
-->

<template>
  <div :class="pageClasses">
    <!-- 頁面標題 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold tracking-tight">統一表單系統測試</h1>
      <p class="text-muted-foreground mt-2">
        測試 HorizAI 統一表單組件和驗證系統
      </p>
    </div>

    <!-- 主題控制 -->
    <Card class="mb-8">
      <CardHeader>
        <CardTitle>主題控制</CardTitle>
        <CardDescription>切換主題和區域以測試樣式差異</CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="flex items-center gap-4">
          <Button @click="toggleTheme" variant="outline">
            <Sun v-if="!isDarkMode" class="w-4 h-4 mr-2" />
            <Moon v-else class="w-4 h-4 mr-2" />
            {{ isDarkMode ? '切換到淺色模式' : '切換到深色模式' }}
          </Button>
          
          <div class="text-sm text-muted-foreground">
            當前區域：{{ currentRegion }} | 主題：{{ themeMode }}
          </div>
        </div>
        
        <div class="grid grid-cols-2 gap-4">
          <Button @click="setRegion('admin')" :variant="isAdmin ? 'default' : 'outline'">
            Admin 區域
          </Button>
          <Button @click="setRegion('workspace')" :variant="isWorkspace ? 'default' : 'outline'">
            Workspace 區域
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 表單測試區域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- 基本表單測試 -->
      <Card>
        <CardHeader>
          <CardTitle>基本表單組件</CardTitle>
          <CardDescription>測試各種表單欄位類型</CardDescription>
        </CardHeader>
        <CardContent>
          <UnifiedForm
            :initial-values="basicFormValues"
            :validation-config="basicValidationConfig"
            submit-text="提交測試"
            :show-cancel="false"
            @submit="handleBasicSubmit"
          >
            <template #default="{ values, errors, setFieldValue, validateField }">
              <FormFieldGroup title="文字輸入" layout="vertical">
                <FormField
                  type="text"
                  label="姓名"
                  placeholder="請輸入姓名"
                  :model-value="values.name"
                  :error="errors.name"
                  required
                  @update:model-value="(value) => setFieldValue('name', value)"
                  @blur="() => validateField('name')"
                />

                <FormField
                  type="email"
                  label="電子郵件"
                  placeholder="請輸入電子郵件"
                  :model-value="values.email"
                  :error="errors.email"
                  required
                  @update:model-value="(value) => setFieldValue('email', value)"
                  @blur="() => validateField('email')"
                />

                <FormField
                  type="password"
                  label="密碼"
                  placeholder="請輸入密碼"
                  :model-value="values.password"
                  :error="errors.password"
                  required
                  description="密碼需包含大小寫字母、數字，至少 8 個字元"
                  @update:model-value="(value) => setFieldValue('password', value)"
                  @blur="() => validateField('password')"
                />
              </FormFieldGroup>

              <FormFieldGroup title="選擇器" layout="grid-2">
                <FormField
                  type="select"
                  label="角色"
                  placeholder="請選擇角色"
                  :model-value="values.role"
                  :error="errors.role"
                  :options="roleOptions"
                  required
                  @update:model-value="(value) => setFieldValue('role', value)"
                />

                <FormField
                  type="select"
                  label="狀態"
                  :model-value="values.status"
                  :options="statusOptions"
                  @update:model-value="(value) => setFieldValue('status', value)"
                />
              </FormFieldGroup>

              <FormFieldGroup title="其他輸入" layout="vertical">
                <FormField
                  type="number"
                  label="年齡"
                  placeholder="請輸入年齡"
                  :model-value="values.age"
                  :error="errors.age"
                  :min="18"
                  :max="100"
                  @update:model-value="(value) => setFieldValue('age', value)"
                />

                <FormField
                  type="textarea"
                  label="備註"
                  placeholder="請輸入備註"
                  :model-value="values.remarks"
                  :rows="3"
                  @update:model-value="(value) => setFieldValue('remarks', value)"
                />

                <FormField
                  type="checkbox"
                  checkbox-label="我同意服務條款"
                  :model-value="values.agreeTerms"
                  @update:model-value="(value) => setFieldValue('agreeTerms', value)"
                />
              </FormFieldGroup>
            </template>
          </UnifiedForm>
        </CardContent>
      </Card>

      <!-- 進階表單測試 -->
      <Card>
        <CardHeader>
          <CardTitle>進階表單功能</CardTitle>
          <CardDescription>測試複雜驗證和動態表單</CardDescription>
        </CardHeader>
        <CardContent>
          <UnifiedForm
            :initial-values="advancedFormValues"
            :validation-config="advancedValidationConfig"
            submit-text="提交進階表單"
            cancel-text="重置"
            :show-cancel="true"
            @submit="handleAdvancedSubmit"
            @cancel="handleAdvancedReset"
          >
            <template #default="{ values, errors, setFieldValue, validateField }">
              <FormFieldGroup title="動態驗證" layout="vertical" :border="true">
                <FormField
                  type="text"
                  label="使用者名稱"
                  placeholder="請輸入使用者名稱"
                  :model-value="values.username"
                  :error="errors.username"
                  required
                  description="使用者名稱必須唯一"
                  @update:model-value="(value) => setFieldValue('username', value)"
                  @blur="() => validateField('username')"
                />

                <FormField
                  type="password"
                  label="新密碼"
                  placeholder="請輸入新密碼"
                  :model-value="values.newPassword"
                  :error="errors.newPassword"
                  required
                  @update:model-value="(value) => setFieldValue('newPassword', value)"
                  @blur="() => validateField('newPassword')"
                />

                <FormField
                  type="password"
                  label="確認密碼"
                  placeholder="請再次輸入密碼"
                  :model-value="values.confirmPassword"
                  :error="errors.confirmPassword"
                  required
                  @update:model-value="(value) => setFieldValue('confirmPassword', value)"
                  @blur="() => validateField('confirmPassword')"
                />
              </FormFieldGroup>

              <FormFieldGroup title="條件顯示" layout="vertical">
                <FormField
                  type="radio"
                  label="通知方式"
                  :model-value="values.notificationMethod"
                  :options="notificationOptions"
                  @update:model-value="(value) => setFieldValue('notificationMethod', value)"
                />

                <FormField
                  v-if="values.notificationMethod === 'email'"
                  type="email"
                  label="通知郵箱"
                  placeholder="請輸入通知郵箱"
                  :model-value="values.notificationEmail"
                  :error="errors.notificationEmail"
                  required
                  @update:model-value="(value) => setFieldValue('notificationEmail', value)"
                />

                <FormField
                  v-if="values.notificationMethod === 'sms'"
                  type="text"
                  label="手機號碼"
                  placeholder="請輸入手機號碼"
                  :model-value="values.phoneNumber"
                  :error="errors.phoneNumber"
                  required
                  @update:model-value="(value) => setFieldValue('phoneNumber', value)"
                />
              </FormFieldGroup>
            </template>
          </UnifiedForm>
        </CardContent>
      </Card>
    </div>

    <!-- 提交結果顯示 -->
    <Card v-if="submitResults.length > 0" class="mt-8">
      <CardHeader>
        <CardTitle>提交結果</CardTitle>
        <CardDescription>最近的表單提交結果</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div
            v-for="(result, index) in submitResults"
            :key="index"
            class="p-4 rounded-lg border bg-muted/50"
          >
            <div class="flex items-center justify-between mb-2">
              <span class="font-medium">{{ result.formType }}</span>
              <span class="text-sm text-muted-foreground">{{ result.timestamp }}</span>
            </div>
            <pre class="text-sm overflow-auto">{{ JSON.stringify(result.data, null, 2) }}</pre>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Sun, Moon } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useTheme } from '@/composables/useTheme'
import { useRegionTheme } from '@/composables/useRegionTheme'
import {
  UnifiedForm,
  FormField,
  FormFieldGroup,
  validationRules,
} from '@/components/forms'

// ============================================================================
// Composables
// ============================================================================

const { isDarkMode, themeMode, toggleTheme } = useTheme()
const {
  currentRegion,
  isAdmin,
  isWorkspace,
  getPageClasses,
  setRegion,
} = useRegionTheme()

// ============================================================================
// 狀態
// ============================================================================

const submitResults = ref<Array<{
  formType: string
  data: any
  timestamp: string
}>>([])

// ============================================================================
// 計算屬性
// ============================================================================

const pageClasses = computed(() => getPageClasses.value)

// ============================================================================
// 表單配置
// ============================================================================

// 基本表單
const basicFormValues = ref({
  name: '',
  email: '',
  password: '',
  role: '',
  status: 'active',
  age: '',
  remarks: '',
  agreeTerms: false,
})

const basicValidationConfig = {
  rules: {
    name: [validationRules.required('姓名為必填'), validationRules.minLength(2)],
    email: [validationRules.required('電子郵件為必填'), validationRules.email()],
    password: [validationRules.required('密碼為必填'), validationRules.password()],
    role: [validationRules.required('請選擇角色')],
    age: [validationRules.numeric(), validationRules.min(18), validationRules.max(100)],
  },
  validateOnChange: true,
}

// 進階表單
const advancedFormValues = ref({
  username: '',
  newPassword: '',
  confirmPassword: '',
  notificationMethod: 'email',
  notificationEmail: '',
  phoneNumber: '',
})

const advancedValidationConfig = {
  rules: {
    username: [validationRules.required('使用者名稱為必填'), validationRules.minLength(3)],
    newPassword: [validationRules.required('新密碼為必填'), validationRules.password()],
    confirmPassword: [
      validationRules.required('請確認密碼'),
      (value: string, formValues: any) => {
        return value === formValues.newPassword ? true : '密碼確認不一致'
      },
    ],
    notificationEmail: [validationRules.email()],
    phoneNumber: [validationRules.pattern(/^09\d{8}$/, '請輸入有效的手機號碼')],
  },
  validateOnChange: true,
}

// 選項配置
const roleOptions = [
  { label: '管理員', value: 'admin' },
  { label: '使用者', value: 'user' },
  { label: '訪客', value: 'guest' },
]

const statusOptions = [
  { label: '啟用', value: 'active' },
  { label: '停用', value: 'inactive' },
]

const notificationOptions = [
  { label: '電子郵件', value: 'email' },
  { label: '簡訊', value: 'sms' },
  { label: '不通知', value: 'none' },
]

// ============================================================================
// 事件處理
// ============================================================================

const handleBasicSubmit = (data: any) => {
  submitResults.value.unshift({
    formType: '基本表單',
    data,
    timestamp: new Date().toLocaleString(),
  })
}

const handleAdvancedSubmit = (data: any) => {
  submitResults.value.unshift({
    formType: '進階表單',
    data,
    timestamp: new Date().toLocaleString(),
  })
}

const handleAdvancedReset = () => {
  advancedFormValues.value = {
    username: '',
    newPassword: '',
    confirmPassword: '',
    notificationMethod: 'email',
    notificationEmail: '',
    phoneNumber: '',
  }
}
</script>
