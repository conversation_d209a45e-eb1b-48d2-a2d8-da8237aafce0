# HorizAI 統一表單系統

## 概述

HorizAI 統一表單系統提供了完整的表單組件和驗證邏輯，支援 Admin 和 Workspace 區域的主題差異，並整合了 shadcn/ui 組件庫。

## 功能特色

- ✅ 統一的表單驗證系統
- ✅ 多種輸入類型支援
- ✅ 區域主題差異支援
- ✅ 完整的無障礙設計
- ✅ TypeScript 完整支援
- ✅ 響應式設計

## 快速開始

### 1. 基本使用

```vue
<template>
  <UnifiedForm
    :initial-values="formValues"
    :validation-config="validationConfig"
    @submit="handleSubmit"
  >
    <template #default="{ values, errors, setFieldValue, validateField }">
      <FormField
        type="text"
        label="姓名"
        :model-value="values.name"
        :error="errors.name"
        required
        @update:model-value="(value) => setFieldValue('name', value)"
        @blur="() => validateField('name')"
      />
    </template>
  </UnifiedForm>
</template>

<script setup lang="ts">
import { UnifiedForm, FormField, validationRules } from '@/components/forms'

const formValues = ref({
  name: '',
  email: '',
})

const validationConfig = {
  rules: {
    name: [validationRules.required('姓名為必填')],
    email: [validationRules.required(), validationRules.email()],
  }
}

const handleSubmit = (data) => {
  console.log('表單提交:', data)
}
</script>
```

### 2. 表單欄位類型

支援的表單欄位類型：

- `text` - 文字輸入
- `email` - 電子郵件輸入
- `password` - 密碼輸入
- `number` - 數字輸入
- `textarea` - 多行文字
- `select` - 下拉選擇
- `checkbox` - 複選框
- `radio` - 單選按鈕組
- `date` - 日期輸入

### 3. 驗證規則

內建驗證規則：

```typescript
import { validationRules } from '@/components/forms'

const rules = {
  required: validationRules.required('此欄位為必填'),
  email: validationRules.email('請輸入有效的電子郵件'),
  minLength: validationRules.minLength(6, '至少需要 6 個字元'),
  maxLength: validationRules.maxLength(50, '不能超過 50 個字元'),
  numeric: validationRules.numeric('請輸入數字'),
  min: validationRules.min(0, '不能小於 0'),
  max: validationRules.max(100, '不能大於 100'),
  pattern: validationRules.pattern(/^[A-Z]+$/, '只能包含大寫字母'),
  password: validationRules.password(), // 強密碼驗證
}
```

### 4. 表單組合

使用 `FormFieldGroup` 組織表單欄位：

```vue
<FormFieldGroup
  title="基本資訊"
  description="使用者的基本個人資訊"
  layout="grid-2"
  :border="true"
>
  <FormField type="text" label="姓名" />
  <FormField type="email" label="電子郵件" />
</FormFieldGroup>
```

支援的佈局：
- `vertical` - 垂直排列（預設）
- `horizontal` - 水平排列
- `grid-2` - 兩欄網格
- `grid-3` - 三欄網格
- `inline` - 內聯排列

## 測試頁面

訪問 `/admin/forms/test` 查看完整的表單系統測試頁面，包含：

- 基本表單組件測試
- 進階驗證功能測試
- 主題切換測試
- 區域樣式差異展示

## 區域主題差異

### Admin 區域
- 緊湊型間距（`space-y-3`）
- 專業風格設計
- 藍色主色調
- Gray 色系背景

### Workspace 區域
- 舒適型間距（`space-y-4`）
- 協作風格設計
- 綠色主色調
- Zinc 色系背景

## API 參考

### UnifiedForm Props

| 屬性 | 類型 | 預設值 | 說明 |
|------|------|--------|------|
| `initialValues` | `object` | - | 表單初始值 |
| `validationConfig` | `FormValidationConfig` | - | 驗證配置 |
| `title` | `string` | - | 表單標題 |
| `description` | `string` | - | 表單描述 |
| `submitText` | `string` | '提交' | 提交按鈕文字 |
| `showActions` | `boolean` | `true` | 是否顯示操作按鈕 |
| `showCancel` | `boolean` | `false` | 是否顯示取消按鈕 |

### FormField Props

| 屬性 | 類型 | 預設值 | 說明 |
|------|------|--------|------|
| `type` | `string` | 'text' | 輸入類型 |
| `label` | `string` | - | 欄位標籤 |
| `placeholder` | `string` | - | 佔位符文字 |
| `required` | `boolean` | `false` | 是否必填 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `error` | `string` | - | 錯誤訊息 |
| `description` | `string` | - | 說明文字 |

## 最佳實踐

1. **使用語意化驗證規則**
   ```typescript
   // ✅ 好的做法
   rules: {
     email: [validationRules.required(), validationRules.email()],
   }
   
   // ❌ 避免的做法
   rules: {
     email: [(value) => value ? true : '請輸入電子郵件'],
   }
   ```

2. **合理組織表單欄位**
   ```vue
   <!-- ✅ 使用 FormFieldGroup 組織相關欄位 -->
   <FormFieldGroup title="基本資訊" layout="grid-2">
     <FormField type="text" label="姓名" />
     <FormField type="email" label="電子郵件" />
   </FormFieldGroup>
   ```

3. **提供適當的使用者反饋**
   ```vue
   <FormField
     type="password"
     label="密碼"
     description="密碼需包含大小寫字母、數字，至少 8 個字元"
     :error="errors.password"
   />
   ```

## 故障排除

### 常見問題

1. **表單驗證不生效**
   - 確保在 `validationConfig` 中正確配置了驗證規則
   - 檢查是否正確調用了 `validateField` 方法

2. **樣式不一致**
   - 確保正確使用了 `useRegionTheme` composable
   - 檢查是否在正確的區域（admin/workspace）中使用

3. **TypeScript 錯誤**
   - 確保正確導入了類型定義
   - 檢查表單初始值的類型是否正確

## 更新日誌

### v1.0.0 (2024-12-19)
- ✅ 初始版本發布
- ✅ 統一表單驗證系統
- ✅ 多種輸入類型支援
- ✅ 區域主題差異支援
- ✅ 完整的無障礙設計
- ✅ TypeScript 完整支援
