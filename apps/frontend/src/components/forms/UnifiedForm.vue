<!--
  統一表單容器組件
  
  提供完整的表單功能，包括驗證、提交、重置等
  支援區域主題差異和無障礙設計
  整合 HorizAI Design System
-->

<template>
  <form 
    :class="formClasses"
    @submit.prevent="handleSubmit"
    :novalidate="true"
    role="form"
    :aria-label="formLabel"
  >
    <!-- 表單標題 -->
    <div v-if="title || description" :class="headerClasses">
      <h2 v-if="title" :class="titleClasses">
        {{ title }}
      </h2>
      <p v-if="description" :class="descriptionClasses">
        {{ description }}
      </p>
    </div>

    <!-- 表單內容 -->
    <div :class="contentClasses">
      <slot 
        :values="formState.values"
        :errors="formState.errors"
        :touched="formState.touched"
        :isValid="formState.isValid"
        :isSubmitting="formState.isSubmitting"
        :isDirty="formState.isDirty"
        :setFieldValue="setFieldValue"
        :validateField="validateField"
        :clearFieldError="clearFieldError"
      />
    </div>

    <!-- 表單操作按鈕 -->
    <div v-if="showActions" :class="actionsClasses">
      <slot name="actions" :isValid="formState.isValid" :isSubmitting="formState.isSubmitting">
        <!-- 預設按鈕 -->
        <div class="flex items-center justify-end space-x-3">
          <Button
            v-if="showCancel"
            type="button"
            variant="outline"
            :disabled="formState.isSubmitting"
            @click="handleCancel"
          >
            {{ cancelText }}
          </Button>
          
          <Button
            type="submit"
            :disabled="!formState.isValid || formState.isSubmitting"
            :class="submitButtonClasses"
          >
            <Loader2 
              v-if="formState.isSubmitting" 
              class="w-4 h-4 mr-2 animate-spin" 
            />
            {{ formState.isSubmitting ? submittingText : submitText }}
          </Button>
        </div>
      </slot>
    </div>

    <!-- 全域錯誤訊息 -->
    <div v-if="globalError" :class="globalErrorClasses">
      <AlertCircle class="w-4 h-4 mr-2" />
      {{ globalError }}
    </div>
  </form>
</template>

<script setup lang="ts">
import { computed, onMounted, watch } from 'vue'
import { Button } from '@/components/ui/button'
import { Loader2, AlertCircle } from 'lucide-vue-next'
import { useFormValidation, type FormValidationConfig } from '@/composables/forms/useFormValidation'
import { useRegionTheme } from '@/composables/useRegionTheme'

// ============================================================================
// 類型定義
// ============================================================================

interface Props<T extends Record<string, any>> {
  // 表單配置
  initialValues: T
  validationConfig?: FormValidationConfig<T>
  
  // 表單內容
  title?: string
  description?: string
  formLabel?: string
  
  // 按鈕文字
  submitText?: string
  submittingText?: string
  cancelText?: string
  
  // 顯示控制
  showActions?: boolean
  showCancel?: boolean
  
  // 樣式
  layout?: 'vertical' | 'horizontal'
  size?: 'sm' | 'default' | 'lg'
  
  // 錯誤
  globalError?: string
  
  // 自動驗證
  validateOnMount?: boolean
}

const props = withDefaults(defineProps<Props<any>>(), {
  submitText: '提交',
  submittingText: '提交中...',
  cancelText: '取消',
  showActions: true,
  showCancel: false,
  layout: 'vertical',
  size: 'default',
  validateOnMount: false,
})

// ============================================================================
// Emits
// ============================================================================

const emit = defineEmits<{
  'submit': [values: any]
  'cancel': []
  'change': [values: any]
  'validate': [isValid: boolean]
}>()

// ============================================================================
// Composables
// ============================================================================

const { getSpacingClasses, getButtonClasses, isAdmin } = useRegionTheme()

const {
  values,
  errors,
  touched,
  isValid,
  isDirty,
  isSubmitting,
  formState,
  validateField,
  validateForm,
  setFieldValue,
  clearFieldError,
  resetForm,
  handleSubmit: handleFormSubmit,
} = useFormValidation(props.initialValues, props.validationConfig)

// ============================================================================
// 計算屬性
// ============================================================================

const formClasses = computed(() => {
  const baseClasses = ['w-full']
  
  // 區域間距
  if (isAdmin.value) {
    baseClasses.push(getSpacingClasses('form'))
  } else {
    baseClasses.push('space-y-6')
  }
  
  return baseClasses.join(' ')
})

const headerClasses = computed(() => {
  return isAdmin.value ? 'space-y-2 mb-4' : 'space-y-3 mb-6'
})

const titleClasses = computed(() => {
  const baseClasses = ['font-semibold tracking-tight']
  
  if (props.size === 'sm') {
    baseClasses.push('text-lg')
  } else if (props.size === 'lg') {
    baseClasses.push('text-2xl')
  } else {
    baseClasses.push('text-xl')
  }
  
  return baseClasses.join(' ')
})

const descriptionClasses = computed(() => {
  return 'text-sm text-muted-foreground'
})

const contentClasses = computed(() => {
  const baseClasses = []
  
  if (props.layout === 'horizontal') {
    baseClasses.push('grid grid-cols-1 md:grid-cols-2 gap-4')
  } else {
    baseClasses.push(isAdmin.value ? 'space-y-4' : 'space-y-6')
  }
  
  return baseClasses.join(' ')
})

const actionsClasses = computed(() => {
  const baseClasses = ['border-t pt-4 mt-6']
  
  if (isAdmin.value) {
    baseClasses.push('border-gray-200 dark:border-gray-700')
  } else {
    baseClasses.push('border-zinc-200/50 dark:border-zinc-800/50')
  }
  
  return baseClasses.join(' ')
})

const submitButtonClasses = computed(() => {
  return getButtonClasses('primary')
})

const globalErrorClasses = computed(() => {
  const baseClasses = [
    'flex items-center p-3 mt-4 text-sm rounded-md',
    'bg-destructive/10 text-destructive border border-destructive/20'
  ]
  
  return baseClasses.join(' ')
})

// ============================================================================
// 方法
// ============================================================================

const handleSubmit = async () => {
  try {
    await handleFormSubmit(async (formValues) => {
      emit('submit', formValues)
    })
  } catch (error) {
    console.error('表單提交失敗:', error)
  }
}

const handleCancel = () => {
  emit('cancel')
}

// ============================================================================
// 生命週期
// ============================================================================

onMounted(() => {
  if (props.validateOnMount) {
    validateForm()
  }
})

// 監聽表單值變化
watch(values, (newValues) => {
  emit('change', newValues)
}, { deep: true })

// 監聽驗證狀態變化
watch(isValid, (newIsValid) => {
  emit('validate', newIsValid)
})

// ============================================================================
// 暴露給父組件的方法
// ============================================================================

defineExpose({
  values,
  errors,
  touched,
  isValid,
  isDirty,
  isSubmitting,
  formState,
  validateField,
  validateForm,
  setFieldValue,
  clearFieldError,
  resetForm,
})
</script>
