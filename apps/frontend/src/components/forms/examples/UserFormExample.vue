<!--
  使用者表單示例
  
  展示統一表單系統的使用方式
  包含完整的驗證、錯誤處理和區域主題支援
-->

<template>
  <UnifiedForm
    :initial-values="initialValues"
    :validation-config="validationConfig"
    title="使用者管理"
    description="建立或編輯使用者資訊"
    submit-text="儲存使用者"
    submitting-text="儲存中..."
    cancel-text="取消"
    :show-cancel="true"
    :global-error="globalError"
    @submit="handleSubmit"
    @cancel="handleCancel"
    ref="formRef"
  >
    <template #default="{ values, errors, setFieldValue, validateField, clearFieldError }">
      <!-- 基本資訊 -->
      <FormFieldGroup
        title="基本資訊"
        description="使用者的基本個人資訊"
        layout="grid-2"
        :border="true"
      >
        <FormField
          type="text"
          label="姓名"
          placeholder="請輸入使用者姓名"
          :model-value="values.name"
          :error="errors.name"
          required
          @update:model-value="(value) => setFieldValue('name', value)"
          @blur="() => validateField('name')"
        />

        <FormField
          type="email"
          label="電子郵件"
          placeholder="請輸入電子郵件地址"
          :model-value="values.email"
          :error="errors.email"
          required
          @update:model-value="(value) => setFieldValue('email', value)"
          @blur="() => validateField('email')"
        />
      </FormFieldGroup>

      <!-- 帳戶設定 -->
      <FormFieldGroup
        title="帳戶設定"
        description="使用者的帳戶相關設定"
        layout="grid-2"
        :border="true"
      >
        <FormField
          type="password"
          label="密碼"
          placeholder="請輸入密碼"
          :model-value="values.password"
          :error="errors.password"
          :required="!isEditing"
          description="密碼需包含大小寫字母、數字，至少 8 個字元"
          @update:model-value="(value) => setFieldValue('password', value)"
          @blur="() => validateField('password')"
        />

        <FormField
          type="select"
          label="使用者角色"
          placeholder="請選擇角色"
          :model-value="values.role"
          :error="errors.role"
          :options="userRoleOptions"
          required
          @update:model-value="(value) => setFieldValue('role', value)"
          @blur="() => validateField('role')"
        />
      </FormFieldGroup>

      <!-- 狀態設定 -->
      <FormFieldGroup
        title="狀態設定"
        layout="horizontal"
      >
        <FormField
          type="select"
          label="帳戶狀態"
          :model-value="values.status"
          :error="errors.status"
          :options="userStatusOptions"
          @update:model-value="(value) => setFieldValue('status', value)"
        />

        <FormField
          type="checkbox"
          checkbox-label="發送歡迎郵件"
          :model-value="values.sendWelcomeEmail"
          description="建立使用者後自動發送歡迎郵件"
          @update:model-value="(value) => setFieldValue('sendWelcomeEmail', value)"
        />
      </FormFieldGroup>

      <!-- 額外資訊 -->
      <FormFieldGroup title="額外資訊">
        <FormField
          type="textarea"
          label="備註"
          placeholder="請輸入備註資訊（選填）"
          :model-value="values.remarks"
          :error="errors.remarks"
          :rows="3"
          description="最多 500 個字元"
          @update:model-value="(value) => setFieldValue('remarks', value)"
        />
      </FormFieldGroup>
    </template>

    <!-- 自定義操作按鈕 -->
    <template #actions="{ isValid, isSubmitting }">
      <div class="flex items-center justify-between">
        <!-- 左側：重置按鈕 -->
        <Button
          type="button"
          variant="ghost"
          :disabled="isSubmitting"
          @click="handleReset"
        >
          重置表單
        </Button>

        <!-- 右側：主要操作 -->
        <div class="flex items-center space-x-3">
          <Button
            type="button"
            variant="outline"
            :disabled="isSubmitting"
            @click="handleCancel"
          >
            取消
          </Button>
          
          <Button
            type="submit"
            :disabled="!isValid || isSubmitting"
          >
            <Loader2 
              v-if="isSubmitting" 
              class="w-4 h-4 mr-2 animate-spin" 
            />
            {{ isSubmitting ? '儲存中...' : (isEditing ? '更新使用者' : '建立使用者') }}
          </Button>
        </div>
      </div>
    </template>
  </UnifiedForm>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Button } from '@/components/ui/button'
import { Loader2 } from 'lucide-vue-next'
import {
  UnifiedForm,
  FormField,
  FormFieldGroup,
  useFormValidation,
  validationRules,
  userRoleOptions,
  userStatusOptions,
  createUserFormInitialValues,
} from '@/components/forms'

// ============================================================================
// 類型定義
// ============================================================================

interface UserFormData {
  name: string
  email: string
  password: string
  role: string
  status: 'active' | 'inactive' | 'suspended'
  sendWelcomeEmail: boolean
  remarks: string
}

interface Props {
  user?: Partial<UserFormData>
  isEditing?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isEditing: false,
})

// ============================================================================
// Emits
// ============================================================================

const emit = defineEmits<{
  'submit': [data: UserFormData]
  'cancel': []
}>()

// ============================================================================
// 狀態
// ============================================================================

const formRef = ref()
const globalError = ref('')

// ============================================================================
// 表單配置
// ============================================================================

const initialValues = computed<UserFormData>(() => {
  const defaults = {
    ...createUserFormInitialValues(),
    sendWelcomeEmail: true,
    remarks: '',
  }
  
  if (props.user) {
    return { ...defaults, ...props.user }
  }
  
  return defaults
})

const validationConfig = {
  rules: {
    name: [
      validationRules.required('姓名為必填'),
      validationRules.minLength(2, '姓名至少需要 2 個字元'),
      validationRules.maxLength(50, '姓名不能超過 50 個字元'),
    ],
    email: [
      validationRules.required('電子郵件為必填'),
      validationRules.email('請輸入有效的電子郵件地址'),
    ],
    password: props.isEditing 
      ? [] // 編輯時密碼為選填
      : [
          validationRules.required('密碼為必填'),
          validationRules.password(),
        ],
    role: [
      validationRules.required('請選擇使用者角色'),
    ],
    remarks: [
      validationRules.maxLength(500, '備註不能超過 500 個字元'),
    ],
  },
  validateOnChange: true,
}

// ============================================================================
// 事件處理
// ============================================================================

const handleSubmit = async (formData: UserFormData) => {
  try {
    globalError.value = ''
    
    // 模擬 API 呼叫
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 如果是編輯模式且密碼為空，則移除密碼欄位
    if (props.isEditing && !formData.password) {
      const { password, ...dataWithoutPassword } = formData
      emit('submit', dataWithoutPassword as UserFormData)
    } else {
      emit('submit', formData)
    }
    
  } catch (error: any) {
    globalError.value = error.message || '提交失敗，請稍後再試'
    throw error
  }
}

const handleCancel = () => {
  emit('cancel')
}

const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetForm()
  }
  globalError.value = ''
}
</script>
