/**
 * HorizAI 統一表單組件系統
 * 
 * 導出所有表單相關組件和工具函數
 * 基於 shadcn/ui 和 HorizAI Design System
 * 
 * <AUTHOR> Design System Team
 * @version 1.0.0
 */

// ============================================================================
// 組件導出
// ============================================================================

export { default as UnifiedForm } from './UnifiedForm.vue'
export { default as FormField } from './FormField.vue'
export { default as FormFieldGroup } from './FormFieldGroup.vue'

// ============================================================================
// Composables 導出
// ============================================================================

export {
  useFormValidation,
  validationRules,
  type FormFieldError,
  type ValidationRule,
  type FormValidationConfig,
  type FormState,
} from '@/composables/forms/useFormValidation'

// ============================================================================
// 常用表單配置
// ============================================================================

/**
 * 使用者表單驗證配置
 */
export const userFormValidation = {
  name: [
    validationRules.required('姓名為必填'),
    validationRules.minLength(2, '姓名至少需要 2 個字元'),
    validationRules.maxLength(50, '姓名不能超過 50 個字元'),
  ],
  email: [
    validationRules.required('電子郵件為必填'),
    validationRules.email('請輸入有效的電子郵件地址'),
  ],
  password: [
    validationRules.required('密碼為必填'),
    validationRules.password(),
  ],
}

/**
 * 租戶表單驗證配置
 */
export const tenantFormValidation = {
  name: [
    validationRules.required('租戶名稱為必填'),
    validationRules.minLength(2, '租戶名稱至少需要 2 個字元'),
    validationRules.maxLength(100, '租戶名稱不能超過 100 個字元'),
  ],
  domain: [
    validationRules.pattern(
      /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/,
      '請輸入有效的網域格式'
    ),
  ],
  industry: [
    validationRules.required('請選擇產業類型'),
  ],
}

/**
 * 方案表單驗證配置
 */
export const planFormValidation = {
  name: [
    validationRules.required('方案名稱為必填'),
    validationRules.minLength(2, '方案名稱至少需要 2 個字元'),
    validationRules.maxLength(100, '方案名稱不能超過 100 個字元'),
  ],
  price: [
    validationRules.required('價格為必填'),
    validationRules.numeric('請輸入有效的數字'),
    validationRules.min(0, '價格不能小於 0'),
  ],
  period: [
    validationRules.required('請選擇計費週期'),
  ],
}

/**
 * 訂單表單驗證配置
 */
export const orderFormValidation = {
  tenantId: [
    validationRules.required('請選擇租戶'),
  ],
  planId: [
    validationRules.required('請選擇方案'),
  ],
  startDate: [
    validationRules.required('請選擇開始日期'),
  ],
  contactEmail: [
    validationRules.email('請輸入有效的電子郵件地址'),
  ],
}

// ============================================================================
// 表單工具函數
// ============================================================================

/**
 * 創建基本的使用者表單初始值
 */
export function createUserFormInitialValues() {
  return {
    name: '',
    email: '',
    password: '',
    role: '',
    status: 'active' as const,
  }
}

/**
 * 創建基本的租戶表單初始值
 */
export function createTenantFormInitialValues() {
  return {
    name: '',
    domain: '',
    companySize: '1-10' as const,
    industry: '',
    status: 'active' as const,
  }
}

/**
 * 創建基本的方案表單初始值
 */
export function createPlanFormInitialValues() {
  return {
    name: '',
    description: '',
    price: 0,
    period: 'monthly' as const,
    features: [] as string[],
    isActive: true,
  }
}

/**
 * 創建基本的訂單表單初始值
 */
export function createOrderFormInitialValues() {
  return {
    tenantId: '',
    planId: '',
    startDate: '',
    period: 1,
    contactName: '',
    contactEmail: '',
    paymentMethod: '銀行轉帳' as const,
    remarks: '',
  }
}

// ============================================================================
// 表單選項配置
// ============================================================================

/**
 * 使用者角色選項
 */
export const userRoleOptions = [
  { label: '系統管理員', value: 'SYSTEM_ADMIN' },
  { label: '租戶管理員', value: 'TENANT_ADMIN' },
  { label: '租戶使用者', value: 'TENANT_USER' },
]

/**
 * 使用者狀態選項
 */
export const userStatusOptions = [
  { label: '啟用', value: 'active' },
  { label: '停用', value: 'inactive' },
  { label: '暫停', value: 'suspended' },
]

/**
 * 公司規模選項
 */
export const companySizeOptions = [
  { label: '1-10 人', value: '1-10' },
  { label: '11-50 人', value: '11-50' },
  { label: '51-200 人', value: '51-200' },
  { label: '201-500 人', value: '201-500' },
  { label: '500+ 人', value: '500+' },
]

/**
 * 產業類型選項
 */
export const industryOptions = [
  { label: '科技業', value: 'technology' },
  { label: '金融業', value: 'finance' },
  { label: '製造業', value: 'manufacturing' },
  { label: '零售業', value: 'retail' },
  { label: '醫療保健', value: 'healthcare' },
  { label: '教育', value: 'education' },
  { label: '其他', value: 'other' },
]

/**
 * 計費週期選項
 */
export const billingPeriodOptions = [
  { label: '月付', value: 'monthly' },
  { label: '季付', value: 'quarterly' },
  { label: '年付', value: 'yearly' },
]

/**
 * 付款方式選項
 */
export const paymentMethodOptions = [
  { label: '銀行轉帳', value: '銀行轉帳' },
  { label: '信用卡', value: '信用卡' },
  { label: '支票', value: '支票' },
  { label: '現金', value: '現金' },
]

// ============================================================================
// 表單主題配置
// ============================================================================

/**
 * 獲取區域特定的表單配置
 */
export function getRegionFormConfig(region: 'admin' | 'workspace') {
  if (region === 'admin') {
    return {
      size: 'default' as const,
      layout: 'vertical' as const,
      spacing: 'compact' as const,
      showActions: true,
      showCancel: false,
    }
  } else {
    return {
      size: 'default' as const,
      layout: 'vertical' as const,
      spacing: 'comfortable' as const,
      showActions: true,
      showCancel: true,
    }
  }
}

// ============================================================================
// 類型導出
// ============================================================================

export type {
  FormFieldError,
  ValidationRule,
  FormValidationConfig,
  FormState,
} from '@/composables/forms/useFormValidation'

// ============================================================================
// 常數導出
// ============================================================================

export const FORM_FIELD_TYPES = [
  'text', 'email', 'password', 'number', 'textarea', 
  'select', 'checkbox', 'radio', 'date'
] as const

export const FORM_LAYOUTS = ['vertical', 'horizontal', 'grid-2', 'grid-3', 'inline'] as const
export const FORM_SIZES = ['sm', 'default', 'lg'] as const
export const FORM_SPACING = ['compact', 'comfortable'] as const
