# 表單測試頁面路由配置驗證

## 已完成的配置

### 1. 路由配置 ✅

**檔案：** `/apps/frontend/src/router/admin.routes.ts`

```typescript
{
  path: "forms/test",
  name: "admin-forms-test",
  component: () => import("@/components/forms/FormTestPage.vue"),
  meta: {
    title: "表單系統測試",
    description: "測試統一表單組件和驗證系統",
    permission: {
      action: "manage",
      subject: "SystemSettings",
    },
    isDevelopment: true, // 標記為開發工具
  },
}
```

**路由路徑：** `/admin/forms/test`

### 2. 導航選單配置 ✅

**檔案：** `/apps/frontend/src/components/common/navigation/AdminSidebar.vue`

**新增分組：** 開發工具
- 圖示：TestTube (試管圖示)
- 標題：表單系統測試
- 路徑：`/admin/forms/test`

**顯示條件：**
```typescript
show: (process.env.NODE_ENV === 'development' || isSuperAdmin.value) &&
      (isSuperAdmin.value || isSystemAdmin.value || can(Actions.MANAGE, Subjects.SYSTEM_SETTINGS))
```

### 3. 權限要求

- **開發環境：** 自動顯示給有系統設定權限的使用者
- **正式環境：** 僅超級管理員可見
- **最低權限：** `MANAGE` + `SystemSettings`

## 訪問方式

### 方法 1：直接 URL 訪問
```
http://localhost:3000/admin/forms/test
```

### 方法 2：側邊欄導航
1. 登入管理後台
2. 在側邊欄找到「開發工具」分組
3. 點擊「表單系統測試」

## 功能驗證清單

### 基本功能 ✅
- [x] 路由正確配置
- [x] 組件正確導入
- [x] 權限控制正確
- [x] 導航選單顯示

### 頁面功能測試
- [ ] 頁面正常載入
- [ ] 主題切換功能
- [ ] 區域切換功能
- [ ] 表單驗證測試
- [ ] 響應式設計測試

### 權限測試
- [ ] 超級管理員可訪問
- [ ] 系統管理員可訪問
- [ ] 一般使用者無法訪問（如果沒有權限）
- [ ] 開發環境自動顯示

## 故障排除

### 如果頁面無法載入

1. **檢查路由配置**
   ```bash
   # 確認路由檔案沒有語法錯誤
   npm run type-check
   ```

2. **檢查組件路徑**
   ```typescript
   // 確認組件路徑正確
   component: () => import("@/components/forms/FormTestPage.vue")
   ```

3. **檢查權限設定**
   ```typescript
   // 確認使用者有足夠權限
   permission: {
     action: "manage",
     subject: "SystemSettings",
   }
   ```

### 如果導航選單不顯示

1. **檢查環境變數**
   ```javascript
   console.log('NODE_ENV:', process.env.NODE_ENV)
   ```

2. **檢查使用者權限**
   ```javascript
   console.log('isSuperAdmin:', isSuperAdmin.value)
   console.log('isSystemAdmin:', isSystemAdmin.value)
   ```

3. **檢查權限能力**
   ```javascript
   console.log('can manage SystemSettings:', can(Actions.MANAGE, Subjects.SYSTEM_SETTINGS))
   ```

## 測試步驟

### 1. 基本訪問測試
```bash
# 啟動開發伺服器
npm run dev

# 訪問測試頁面
# http://localhost:3000/admin/forms/test
```

### 2. 功能測試
1. 測試主題切換（light/dark）
2. 測試區域切換（admin/workspace）
3. 測試表單驗證功能
4. 測試響應式設計

### 3. 權限測試
1. 使用不同權限等級的帳號測試
2. 確認開發工具分組的顯示邏輯
3. 驗證路由保護機制

## 預期結果

### 成功指標
- ✅ 頁面正常載入，無 JavaScript 錯誤
- ✅ 主題切換功能正常運作
- ✅ 區域切換功能正常運作
- ✅ 表單驗證功能正常運作
- ✅ 響應式設計在不同螢幕尺寸下正常顯示
- ✅ 導航選單在適當條件下顯示

### 效能指標
- 頁面載入時間 < 2 秒
- 主題切換響應時間 < 200ms
- 表單驗證響應時間 < 100ms

## 後續步驟

1. **完成功能測試**：驗證所有表單功能正常運作
2. **進行使用者測試**：邀請其他開發者測試使用體驗
3. **文檔完善**：根據測試結果更新使用文檔
4. **部署準備**：確保正式環境的權限控制正確

---

**配置完成時間：** 2024-12-19  
**配置者：** HorizAI Design System Team  
**狀態：** 已完成，待測試驗證
