/**
 * 表單驗證系統測試
 * 
 * 用於驗證表單組件和驗證邏輯是否正常工作
 * 可以在瀏覽器控制台中執行這些測試
 */

import { validationRules } from '@/composables/forms/useFormValidation'

/**
 * 測試驗證規則
 */
export function testValidationRules() {
  console.log('🧪 開始測試表單驗證規則...')
  
  const tests = [
    // 測試 required 規則
    {
      name: 'required - 空值',
      rule: validationRules.required('此欄位為必填'),
      value: '',
      expected: '此欄位為必填'
    },
    {
      name: 'required - 有值',
      rule: validationRules.required('此欄位為必填'),
      value: 'test',
      expected: true
    },
    
    // 測試 email 規則
    {
      name: 'email - 有效郵箱',
      rule: validationRules.email(),
      value: '<EMAIL>',
      expected: true
    },
    {
      name: 'email - 無效郵箱',
      rule: validationRules.email(),
      value: 'invalid-email',
      expected: '請輸入有效的電子郵件地址'
    },
    
    // 測試 minLength 規則
    {
      name: 'minLength - 符合最小長度',
      rule: validationRules.minLength(3),
      value: 'test',
      expected: true
    },
    {
      name: 'minLength - 不符合最小長度',
      rule: validationRules.minLength(5),
      value: 'abc',
      expected: '至少需要 5 個字元'
    },
    
    // 測試 numeric 規則
    {
      name: 'numeric - 有效數字',
      rule: validationRules.numeric(),
      value: '123',
      expected: true
    },
    {
      name: 'numeric - 無效數字',
      rule: validationRules.numeric(),
      value: 'abc',
      expected: '請輸入有效的數字'
    },
  ]
  
  let passedTests = 0
  let totalTests = tests.length
  
  tests.forEach(test => {
    try {
      const result = test.rule(test.value)
      const passed = result === test.expected
      
      if (passed) {
        console.log(`✅ ${test.name}: PASS`)
        passedTests++
      } else {
        console.log(`❌ ${test.name}: FAIL`)
        console.log(`   Expected: ${test.expected}`)
        console.log(`   Got: ${result}`)
      }
    } catch (error) {
      console.log(`💥 ${test.name}: ERROR`)
      console.error(error)
    }
  })
  
  console.log(`\n📊 測試結果: ${passedTests}/${totalTests} 通過`)
  
  if (passedTests === totalTests) {
    console.log('🎉 所有驗證規則測試通過！')
  } else {
    console.log('⚠️ 部分測試失敗，請檢查驗證邏輯')
  }
  
  return passedTests === totalTests
}

/**
 * 測試表單配置
 */
export function testFormConfigurations() {
  console.log('\n🧪 開始測試表單配置...')
  
  try {
    // 測試導入
    const {
      userFormValidation,
      tenantFormValidation,
      planFormValidation,
      orderFormValidation,
      userRoleOptions,
      userStatusOptions,
      createUserFormInitialValues,
    } = require('./index')
    
    // 測試表單驗證配置
    console.log('✅ userFormValidation:', userFormValidation ? 'OK' : 'FAIL')
    console.log('✅ tenantFormValidation:', tenantFormValidation ? 'OK' : 'FAIL')
    console.log('✅ planFormValidation:', planFormValidation ? 'OK' : 'FAIL')
    console.log('✅ orderFormValidation:', orderFormValidation ? 'OK' : 'FAIL')
    
    // 測試選項配置
    console.log('✅ userRoleOptions:', Array.isArray(userRoleOptions) ? 'OK' : 'FAIL')
    console.log('✅ userStatusOptions:', Array.isArray(userStatusOptions) ? 'OK' : 'FAIL')
    
    // 測試初始值函數
    const initialValues = createUserFormInitialValues()
    console.log('✅ createUserFormInitialValues:', 
      (initialValues && typeof initialValues === 'object') ? 'OK' : 'FAIL')
    
    console.log('🎉 表單配置測試完成！')
    return true
    
  } catch (error) {
    console.error('💥 表單配置測試失敗:', error)
    return false
  }
}

/**
 * 執行所有測試
 */
export function runAllTests() {
  console.log('🚀 開始執行表單系統完整測試...\n')
  
  const validationTest = testValidationRules()
  const configTest = testFormConfigurations()
  
  console.log('\n📋 測試總結:')
  console.log(`   驗證規則測試: ${validationTest ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`   表單配置測試: ${configTest ? '✅ PASS' : '❌ FAIL'}`)
  
  const allPassed = validationTest && configTest
  
  if (allPassed) {
    console.log('\n🎉 所有測試通過！表單系統運作正常。')
  } else {
    console.log('\n⚠️ 部分測試失敗，請檢查相關配置。')
  }
  
  return allPassed
}

/**
 * 在瀏覽器控制台中執行測試的輔助函數
 */
export function runTestsInConsole() {
  // 將測試函數掛載到 window 對象，方便在控制台中調用
  if (typeof window !== 'undefined') {
    (window as any).testFormSystem = {
      runAll: runAllTests,
      testValidation: testValidationRules,
      testConfig: testFormConfigurations,
    }
    
    console.log('🔧 表單系統測試工具已載入！')
    console.log('在控制台中執行以下命令來測試：')
    console.log('  window.testFormSystem.runAll()      - 執行所有測試')
    console.log('  window.testFormSystem.testValidation() - 測試驗證規則')
    console.log('  window.testFormSystem.testConfig()     - 測試表單配置')
  }
}

// 自動執行測試（僅在開發環境）
if (process.env.NODE_ENV === 'development') {
  runTestsInConsole()
}
