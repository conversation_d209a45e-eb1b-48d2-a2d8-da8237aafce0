/**
 * 表單驗證系統測試
 * 
 * 用於驗證 validationRules 是否正確導入和運作
 */

import { validationRules } from './index'

// 測試 validationRules 是否正確導入
console.log('validationRules 測試:')
console.log('- required:', typeof validationRules.required)
console.log('- email:', typeof validationRules.email)
console.log('- password:', typeof validationRules.password)
console.log('- minLength:', typeof validationRules.minLength)
console.log('- maxLength:', typeof validationRules.maxLength)

// 測試基本驗證功能
const testValidation = () => {
  // 測試必填驗證
  const requiredRule = validationRules.required('此欄位為必填')
  console.log('必填驗證測試:')
  console.log('- 空字串:', requiredRule(''))
  console.log('- 有值:', requiredRule('test'))

  // 測試電子郵件驗證
  const emailRule = validationRules.email()
  console.log('電子郵件驗證測試:')
  console.log('- 無效郵件:', emailRule('invalid'))
  console.log('- 有效郵件:', emailRule('<EMAIL>'))

  // 測試密碼驗證
  const passwordRule = validationRules.password()
  console.log('密碼驗證測試:')
  console.log('- 弱密碼:', passwordRule('123'))
  console.log('- 強密碼:', passwordRule('Test123!'))
}

// 導出測試函數
export { testValidation }

// 如果在開發環境，自動執行測試
if (process.env.NODE_ENV === 'development') {
  testValidation()
}
