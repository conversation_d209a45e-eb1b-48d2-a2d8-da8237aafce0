# HorizAI 統一表格系統

## 概述

HorizAI 統一表格系統提供了支援虛擬滾動的高效能表格解決方案，能夠處理大數據量的顯示需求，同時保持優秀的使用者體驗和效能表現。

## 功能特色

- ✅ **虛擬滾動支援**：只渲染可見區域的項目，支援 10 萬+ 項目的流暢顯示
- ✅ **統一排序系統**：支援多欄位排序、自定義排序函數
- ✅ **強大篩選功能**：支援多種篩選類型和全域搜尋
- ✅ **行選擇功能**：支援單選、多選和全選操作
- ✅ **區域主題支援**：Admin 和 Workspace 區域的樣式差異
- ✅ **響應式設計**：適配不同螢幕尺寸
- ✅ **TypeScript 完整支援**：完整的型別定義和智能提示
- ✅ **無障礙設計**：符合 WCAG 標準的無障礙功能

## 快速開始

### 1. 基本使用

```vue
<template>
  <UnifiedTable
    :data="tableData"
    :columns="tableColumns"
    :enable-virtual-scroll="true"
    :virtual-scroll-height="500"
    title="使用者列表"
    @row-click="handleRowClick"
  />
</template>

<script setup lang="ts">
import { UnifiedTable, type TableColumn } from '@/components/tables'

const tableData = ref([
  { id: 1, name: '張三', email: '<EMAIL>', status: 'active' },
  { id: 2, name: '李四', email: '<EMAIL>', status: 'inactive' },
  // ... 更多資料
])

const tableColumns: TableColumn[] = [
  {
    key: 'name',
    title: '姓名',
    sortable: true,
    filterable: true,
    filterType: 'text',
    width: 150,
  },
  {
    key: 'email',
    title: '電子郵件',
    sortable: true,
    filterable: true,
    filterType: 'text',
    width: 200,
  },
  {
    key: 'status',
    title: '狀態',
    sortable: true,
    filterable: true,
    filterType: 'select',
    width: 100,
    filterOptions: [
      { label: '啟用', value: 'active' },
      { label: '停用', value: 'inactive' },
    ],
  },
]

const handleRowClick = (item: any, index: number) => {
  console.log('點擊行:', item)
}
</script>
```

### 2. 虛擬滾動配置

```vue
<UnifiedTable
  :data="largeDataset"
  :columns="columns"
  :enable-virtual-scroll="true"
  :virtual-scroll-height="600"
  :options="{
    virtualScroll: {
      itemHeight: 48,
      buffer: 10,
      throttleDelay: 16,
    }
  }"
/>
```

### 3. 自定義欄位渲染

```vue
<template>
  <UnifiedTable :data="data" :columns="columns">
    <!-- 自定義狀態欄位 -->
    <template #cell-status="{ value, item }">
      <Badge :variant="getStatusVariant(value)">
        {{ getStatusLabel(value) }}
      </Badge>
    </template>
    
    <!-- 自定義操作欄位 -->
    <template #cell-actions="{ item }">
      <div class="flex space-x-2">
        <Button size="sm" @click="editItem(item)">編輯</Button>
        <Button size="sm" variant="destructive" @click="deleteItem(item)">刪除</Button>
      </div>
    </template>
  </UnifiedTable>
</template>
```

## 核心組件

### UnifiedTable

主要的表格組件，整合了所有功能。

**Props:**

| 屬性 | 類型 | 預設值 | 說明 |
|------|------|--------|------|
| `data` | `any[]` | - | 表格資料 |
| `columns` | `TableColumn[]` | - | 欄位配置 |
| `enableVirtualScroll` | `boolean` | `false` | 是否啟用虛擬滾動 |
| `virtualScrollHeight` | `number` | `400` | 虛擬滾動容器高度 |
| `enableSort` | `boolean` | `true` | 是否啟用排序 |
| `enableFilter` | `boolean` | `true` | 是否啟用篩選 |
| `enableGlobalSearch` | `boolean` | `true` | 是否啟用全域搜尋 |
| `selectable` | `boolean` | `false` | 是否可選擇行 |
| `loading` | `boolean` | `false` | 載入狀態 |

**Events:**

| 事件 | 參數 | 說明 |
|------|------|------|
| `row-click` | `(item, index)` | 行點擊事件 |
| `selection-change` | `(selectedKeys)` | 選擇變更事件 |
| `sort-change` | `(sortConfigs)` | 排序變更事件 |
| `filter-change` | `(filters)` | 篩選變更事件 |

## Composables

### useVirtualScroll

提供虛擬滾動功能的 composable。

```typescript
const {
  containerRef,
  visibleItems,
  totalHeight,
  offsetY,
  scrollToItem,
  scrollToTop,
  scrollToBottom,
} = useVirtualScroll(items, {
  itemHeight: 48,
  containerHeight: 400,
  buffer: 5,
})
```

### useTableSort

提供表格排序功能的 composable。

```typescript
const {
  sortedData,
  getSortDirection,
  toggleSort,
  clearSort,
} = useTableSort(data, columns, {
  multiSort: false,
  defaultSort: [{ field: 'name', direction: 'asc' }],
})
```

### useTableFilter

提供表格篩選功能的 composable。

```typescript
const {
  filteredData,
  addFilter,
  removeFilter,
  clearFilters,
  filterByText,
  filterBySelect,
} = useTableFilter(data, columns, {
  globalSearch: true,
  globalSearchFields: ['name', 'email'],
})
```

### useUnifiedTable

整合所有表格功能的統一 composable。

```typescript
const {
  tableState,
  processedData,
  visibleItems,
  selectedRows,
  toggleSort,
  addFilter,
  toggleRowSelection,
} = useUnifiedTable(data, columns, options)
```

## 效能優化

### 虛擬滾動效能

- **大數據量支援**：可處理 10 萬+ 項目而不影響效能
- **記憶體優化**：只保留可見項目的 DOM 節點
- **滾動流暢度**：使用節流技術確保滾動體驗

### 渲染優化

- **按需渲染**：只渲染可見區域的內容
- **智能更新**：只更新變更的部分
- **緩衝區機制**：預載入上下文項目提升滾動體驗

### 記憶體管理

- **自動清理**：自動清理不可見項目的資源
- **事件優化**：使用 passive 事件監聽器
- **防抖節流**：對高頻操作進行優化

## 測試頁面

訪問 `/admin/tables/test` 查看完整的表格系統測試頁面，包含：

- **效能測試**：不同資料量下的效能比較
- **功能測試**：排序、篩選、選擇等功能測試
- **虛擬滾動測試**：大數據量的虛擬滾動展示
- **主題測試**：不同主題和區域的樣式測試

## 最佳實踐

### 1. 虛擬滾動使用時機

```typescript
// ✅ 適合使用虛擬滾動的場景
const shouldUseVirtualScroll = data.length > 1000

// ✅ 根據資料量動態決定
<UnifiedTable
  :enable-virtual-scroll="data.length > 1000"
  :virtual-scroll-height="500"
/>
```

### 2. 欄位配置最佳化

```typescript
// ✅ 合理設定欄位寬度
const columns: TableColumn[] = [
  {
    key: 'id',
    title: 'ID',
    width: 80,        // 固定寬度
    minWidth: 60,     // 最小寬度
  },
  {
    key: 'description',
    title: '描述',
    // 不設定 width，讓其自適應
    minWidth: 200,
  },
]
```

### 3. 效能監控

```typescript
// ✅ 監控表格效能
const startTime = performance.now()
// 表格操作
const endTime = performance.now()
console.log(`表格渲染時間: ${endTime - startTime}ms`)
```

## 故障排除

### 常見問題

1. **虛擬滾動不生效**
   - 確保設定了 `enable-virtual-scroll="true"`
   - 檢查 `virtual-scroll-height` 是否合理
   - 確認資料量足夠大（建議 > 100 項）

2. **排序功能異常**
   - 檢查欄位配置中的 `sortable: true`
   - 確認 `sortType` 設定正確
   - 檢查自定義排序函數

3. **篩選功能不正常**
   - 確認欄位配置中的 `filterable: true`
   - 檢查 `filterType` 和 `filterOptions` 設定
   - 確認篩選值的資料類型

4. **效能問題**
   - 啟用虛擬滾動
   - 減少不必要的欄位
   - 優化自定義渲染函數

## 更新日誌

### v1.0.0 (2024-12-19)
- ✅ 初始版本發布
- ✅ 虛擬滾動支援
- ✅ 統一排序和篩選系統
- ✅ 區域主題支援
- ✅ 完整的 TypeScript 支援
- ✅ 無障礙設計實作
