<!--
  表格系統測試頁面
  
  用於測試和展示統一表格系統的功能
  包含虛擬滾動、排序、篩選等功能測試
-->

<template>
  <div :class="pageClasses">
    <!-- 頁面標題 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold tracking-tight">統一表格系統測試</h1>
      <p class="text-muted-foreground mt-2">測試 HorizAI 統一表格組件和虛擬滾動系統</p>
    </div>

    <!-- 控制面板 -->
    <Card class="mb-8">
      <CardHeader>
        <CardTitle>測試控制</CardTitle>
        <CardDescription>調整表格設定和測試參數</CardDescription>
      </CardHeader>
      <CardContent class="space-y-6">
        <!-- 主題和區域控制 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div class="space-y-2">
            <Label>主題模式</Label>
            <Button @click="toggleTheme" variant="outline" class="w-full">
              <Sun v-if="!isDarkMode" class="w-4 h-4 mr-2" />
              <Moon v-else class="w-4 h-4 mr-2" />
              {{ isDarkMode ? '深色' : '淺色' }}
            </Button>
          </div>

          <div class="space-y-2">
            <Label>區域主題</Label>
            <Select v-model="currentRegion">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="admin">Admin 區域</SelectItem>
                <SelectItem value="workspace">Workspace 區域</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div class="space-y-2">
            <Label>資料量</Label>
            <Select v-model="dataSize">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="small">小量 (100 項)</SelectItem>
                <SelectItem value="medium">中量 (1,000 項)</SelectItem>
                <SelectItem value="large">大量 (10,000 項)</SelectItem>
                <SelectItem value="huge">超大量 (100,000 項)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div class="space-y-2">
            <Label>表格模式</Label>
            <Select v-model="tableMode">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="normal">一般表格</SelectItem>
                <SelectItem value="virtual">虛擬滾動</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- 功能開關 -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="flex items-center space-x-2">
            <Checkbox id="sort" v-model:checked="enableSort" />
            <Label for="sort">啟用排序</Label>
          </div>

          <div class="flex items-center space-x-2">
            <Checkbox id="filter" v-model:checked="enableFilter" />
            <Label for="filter">啟用篩選</Label>
          </div>

          <div class="flex items-center space-x-2">
            <Checkbox id="search" v-model:checked="enableGlobalSearch" />
            <Label for="search">全域搜尋</Label>
          </div>

          <div class="flex items-center space-x-2">
            <Checkbox id="select" v-model:checked="enableSelection" />
            <Label for="select">行選擇</Label>
          </div>
        </div>

        <!-- 效能資訊 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted rounded-lg">
          <div class="text-center">
            <div class="text-2xl font-bold">{{ tableData.length.toLocaleString() }}</div>
            <div class="text-sm text-muted-foreground">總項目數</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold">{{ renderTime }}ms</div>
            <div class="text-sm text-muted-foreground">渲染時間</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold">{{ memoryUsage }}MB</div>
            <div class="text-sm text-muted-foreground">記憶體使用</div>
          </div>
        </div>

        <!-- 操作按鈕 -->
        <div class="flex items-center space-x-2">
          <Button @click="generateData" :disabled="isGenerating">
            <RefreshCw :class="{ 'animate-spin': isGenerating }" class="w-4 h-4 mr-2" />
            重新生成資料
          </Button>

          <Button @click="measurePerformance" variant="outline">
            <BarChart class="w-4 h-4 mr-2" />
            效能測試
          </Button>

          <Button @click="exportData" variant="outline">
            <Download class="w-4 h-4 mr-2" />
            匯出資料
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 表格展示 -->
    <Card>
      <CardHeader>
        <CardTitle>
          {{ tableMode === 'virtual' ? '虛擬滾動表格' : '一般表格' }}
          <Badge variant="secondary" class="ml-2">
            {{ tableData.length.toLocaleString() }} 項目
          </Badge>
        </CardTitle>
        <CardDescription>
          {{
            tableMode === 'virtual'
              ? '使用虛擬滾動技術，只渲染可見區域的項目，支援大數據量高效能顯示'
              : '傳統表格渲染，適合小到中等數據量的顯示'
          }}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <UnifiedTable
          :data="tableData"
          :columns="tableColumns"
          :enable-virtual-scroll="tableMode === 'virtual'"
          :virtual-scroll-height="500"
          :enable-sort="enableSort"
          :enable-filter="enableFilter"
          :enable-global-search="enableGlobalSearch"
          :selectable="enableSelection"
          :loading="isGenerating"
          title="測試資料表格"
          @row-click="handleRowClick"
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
          @filter-change="handleFilterChange"
        >
          <!-- 自定義狀態欄位 -->
          <template #cell-status="{ value }">
            <Badge :variant="getStatusVariant(value)">
              {{ getStatusLabel(value) }}
            </Badge>
          </template>

          <!-- 自定義操作欄位 -->
          <template #cell-actions="{ item }">
            <div class="flex items-center space-x-1">
              <Button variant="ghost" size="sm" @click.stop="editItem(item)">
                <Edit class="w-3 h-3" />
              </Button>
              <Button variant="ghost" size="sm" @click.stop="deleteItem(item)">
                <Trash2 class="w-3 h-3" />
              </Button>
            </div>
          </template>
        </UnifiedTable>
      </CardContent>
    </Card>

    <!-- 效能分析 -->
    <Card v-if="performanceData.length > 0" class="mt-8">
      <CardHeader>
        <CardTitle>效能分析</CardTitle>
        <CardDescription>不同資料量和模式下的效能比較</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div
            v-for="(data, index) in performanceData"
            :key="index"
            class="flex items-center justify-between p-4 border rounded-lg"
          >
            <div>
              <div class="font-medium">{{ data.mode }} - {{ data.size }} 項目</div>
              <div class="text-sm text-muted-foreground">{{ data.timestamp }}</div>
            </div>
            <div class="text-right">
              <div class="font-mono text-sm">渲染: {{ data.renderTime }}ms</div>
              <div class="font-mono text-sm">記憶體: {{ data.memoryUsage }}MB</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { Sun, Moon, RefreshCw, BarChart, Download, Edit, Trash2 } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useTheme } from '@/composables/useTheme';
import { useRegionTheme } from '@/composables/useRegionTheme';
import { UnifiedTable, userTableColumns, type TableColumn } from '@/components/tables';

// ============================================================================
// Composables
// ============================================================================

const { isDarkMode, toggleTheme } = useTheme();
const { currentRegion, getPageClasses, setRegion } = useRegionTheme();

// ============================================================================
// 狀態
// ============================================================================

const dataSize = ref<'small' | 'medium' | 'large' | 'huge'>('medium');
const tableMode = ref<'normal' | 'virtual'>('virtual');
const enableSort = ref(true);
const enableFilter = ref(true);
const enableGlobalSearch = ref(true);
const enableSelection = ref(true);
const isGenerating = ref(false);
const renderTime = ref(0);
const memoryUsage = ref(0);
const performanceData = ref<
  Array<{
    mode: string;
    size: number;
    renderTime: number;
    memoryUsage: number;
    timestamp: string;
  }>
>([]);

// 表格資料
const tableData = ref<any[]>([]);

// ============================================================================
// 計算屬性
// ============================================================================

const pageClasses = computed(() => getPageClasses.value);

const tableColumns = computed<TableColumn[]>(() => [
  {
    key: 'id',
    title: 'ID',
    sortable: true,
    filterable: true,
    filterType: 'number',
    sortType: 'number',
    width: 80,
    align: 'center',
  },
  {
    key: 'name',
    title: '姓名',
    sortable: true,
    filterable: true,
    filterType: 'text',
    sortType: 'string',
    width: 150,
  },
  {
    key: 'email',
    title: '電子郵件',
    sortable: true,
    filterable: true,
    filterType: 'text',
    sortType: 'string',
    width: 200,
  },
  {
    key: 'department',
    title: '部門',
    sortable: true,
    filterable: true,
    filterType: 'select',
    sortType: 'string',
    width: 120,
    filterOptions: [
      { label: '工程部', value: 'engineering' },
      { label: '產品部', value: 'product' },
      { label: '設計部', value: 'design' },
      { label: '行銷部', value: 'marketing' },
      { label: '業務部', value: 'sales' },
    ],
  },
  {
    key: 'status',
    title: '狀態',
    sortable: true,
    filterable: true,
    filterType: 'select',
    sortType: 'string',
    width: 100,
    align: 'center',
    filterOptions: [
      { label: '啟用', value: 'active' },
      { label: '停用', value: 'inactive' },
      { label: '暫停', value: 'suspended' },
    ],
  },
  {
    key: 'salary',
    title: '薪資',
    sortable: true,
    filterable: true,
    filterType: 'number',
    sortType: 'number',
    width: 120,
    align: 'right',
    render: (value) => `NT$ ${value.toLocaleString()}`,
  },
  {
    key: 'join_date',
    title: '加入日期',
    sortable: true,
    filterable: true,
    filterType: 'date',
    sortType: 'date',
    width: 120,
    render: (value) => new Date(value).toLocaleDateString('zh-TW'),
  },
  {
    key: 'actions',
    title: '操作',
    sortable: false,
    filterable: false,
    width: 100,
    align: 'center',
    fixed: 'right',
  },
]);

// ============================================================================
// 方法
// ============================================================================

const generateData = async () => {
  isGenerating.value = true;
  const startTime = performance.now();

  try {
    const sizes = {
      small: 100,
      medium: 1000,
      large: 10000,
      huge: 100000,
    };

    const count = sizes[dataSize.value];
    const data = [];

    const departments = ['engineering', 'product', 'design', 'marketing', 'sales'];
    const statuses = ['active', 'inactive', 'suspended'];

    for (let i = 1; i <= count; i++) {
      data.push({
        id: i,
        name: `使用者 ${i}`,
        email: `user${i}@example.com`,
        department: departments[Math.floor(Math.random() * departments.length)],
        status: statuses[Math.floor(Math.random() * statuses.length)],
        salary: Math.floor(Math.random() * 100000) + 30000,
        join_date: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      });
    }

    tableData.value = data;

    const endTime = performance.now();
    renderTime.value = Math.round(endTime - startTime);

    // 估算記憶體使用量
    const dataSizeInBytes = JSON.stringify(data).length;
    memoryUsage.value = Math.round((dataSizeInBytes / 1024 / 1024) * 100) / 100;
  } finally {
    isGenerating.value = false;
  }
};

const measurePerformance = async () => {
  const modes = ['normal', 'virtual'];
  const sizes = ['small', 'medium', 'large'];

  for (const mode of modes) {
    for (const size of sizes) {
      tableMode.value = mode as any;
      dataSize.value = size as any;

      await generateData();

      performanceData.value.push({
        mode: mode === 'virtual' ? '虛擬滾動' : '一般表格',
        size: tableData.value.length,
        renderTime: renderTime.value,
        memoryUsage: memoryUsage.value,
        timestamp: new Date().toLocaleTimeString(),
      });

      // 等待一下讓 UI 更新
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
  }
};

const exportData = () => {
  const dataStr = JSON.stringify(tableData.value, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(dataBlob);

  const link = document.createElement('a');
  link.href = url;
  link.download = `table-data-${dataSize.value}-${Date.now()}.json`;
  link.click();

  URL.revokeObjectURL(url);
};

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'active':
      return 'default';
    case 'inactive':
      return 'secondary';
    case 'suspended':
      return 'destructive';
    default:
      return 'outline';
  }
};

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'active':
      return '啟用';
    case 'inactive':
      return '停用';
    case 'suspended':
      return '暫停';
    default:
      return status;
  }
};

const handleRowClick = (item: any, index: number) => {
  console.log('Row clicked:', item, index);
};

const handleSelectionChange = (selectedKeys: (string | number)[]) => {
  console.log('Selection changed:', selectedKeys);
};

const handleSortChange = (sortConfigs: any[]) => {
  console.log('Sort changed:', sortConfigs);
};

const handleFilterChange = (filters: any[]) => {
  console.log('Filter changed:', filters);
};

const editItem = (item: any) => {
  console.log('Edit item:', item);
};

const deleteItem = (item: any) => {
  console.log('Delete item:', item);
};

// ============================================================================
// 監聽器
// ============================================================================

watch(currentRegion, (newRegion) => {
  setRegion(newRegion);
});

watch(dataSize, () => {
  generateData();
});

// ============================================================================
// 生命週期
// ============================================================================

onMounted(() => {
  generateData();
});
</script>
