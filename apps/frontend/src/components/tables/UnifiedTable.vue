<!--
  統一表格組件
  
  支援虛擬滾動、排序、篩選的高效能表格組件
  基於 HorizAI Design System 和區域主題差異
-->

<template>
  <div :class="containerClasses">
    <!-- 表格標題和操作區 -->
    <div v-if="showHeader" :class="headerClasses">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <h3 v-if="title" :class="titleClasses">{{ title }}</h3>
          <Badge v-if="showCount" variant="secondary">
            {{ tableState.filteredCount }} / {{ tableState.totalCount }} 項目
          </Badge>
        </div>
        
        <div class="flex items-center space-x-2">
          <!-- 全域搜尋 -->
          <div v-if="enableGlobalSearch" class="relative">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              v-model="globalSearchQuery"
              placeholder="搜尋..."
              :class="searchInputClasses"
            />
          </div>
          
          <!-- 篩選指示器 -->
          <Button
            v-if="hasAnyFilter"
            variant="outline"
            size="sm"
            @click="clearFilters"
          >
            <Filter class="w-4 h-4 mr-1" />
            {{ activeFilterCount }} 個篩選
            <X class="w-3 h-3 ml-1" />
          </Button>
          
          <!-- 操作插槽 -->
          <slot name="actions" />
        </div>
      </div>
    </div>

    <!-- 表格容器 -->
    <div :class="tableContainerClasses">
      <!-- 虛擬滾動容器 -->
      <div
        v-if="enableVirtualScroll"
        ref="containerRef"
        :class="virtualScrollClasses"
        :style="{ height: `${virtualScrollHeight}px` }"
        @scroll="handleScroll"
      >
        <!-- 表格標題 -->
        <div :class="stickyHeaderClasses">
          <table :class="tableClasses">
            <thead>
              <tr>
                <!-- 選擇欄 -->
                <th v-if="selectable" :class="checkboxHeaderClasses">
                  <Checkbox
                    :checked="isAllSelected"
                    :indeterminate="isIndeterminate"
                    @update:checked="handleSelectAll"
                  />
                </th>
                
                <!-- 資料欄位 -->
                <th
                  v-for="column in visibleColumns"
                  :key="column.key"
                  :class="getHeaderClasses(column)"
                  :style="getColumnStyle(column)"
                >
                  <div class="flex items-center space-x-2">
                    <span>{{ column.title }}</span>
                    
                    <!-- 排序按鈕 -->
                    <Button
                      v-if="column.sortable && enableSort"
                      variant="ghost"
                      size="sm"
                      class="h-4 w-4 p-0"
                      @click="toggleSort(column.key)"
                    >
                      <ArrowUpDown v-if="!isSorted(column.key)" class="h-3 w-3 opacity-50" />
                      <ArrowUp v-else-if="getSortDirection(column.key) === 'asc'" class="h-3 w-3" />
                      <ArrowDown v-else class="h-3 w-3" />
                    </Button>
                    
                    <!-- 篩選按鈕 -->
                    <Button
                      v-if="column.filterable && enableFilter"
                      variant="ghost"
                      size="sm"
                      class="h-4 w-4 p-0"
                      :class="{ 'text-primary': hasFilter(column.key) }"
                      @click="toggleColumnFilter(column.key)"
                    >
                      <Filter class="h-3 w-3" />
                    </Button>
                  </div>
                </th>
              </tr>
            </thead>
          </table>
        </div>

        <!-- 虛擬滾動內容 -->
        <div :style="{ height: `${totalHeight}px`, position: 'relative' }">
          <div :style="{ transform: `translateY(${offsetY}px)` }">
            <table :class="tableClasses">
              <tbody>
                <!-- 載入狀態 -->
                <tr v-if="tableState.loading">
                  <td :colspan="columnCount" :class="loadingCellClasses">
                    <div class="flex items-center justify-center py-8">
                      <Loader2 class="w-4 h-4 mr-2 animate-spin" />
                      載入中...
                    </div>
                  </td>
                </tr>
                
                <!-- 空狀態 -->
                <tr v-else-if="!tableState.hasData">
                  <td :colspan="columnCount" :class="emptyCellClasses">
                    <div class="flex flex-col items-center justify-center py-12">
                      <div class="text-muted-foreground text-center">
                        <h4 class="text-sm font-medium">{{ emptyState.title }}</h4>
                        <p class="text-xs mt-1">{{ emptyState.description }}</p>
                      </div>
                    </div>
                  </td>
                </tr>
                
                <!-- 資料行 -->
                <tr
                  v-else
                  v-for="item in visibleItems"
                  :key="getRowKey(item.data, item.index)"
                  :class="getRowClasses(item.data, item.index)"
                  @click="handleRowClick(item.data, item.index)"
                >
                  <!-- 選擇欄 -->
                  <td v-if="selectable" :class="checkboxCellClasses">
                    <Checkbox
                      :checked="isRowSelected(getRowKey(item.data, item.index))"
                      @update:checked="() => toggleRowSelection(getRowKey(item.data, item.index))"
                      @click.stop
                    />
                  </td>
                  
                  <!-- 資料欄位 -->
                  <td
                    v-for="column in visibleColumns"
                    :key="column.key"
                    :class="getCellClasses(column)"
                    :style="getColumnStyle(column)"
                  >
                    <slot
                      :name="`cell-${column.key}`"
                      :item="item.data"
                      :value="getCellValue(item.data, column)"
                      :index="item.index"
                      :column="column"
                    >
                      {{ getCellValue(item.data, column) }}
                    </slot>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- 傳統表格（非虛擬滾動） -->
      <table v-else :class="tableClasses">
        <thead>
          <tr>
            <!-- 選擇欄 -->
            <th v-if="selectable" :class="checkboxHeaderClasses">
              <Checkbox
                :checked="isAllSelected"
                :indeterminate="isIndeterminate"
                @update:checked="handleSelectAll"
              />
            </th>
            
            <!-- 資料欄位 -->
            <th
              v-for="column in visibleColumns"
              :key="column.key"
              :class="getHeaderClasses(column)"
              :style="getColumnStyle(column)"
            >
              <div class="flex items-center space-x-2">
                <span>{{ column.title }}</span>
                
                <!-- 排序按鈕 -->
                <Button
                  v-if="column.sortable && enableSort"
                  variant="ghost"
                  size="sm"
                  class="h-4 w-4 p-0"
                  @click="toggleSort(column.key)"
                >
                  <ArrowUpDown v-if="!isSorted(column.key)" class="h-3 w-3 opacity-50" />
                  <ArrowUp v-else-if="getSortDirection(column.key) === 'asc'" class="h-3 w-3" />
                  <ArrowDown v-else class="h-3 w-3" />
                </Button>
                
                <!-- 篩選按鈕 -->
                <Button
                  v-if="column.filterable && enableFilter"
                  variant="ghost"
                  size="sm"
                  class="h-4 w-4 p-0"
                  :class="{ 'text-primary': hasFilter(column.key) }"
                  @click="toggleColumnFilter(column.key)"
                >
                  <Filter class="h-3 w-3" />
                </Button>
              </div>
            </th>
          </tr>
        </thead>
        
        <tbody>
          <!-- 載入狀態 -->
          <tr v-if="tableState.loading">
            <td :colspan="columnCount" :class="loadingCellClasses">
              <div class="flex items-center justify-center py-8">
                <Loader2 class="w-4 h-4 mr-2 animate-spin" />
                載入中...
              </div>
            </td>
          </tr>
          
          <!-- 空狀態 -->
          <tr v-else-if="!tableState.hasData">
            <td :colspan="columnCount" :class="emptyCellClasses">
              <div class="flex flex-col items-center justify-center py-12">
                <div class="text-muted-foreground text-center">
                  <h4 class="text-sm font-medium">{{ emptyState.title }}</h4>
                  <p class="text-xs mt-1">{{ emptyState.description }}</p>
                </div>
              </div>
            </td>
          </tr>
          
          <!-- 資料行 -->
          <tr
            v-else
            v-for="(item, index) in processedData"
            :key="getRowKey(item, index)"
            :class="getRowClasses(item, index)"
            @click="handleRowClick(item, index)"
          >
            <!-- 選擇欄 -->
            <td v-if="selectable" :class="checkboxCellClasses">
              <Checkbox
                :checked="isRowSelected(getRowKey(item, index))"
                @update:checked="() => toggleRowSelection(getRowKey(item, index))"
                @click.stop
              />
            </td>
            
            <!-- 資料欄位 -->
            <td
              v-for="column in visibleColumns"
              :key="column.key"
              :class="getCellClasses(column)"
              :style="getColumnStyle(column)"
            >
              <slot
                :name="`cell-${column.key}`"
                :item="item"
                :value="getCellValue(item, column)"
                :index="index"
                :column="column"
              >
                {{ getCellValue(item, column) }}
              </slot>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 表格底部資訊 -->
    <div v-if="showFooter" :class="footerClasses">
      <div class="text-sm text-muted-foreground">
        <span v-if="tableState.filteredCount !== tableState.totalCount">
          顯示 {{ tableState.filteredCount }} 項，共 {{ tableState.totalCount }} 項
        </span>
        <span v-else>
          共 {{ tableState.totalCount }} 項
        </span>
        
        <span v-if="selectedRows.size > 0" class="ml-4">
          已選擇 {{ selectedRows.size }} 項
        </span>
      </div>
      
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Search, Filter, X, ArrowUpDown, ArrowUp, ArrowDown, Loader2 } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { useUnifiedTable, type TableColumn, type UnifiedTableOptions } from '@/composables/tables/useUnifiedTable'
import { useRegionTheme } from '@/composables/useRegionTheme'

// ============================================================================
// 類型定義
// ============================================================================

interface Props {
  /** 表格資料 */
  data: any[]
  /** 表格欄位配置 */
  columns: TableColumn[]
  /** 表格標題 */
  title?: string
  /** 是否顯示標題區 */
  showHeader?: boolean
  /** 是否顯示底部資訊 */
  showFooter?: boolean
  /** 是否顯示項目計數 */
  showCount?: boolean
  /** 是否可選擇行 */
  selectable?: boolean
  /** 是否啟用虛擬滾動 */
  enableVirtualScroll?: boolean
  /** 虛擬滾動容器高度 */
  virtualScrollHeight?: number
  /** 是否啟用排序 */
  enableSort?: boolean
  /** 是否啟用篩選 */
  enableFilter?: boolean
  /** 是否啟用全域搜尋 */
  enableGlobalSearch?: boolean
  /** 載入狀態 */
  loading?: boolean
  /** 空狀態配置 */
  emptyState?: {
    title?: string
    description?: string
  }
  /** 行點擊回調 */
  onRowClick?: (item: any, index: number) => void
  /** 統一表格選項 */
  options?: UnifiedTableOptions
}

const props = withDefaults(defineProps<Props>(), {
  showHeader: true,
  showFooter: true,
  showCount: true,
  selectable: false,
  enableVirtualScroll: false,
  virtualScrollHeight: 400,
  enableSort: true,
  enableFilter: true,
  enableGlobalSearch: true,
  loading: false,
  emptyState: () => ({
    title: '沒有資料',
    description: '目前沒有可顯示的項目',
  }),
})

// ============================================================================
// Emits
// ============================================================================

const emit = defineEmits<{
  'row-click': [item: any, index: number]
  'selection-change': [selectedKeys: (string | number)[]]
  'sort-change': [sortConfigs: any[]]
  'filter-change': [filters: any[]]
}>()

// ============================================================================
// Composables
// ============================================================================

const { getTableClasses, isAdmin } = useRegionTheme()

// 統一表格功能
const unifiedTableOptions: UnifiedTableOptions = {
  enableVirtualScroll: props.enableVirtualScroll,
  enableSort: props.enableSort,
  enableFilter: props.enableFilter,
  loading: props.loading,
  emptyState: props.emptyState,
  virtualScroll: props.enableVirtualScroll ? {
    itemHeight: 48, // 預設行高
    containerHeight: props.virtualScrollHeight,
    buffer: 5,
  } : undefined,
  ...props.options,
}

const {
  containerRef,
  tableState,
  processedData,
  visibleColumns,
  visibleItems,
  totalHeight,
  offsetY,
  selectedRows,
  globalSearchQuery,
  hasAnyFilter,
  activeFilterCount,
  isAllSelected,
  isIndeterminate,
  getSortDirection,
  isSorted,
  hasFilter,
  toggleSort,
  clearFilters,
  isRowSelected,
  toggleRowSelection,
  selectAllRows,
  clearSelection,
  getRowKey,
  getCellValue,
  handleScroll,
} = useUnifiedTable(
  computed(() => props.data),
  computed(() => props.columns),
  unifiedTableOptions
)

// ============================================================================
// 計算屬性
// ============================================================================

const columnCount = computed(() => {
  let count = visibleColumns.value.length
  if (props.selectable) count += 1
  return count
})

// 樣式類別
const containerClasses = computed(() => {
  const baseClasses = ['w-full']
  if (isAdmin.value) {
    baseClasses.push('space-y-3')
  } else {
    baseClasses.push('space-y-4')
  }
  return baseClasses.join(' ')
})

const headerClasses = computed(() => {
  return isAdmin.value 
    ? 'pb-3 border-b border-gray-200 dark:border-gray-700'
    : 'pb-4 border-b border-zinc-200/50 dark:border-zinc-800/50'
})

const titleClasses = computed(() => {
  return isAdmin.value 
    ? 'text-lg font-semibold text-gray-900 dark:text-gray-100'
    : 'text-xl font-semibold text-zinc-900 dark:text-zinc-100'
})

const searchInputClasses = computed(() => {
  return 'pl-9 w-64'
})

const tableContainerClasses = computed(() => {
  const baseClasses = ['rounded-md border overflow-hidden']
  if (isAdmin.value) {
    baseClasses.push('border-gray-200 dark:border-gray-700')
  } else {
    baseClasses.push('border-zinc-200/50 dark:border-zinc-800/50')
  }
  return baseClasses.join(' ')
})

const virtualScrollClasses = computed(() => {
  return 'overflow-auto'
})

const stickyHeaderClasses = computed(() => {
  const baseClasses = ['sticky top-0 z-10']
  if (isAdmin.value) {
    baseClasses.push('bg-gray-50 dark:bg-gray-800')
  } else {
    baseClasses.push('bg-zinc-50 dark:bg-zinc-900')
  }
  return baseClasses.join(' ')
})

const tableClasses = computed(() => {
  return 'w-full caption-bottom text-sm'
})

const checkboxHeaderClasses = computed(() => {
  return 'w-12 px-4 py-3 text-left align-middle font-medium text-muted-foreground'
})

const checkboxCellClasses = computed(() => {
  return 'w-12 px-4 py-3 align-middle'
})

const loadingCellClasses = computed(() => {
  return 'text-center text-muted-foreground'
})

const emptyCellClasses = computed(() => {
  return 'text-center'
})

const footerClasses = computed(() => {
  const baseClasses = ['flex items-center justify-between pt-3']
  if (isAdmin.value) {
    baseClasses.push('border-t border-gray-200 dark:border-gray-700')
  } else {
    baseClasses.push('border-t border-zinc-200/50 dark:border-zinc-800/50')
  }
  return baseClasses.join(' ')
})

// ============================================================================
// 方法
// ============================================================================

const getHeaderClasses = (column: TableColumn) => {
  const baseClasses = ['px-4 py-3 text-left align-middle font-medium text-muted-foreground']
  
  if (column.align === 'center') {
    baseClasses.push('text-center')
  } else if (column.align === 'right') {
    baseClasses.push('text-right')
  }
  
  if (column.headerClass) {
    baseClasses.push(column.headerClass)
  }
  
  return baseClasses.join(' ')
}

const getCellClasses = (column: TableColumn) => {
  const baseClasses = ['px-4 py-3 align-middle']
  
  if (column.align === 'center') {
    baseClasses.push('text-center')
  } else if (column.align === 'right') {
    baseClasses.push('text-right')
  }
  
  if (column.cellClass) {
    baseClasses.push(column.cellClass)
  }
  
  return baseClasses.join(' ')
}

const getRowClasses = (item: any, index: number) => {
  const baseClasses = ['border-b transition-colors hover:bg-muted/50']
  
  if (isRowSelected(getRowKey(item, index))) {
    baseClasses.push('bg-muted')
  }
  
  if (isAdmin.value) {
    baseClasses.push('border-gray-100 dark:border-gray-700')
  } else {
    baseClasses.push('border-zinc-100 dark:border-zinc-800')
  }
  
  return baseClasses.join(' ')
}

const getColumnStyle = (column: TableColumn) => {
  const style: Record<string, string> = {}
  
  if (column.width) {
    style.width = typeof column.width === 'number' ? `${column.width}px` : column.width
  }
  
  if (column.minWidth) {
    style.minWidth = `${column.minWidth}px`
  }
  
  return style
}

const handleSelectAll = (checked: boolean) => {
  if (checked) {
    selectAllRows()
  } else {
    clearSelection()
  }
  
  emit('selection-change', Array.from(selectedRows.value))
}

const handleRowClick = (item: any, index: number) => {
  emit('row-click', item, index)
  props.onRowClick?.(item, index)
}

const toggleColumnFilter = (columnKey: string) => {
  // 這裡可以實作欄位篩選的 UI 邏輯
  console.log('Toggle filter for column:', columnKey)
}
</script>
