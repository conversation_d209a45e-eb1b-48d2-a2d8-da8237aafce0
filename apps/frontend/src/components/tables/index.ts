/**
 * HorizAI 統一表格系統
 * 
 * 導出所有表格相關組件和工具函數
 * 支援虛擬滾動、排序、篩選的高效能表格解決方案
 * 
 * <AUTHOR> Design System Team
 * @version 1.0.0
 */

// ============================================================================
// 組件導出
// ============================================================================

export { default as UnifiedTable } from './UnifiedTable.vue'

// ============================================================================
// Composables 導出
// ============================================================================

export {
  useVirtualScroll,
  type VirtualScrollOptions,
  type VirtualScrollState,
  type VirtualScrollItem,
} from '@/composables/tables/useVirtualScroll'

export {
  useTableSort,
  type SortDirection,
  type SortConfig,
  type SortableColumn,
  type TableSortOptions,
} from '@/composables/tables/useTableSort'

export {
  useTableFilter,
  type FilterOperator,
  type FilterConfig,
  type FilterableColumn,
  type TableFilterOptions,
} from '@/composables/tables/useTableFilter'

export {
  useUnifiedTable,
  type TableColumn,
  type UnifiedTableOptions,
  type TableState,
} from '@/composables/tables/useUnifiedTable'

// ============================================================================
// 常用表格配置
// ============================================================================

/**
 * 使用者表格欄位配置
 */
export const userTableColumns: TableColumn[] = [
  {
    key: 'name',
    title: '姓名',
    sortable: true,
    filterable: true,
    filterType: 'text',
    sortType: 'string',
    width: 200,
    minWidth: 150,
  },
  {
    key: 'email',
    title: '電子郵件',
    sortable: true,
    filterable: true,
    filterType: 'text',
    sortType: 'string',
    width: 250,
    minWidth: 200,
  },
  {
    key: 'role',
    title: '角色',
    sortable: true,
    filterable: true,
    filterType: 'select',
    sortType: 'string',
    width: 120,
    filterOptions: [
      { label: '系統管理員', value: 'SYSTEM_ADMIN' },
      { label: '租戶管理員', value: 'TENANT_ADMIN' },
      { label: '租戶使用者', value: 'TENANT_USER' },
    ],
  },
  {
    key: 'status',
    title: '狀態',
    sortable: true,
    filterable: true,
    filterType: 'select',
    sortType: 'string',
    width: 100,
    align: 'center',
    filterOptions: [
      { label: '啟用', value: 'active' },
      { label: '停用', value: 'inactive' },
      { label: '暫停', value: 'suspended' },
    ],
  },
  {
    key: 'created_at',
    title: '建立時間',
    sortable: true,
    filterable: true,
    filterType: 'date',
    sortType: 'date',
    width: 180,
    render: (value) => new Date(value).toLocaleDateString('zh-TW'),
  },
  {
    key: 'actions',
    title: '操作',
    sortable: false,
    filterable: false,
    width: 120,
    align: 'right',
    fixed: 'right',
  },
]

/**
 * 租戶表格欄位配置
 */
export const tenantTableColumns: TableColumn[] = [
  {
    key: 'name',
    title: '租戶名稱',
    sortable: true,
    filterable: true,
    filterType: 'text',
    sortType: 'string',
    width: 200,
    minWidth: 150,
  },
  {
    key: 'domain',
    title: '網域',
    sortable: true,
    filterable: true,
    filterType: 'text',
    sortType: 'string',
    width: 180,
  },
  {
    key: 'company_size',
    title: '公司規模',
    sortable: true,
    filterable: true,
    filterType: 'select',
    sortType: 'string',
    width: 120,
    filterOptions: [
      { label: '1-10 人', value: '1-10' },
      { label: '11-50 人', value: '11-50' },
      { label: '51-200 人', value: '51-200' },
      { label: '201-500 人', value: '201-500' },
      { label: '500+ 人', value: '500+' },
    ],
  },
  {
    key: 'industry',
    title: '產業',
    sortable: true,
    filterable: true,
    filterType: 'select',
    sortType: 'string',
    width: 120,
    filterOptions: [
      { label: '科技業', value: 'technology' },
      { label: '金融業', value: 'finance' },
      { label: '製造業', value: 'manufacturing' },
      { label: '零售業', value: 'retail' },
      { label: '醫療保健', value: 'healthcare' },
      { label: '教育', value: 'education' },
      { label: '其他', value: 'other' },
    ],
  },
  {
    key: 'status',
    title: '狀態',
    sortable: true,
    filterable: true,
    filterType: 'select',
    sortType: 'string',
    width: 100,
    align: 'center',
    filterOptions: [
      { label: '啟用', value: 'active' },
      { label: '停用', value: 'inactive' },
      { label: '暫停', value: 'suspended' },
    ],
  },
  {
    key: 'user_count',
    title: '使用者數',
    sortable: true,
    filterable: true,
    filterType: 'number',
    sortType: 'number',
    width: 100,
    align: 'center',
  },
  {
    key: 'created_at',
    title: '建立時間',
    sortable: true,
    filterable: true,
    filterType: 'date',
    sortType: 'date',
    width: 180,
    render: (value) => new Date(value).toLocaleDateString('zh-TW'),
  },
  {
    key: 'actions',
    title: '操作',
    sortable: false,
    filterable: false,
    width: 120,
    align: 'right',
    fixed: 'right',
  },
]

/**
 * 訂單表格欄位配置
 */
export const orderTableColumns: TableColumn[] = [
  {
    key: 'order_number',
    title: '訂單編號',
    sortable: true,
    filterable: true,
    filterType: 'text',
    sortType: 'string',
    width: 150,
    minWidth: 120,
  },
  {
    key: 'tenant.name',
    title: '租戶',
    sortable: true,
    filterable: true,
    filterType: 'text',
    sortType: 'string',
    width: 180,
  },
  {
    key: 'plan.name',
    title: '方案',
    sortable: true,
    filterable: true,
    filterType: 'text',
    sortType: 'string',
    width: 150,
  },
  {
    key: 'amount',
    title: '金額',
    sortable: true,
    filterable: true,
    filterType: 'number',
    sortType: 'number',
    width: 120,
    align: 'right',
    render: (value) => `NT$ ${value.toLocaleString()}`,
  },
  {
    key: 'status',
    title: '狀態',
    sortable: true,
    filterable: true,
    filterType: 'select',
    sortType: 'string',
    width: 100,
    align: 'center',
    filterOptions: [
      { label: '待付款', value: 'pending' },
      { label: '已付款', value: 'paid' },
      { label: '已取消', value: 'cancelled' },
      { label: '已退款', value: 'refunded' },
    ],
  },
  {
    key: 'payment_method',
    title: '付款方式',
    sortable: true,
    filterable: true,
    filterType: 'select',
    sortType: 'string',
    width: 120,
    filterOptions: [
      { label: '銀行轉帳', value: '銀行轉帳' },
      { label: '信用卡', value: '信用卡' },
      { label: '支票', value: '支票' },
      { label: '現金', value: '現金' },
    ],
  },
  {
    key: 'created_at',
    title: '建立時間',
    sortable: true,
    filterable: true,
    filterType: 'date',
    sortType: 'date',
    width: 180,
    render: (value) => new Date(value).toLocaleDateString('zh-TW'),
  },
  {
    key: 'actions',
    title: '操作',
    sortable: false,
    filterable: false,
    width: 120,
    align: 'right',
    fixed: 'right',
  },
]

// ============================================================================
// 表格工具函數
// ============================================================================

/**
 * 創建基本的表格配置
 */
export function createTableConfig(options: {
  enableVirtualScroll?: boolean
  virtualScrollHeight?: number
  enableSort?: boolean
  enableFilter?: boolean
  enableGlobalSearch?: boolean
  selectable?: boolean
}) {
  return {
    enableVirtualScroll: options.enableVirtualScroll || false,
    virtualScrollHeight: options.virtualScrollHeight || 400,
    enableSort: options.enableSort !== false,
    enableFilter: options.enableFilter !== false,
    enableGlobalSearch: options.enableGlobalSearch !== false,
    selectable: options.selectable || false,
    showHeader: true,
    showFooter: true,
    showCount: true,
  }
}

/**
 * 創建虛擬滾動配置
 */
export function createVirtualScrollConfig(options: {
  itemHeight?: number
  containerHeight?: number
  buffer?: number
}) {
  return {
    itemHeight: options.itemHeight || 48,
    containerHeight: options.containerHeight || 400,
    buffer: options.buffer || 5,
    throttleDelay: 16,
  }
}

/**
 * 獲取區域特定的表格配置
 */
export function getRegionTableConfig(region: 'admin' | 'workspace') {
  if (region === 'admin') {
    return {
      itemHeight: 44, // 緊湊型行高
      containerHeight: 400,
      buffer: 3,
      enableGlobalSearch: true,
      selectable: true,
    }
  } else {
    return {
      itemHeight: 52, // 舒適型行高
      containerHeight: 500,
      buffer: 5,
      enableGlobalSearch: true,
      selectable: false,
    }
  }
}

// ============================================================================
// 常數導出
// ============================================================================

export const TABLE_ITEM_HEIGHTS = {
  COMPACT: 40,
  DEFAULT: 48,
  COMFORTABLE: 56,
} as const

export const TABLE_CONTAINER_HEIGHTS = {
  SMALL: 300,
  DEFAULT: 400,
  LARGE: 600,
  FULL: 800,
} as const

export const FILTER_OPERATORS = [
  'equals',
  'not_equals',
  'contains',
  'not_contains',
  'starts_with',
  'ends_with',
  'greater_than',
  'greater_than_or_equal',
  'less_than',
  'less_than_or_equal',
  'between',
  'in',
  'not_in',
  'is_null',
  'is_not_null',
] as const

export const SORT_DIRECTIONS = ['asc', 'desc'] as const
