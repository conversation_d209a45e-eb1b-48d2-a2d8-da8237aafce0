# TableTestPage.vue 變數衝突修復

## 🐛 問題描述

**錯誤訊息：**
```
TableTestPage.vue:380 Uncaught (in promise) ReferenceError: Cannot access 'dataSize2' before initialization
    at generateData (TableTestPage.vue:380:25)
    at TableTestPage.vue:509:3
```

**錯誤原因：**
在 `generateData` 函數中，第 404 行有一個變數名稱衝突：

```typescript
// 第 246 行：全域狀態定義
const dataSize = ref<'small' | 'medium' | 'large' | 'huge'>('medium')

// 第 404 行：局部變數重新定義（衝突！）
const dataSize = JSON.stringify(data).length  // ❌ 變數名稱衝突
```

這導致在函數內部嘗試訪問 `dataSize.value`（第 380 行）時，JavaScript 引擎因為變數提升（hoisting）而無法正確訪問全域的 `dataSize` ref。

## ✅ 解決方案

**修復內容：**
將局部變數重新命名，避免與全域狀態變數衝突：

```typescript
// 修復前（第 404 行）
const dataSize = JSON.stringify(data).length

// 修復後
const dataSizeInBytes = JSON.stringify(data).length
```

**修復位置：**
- 檔案：`/apps/frontend/src/components/tables/TableTestPage.vue`
- 行數：第 404 行

## 🔍 根本原因分析

### JavaScript 變數提升（Hoisting）

在 JavaScript 中，`const` 和 `let` 宣告會被提升到函數作用域的頂部，但不會被初始化。這意味著：

```typescript
function generateData() {
  // 這裡嘗試訪問 dataSize.value
  const count = sizes[dataSize.value]  // ❌ 錯誤：無法訪問未初始化的變數
  
  // ... 其他程式碼 ...
  
  // 這裡重新定義了 dataSize
  const dataSize = JSON.stringify(data).length  // 變數提升影響上面的訪問
}
```

### 作用域衝突

由於函數內部重新定義了 `dataSize`，JavaScript 引擎認為整個函數作用域內的 `dataSize` 都指向局部變數，而不是外部的 ref。

## 🧪 驗證修復

### 修復前的錯誤流程
1. 函數開始執行
2. 嘗試訪問 `dataSize.value`（第 380 行）
3. JavaScript 引擎發現函數內有 `dataSize` 宣告
4. 但此時局部 `dataSize` 尚未初始化
5. 拋出 "Cannot access before initialization" 錯誤

### 修復後的正確流程
1. 函數開始執行
2. 成功訪問全域 `dataSize.value`（第 380 行）
3. 正常執行其他邏輯
4. 定義局部變數 `dataSizeInBytes`（第 409 行）
5. 函數正常完成

## 📋 檢查清單

- ✅ **變數名稱衝突已解決**：局部變數重新命名為 `dataSizeInBytes`
- ✅ **功能保持不變**：記憶體使用量計算邏輯完全相同
- ✅ **TypeScript 編譯通過**：無語法錯誤
- ✅ **作用域清晰**：全域和局部變數明確區分

## 🔄 預防措施

### 1. 命名規範
```typescript
// ✅ 好的做法：使用描述性的變數名稱
const dataSizeInBytes = JSON.stringify(data).length
const memoryUsageInMB = dataSizeInBytes / 1024 / 1024

// ❌ 避免的做法：重用外部變數名稱
const dataSize = JSON.stringify(data).length  // 與外部 ref 衝突
```

### 2. 作用域檢查
在函數內部定義變數時，確保不與外部作用域的變數名稱衝突。

### 3. ESLint 規則
可以考慮啟用以下 ESLint 規則來預防類似問題：
- `no-shadow`: 禁止變數遮蔽
- `no-redeclare`: 禁止重複宣告

## 🎯 修復效果

修復後，表格測試頁面應該能夠：

1. **正常載入**：頁面不再拋出 JavaScript 錯誤
2. **資料生成**：`generateData` 函數正常執行
3. **效能測試**：記憶體使用量計算正確
4. **功能完整**：所有表格功能正常運作

---

**修復完成時間：** 2024-12-19  
**修復者：** HorizAI Design System Team  
**狀態：** 已完成，已驗證
