# AI Composables 模組化架構

本目錄包含 HorizAI SaaS 平台中所有 AI 相關的 Vue 3 Composables，按功能模組進行組織。

## 📂 目錄結構

```
composables/admin/ai/
├── agents/         # AI Agent 管理模組
├── keys/           # API 金鑰管理模組
├── models/         # AI 模型管理模組
├── settings/       # 設定和配置模組
├── features/       # AI 功能配置模組
└── README.md       # 本文件
```

## 🤖 Agents 模組 (`./agents/`)

負責 AI Agent 的全生命週期管理：

- `useAIAgentManager.ts` - Agent CRUD 操作、列表管理
- `useAIAgentEditor.ts` - Agent 編輯表單邏輯
- `useAIAgentChat.ts` - Agent 對話功能
- `useAIAgentPromptOptimizer.ts` - 提示詞優化功能
- `useAIAgentTesterPage.ts` - Agent 測試頁面邏輯
- `useAgentsManagementTab.ts` - Agent 管理標籤頁

## 🔑 Keys 模組 (`./keys/`)

負責 AI 供應商 API 金鑰管理：

- `useAIKeyManager.ts` - 金鑰 CRUD、驗證、供應商管理

## 🧠 Models 模組 (`./models/`)

負責 AI 模型管理和配置：

- `useAIModelManager.ts` - 模型 CRUD 操作、定價管理
- `useModelsTableSorting.ts` - 模型表格排序邏輯

## ⚙️ Settings 模組 (`./settings/`)

負責系統級別的 AI 設定管理：

- `useAISettings.ts` - 主要設定頁面邏輯，整合其他模組
- `useAiGlobalSettings.ts` - 全域 AI 設定
- `useAiSettingsTabs.ts` - 設定頁面標籤切換邏輯

## 🚀 Features 模組 (`./features/`)

負責 AI 功能的啟用、配置和管理：

- `useAiFeatureConfig.ts` - AI 功能配置、功能定義管理

## 🎯 使用指南

### 基本使用

```typescript
// 從模組直接導入
import { useAIAgentManager } from '@/composables/admin/ai/agents/useAIAgentManager';
import { useAIKeyManager } from '@/composables/admin/ai/keys/useAIKeyManager';

// 或者使用模組的 index 文件
import { useAIAgentManager, useAIAgentEditor } from '@/composables/admin/ai/agents';
import { useAIKeyManager } from '@/composables/admin/ai/keys';
```

### 模組間依賴

- `settings/useAISettings.ts` 作為主要入口點，整合其他模組
- `agents` 模組依賴 `keys` 和 `models` 模組的資料
- `features` 模組與 `agents` 模組有關聯關係

## 🏗️ 架構原則

1. **單一職責原則** - 每個 composable 只負責特定功能領域
2. **模組化設計** - 相關功能組織在同一模組下
3. **清晰的依賴關係** - 避免循環依賴，保持依賴關係清晰
4. **統一的命名規範** - 使用一致的命名約定
5. **類型安全** - 完整的 TypeScript 類型定義

## 🔄 重構歷史

此模組化架構是對原本平鋪在 `composables/admin/ai/` 目錄下的 composables 進行重新組織的結果，目標是：

- 提高代碼可維護性
- 清晰的功能邊界
- 更好的開發體驗
- 便於團隊協作

## 📝 開發注意事項

1. 新增功能時，請根據功能性質選擇對應的模組
2. 跨模組的功能整合建議在 `settings` 模組中進行
3. 每個模組都應該有對應的 `index.ts` 文件方便導入
4. 保持與後端 API 結構的一致性
