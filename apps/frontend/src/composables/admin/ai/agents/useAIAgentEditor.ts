import { ref, computed } from "vue";
import type { AIAgent } from "@/types/models/ai-agent.model";
import type { AiBotProviderType } from "@/types/models/ai.model";
import type { ComponentPublicInstance } from "vue";
import { useUtils } from "@/composables/shared/useUtils";

interface AvailableKey {
  id: string;
  name: string;
  api_key?: string;
  provider: string;
  api_url?: string | null;
  is_enabled: boolean;
  created_at?: string;
  updated_at?: string;
  last_test?: string | null;
}

export function useAIAgentEditor() {
  const { formatDate } = useUtils();

  const nameInput = ref<ComponentPublicInstance | null>(null);

  /**
   * 過濾啟用的模型
   */
  const getActiveModels = (
    availableModels: { id: string; name: string; is_enabled?: boolean }[]
  ) => {
    return availableModels.filter((model) => model.is_enabled !== false);
  };

  /**
   * 格式化最後更新時間
   */
  const getLastUpdatedAt = (agent: AIAgent | null) => {
    return computed(() => {
      return formatDate(agent?.updatedAt || null);
    });
  };

  /**
   * 獲取選中金鑰的詳細資訊
   */
  const getSelectedKeyDetails = (
    editData: AIAgent,
    availableKeys?: AvailableKey[]
  ) => {
    return computed(() => {
      if (!editData?.key_id || !availableKeys) {
        return {
          apiKey: undefined,
          apiUrl: undefined,
          providerType: undefined,
        };
      }
      const key = availableKeys.find((k) => k.id === editData.key_id);
      return {
        apiKey: key?.api_key || undefined,
        apiUrl: key?.api_url || undefined,
        providerType: key?.provider as AiBotProviderType | undefined,
      };
    });
  };

  /**
   * 處理欄位更新
   */
  const createFieldUpdateHandler = <T extends Record<string, any>>(
    updateFn: (key: keyof T, value: any) => void
  ) => {
    return <K extends keyof T>(key: K, value: T[K]) => {
      updateFn(key, value);
    };
  };

  /**
   * 處理系統功能選擇
   */
  const createSystemFeatureSelectHandler = (
    systemFeatures: { key: string; name: string }[],
    updateSystemFeature: (val: string) => void,
    updateEditData: (key: string, value: any) => void
  ) => {
    return (featureKey: string | null | undefined) => {
      if (!featureKey) return;
      const feature = systemFeatures.find((f) => f.key === featureKey);
      if (feature) {
        updateSystemFeature(feature.key);
        updateEditData("name", feature.name);
      }
    };
  };

  /**
   * 聚焦到名稱輸入框
   */
  const focusNameInput = () => {
    const inputElement = nameInput.value?.$el?.querySelector("input");
    if (inputElement) {
      inputElement.focus();
    }
  };

  /**
   * 檢查是否為系統 Agent
   */
  const isSystemAgent = (scope?: string) => {
    return scope === "SYSTEM";
  };

  /**
   * 獲取範疇描述
   */
  const getScopeDescription = (scope: string) => {
    const descriptions = {
      SYSTEM: "系統級 - 用於後端/管理性的 AI 功能",
      TENANT_TEMPLATE: "租戶範本 - 可由租戶管理員設定的 Agent 範本",
      WORKSPACE: "工作空間級 - 特定於使用者工作空間內設定和使用的 Agent",
    };
    return descriptions[scope as keyof typeof descriptions] || scope;
  };

  /**
   * 檢查是否可以編輯核心屬性
   */
  const getCanEditCoreProps = (scope?: string, isSystemDefined?: boolean) => {
    // 系統定義的 Agent 通常不能編輯核心屬性
    if (isSystemDefined) return false;
    // 其他邏輯根據需求添加
    return true;
  };

  /**
   * 檢查是否可以刪除
   */
  const getCanDelete = (scope?: string, isSystemDefined?: boolean) => {
    // 系統 Agent 通常不能刪除
    if (scope === "SYSTEM" || isSystemDefined) return false;
    return true;
  };

  /**
   * 驗證 Agent 配置
   */
  const validateAgentConfig = (agent: Partial<AIAgent>) => {
    const errors: string[] = [];

    if (!agent.name?.trim()) {
      errors.push("Agent 名稱不能為空");
    }

    if (!agent.model_id) {
      errors.push("必須選擇一個模型");
    }

    if (!agent.key_id) {
      errors.push("必須選擇一個 API 金鑰");
    }

    if (
      agent.temperature !== undefined &&
      (agent.temperature < 0 || agent.temperature > 2)
    ) {
      errors.push("溫度值必須在 0-2 之間");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  };

  return {
    nameInput,
    getActiveModels,
    getLastUpdatedAt,
    getSelectedKeyDetails,
    createFieldUpdateHandler,
    createSystemFeatureSelectHandler,
    focusNameInput,
    isSystemAgent,
    getScopeDescription,
    getCanEditCoreProps,
    getCanDelete,
    validateAgentConfig,
  };
}
