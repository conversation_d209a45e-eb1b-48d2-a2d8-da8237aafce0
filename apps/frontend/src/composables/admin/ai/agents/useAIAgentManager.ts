import { ref, computed, watch } from "vue";
import { useNotification } from "@/composables/shared/useNotification";
import { AiBotsService } from "@/services/admin/ai/ai-agents.service";
import { AiKeysService } from "@/services/admin/ai/ai-keys.service";
import {
  AiBotScope,
  AiBotProviderType,
  AiBotResponseFormat,
  type AiBot,
  type AiBotWithRelations,
} from "@/types/models/ai.model";
import { DEFAULT_AGENT_SCENES } from "@/types/models/ai-agent.model";
import { httpService } from "@/services/http.service";
import type {
  CreateBotDto,
  UpdateBotDto,
  TestBotDto,
  OptimizePromptDto,
} from "@/types/dto/ai-agents.dto";
import type { Ref } from "vue";
import type { AiKey, AiModel } from "@/types/models/ai.model";

interface AiSystemFeatureDefinition {
  id: string;
  key: string;
  name: string;
  description?: string | null;
  is_system_level: boolean;
}

/**
 * 根據 API 金鑰的 provider 映射到對應的 AiBotProviderType
 */
const mapProviderToAiBotProviderType = (provider: string): AiBotProviderType => {
  switch (provider.toLowerCase()) {
    case "openai":
      return AiBotProviderType.OPENAI;
    case "openai-compatible":
      return AiBotProviderType.OPENAI_COMPATIBLE;
    case "anthropic":
      return AiBotProviderType.CLAUDE;
    case "google-gemini":
      return AiBotProviderType.GEMINI;
    default:
      return AiBotProviderType.OPENAI; // 預設使用 OpenAI
  }
};

export function useAIAgentManager(
  systemFeatureDefinitions: Ref<AiSystemFeatureDefinition[]>
) {
  const notification = useNotification();

  // 核心狀態
  const agentList = ref<AiBotWithRelations[]>([]);
  const enabledModels = ref<AiModel[]>([]);
  const availableKeys = ref<AiKey[]>([]);
  const selectedAgentId = ref<string>("");
  const agentLoading = ref(false);
  const isSaving = ref(false);
  const isDirty = ref(false);
  const isNew = ref(false);

  // 編輯狀態
  const isEditing = ref(false);
  const editData = ref<AiBot>({
    id: "",
    name: "",
    description: "",
    scene: "general_assistant",
    provider_type: AiBotProviderType.OPENAI, // 預設使用 OpenAI
    model_id: "",
    key_id: "",
    temperature: 0.7,
    system_prompt: "",
    response_format: AiBotResponseFormat.TEXT,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    scope: AiBotScope.WORKSPACE,
    is_template: false,
    is_enabled: true,
  });

  // 聊天測試狀態
  const testMessage = ref("");
  const isSending = ref(false);
  const chatMessages = ref<{ role: "user" | "assistant"; content: string }[]>(
    []
  );

  // 監聽 key_id 變化，自動更新 provider_type
  watch(
    () => editData.value.key_id,
    (newKeyId) => {
      if (newKeyId && availableKeys.value.length > 0) {
        const selectedKey = availableKeys.value.find(key => key.id === newKeyId);
        if (selectedKey) {
          const newProviderType = mapProviderToAiBotProviderType(selectedKey.provider);
          if (editData.value.provider_type !== newProviderType) {
            editData.value.provider_type = newProviderType;
            isDirty.value = true;
          }
        }
      }
    },
    { immediate: false }
  );

  // Computed properties
  const systemFeatures = computed(() => {
    if (!systemFeatureDefinitions.value) return [];
    return systemFeatureDefinitions.value.filter(
      (feature) => feature.is_system_level
    );
  });

  const systemScenes = computed(() => systemFeatures.value.map((f) => f.key));

  const nonSystemScenes = computed(() => {
    if (!systemFeatureDefinitions.value) return [];
    return systemFeatureDefinitions.value
      .filter((f) => !f.is_system_level)
      .map((f) => f.key);
  });

  const selectedAgent = computed<AiBot | null>(() => {
    if (!agentList.value?.length || !selectedAgentId.value) return null;
    return agentList.value.find((b) => b.id === selectedAgentId.value) || null;
  });

  const isSystemAgent = computed(
    () =>
      selectedAgent.value?.scope === "SYSTEM" ||
      editData.value?.scope === "SYSTEM"
  );

  const canDeleteAgent = computed(() => {
    return !isSystemAgent.value;
  });

  const availableModels = computed(() => {
    return enabledModels.value.map((m) => ({
      id: m.id,
      name: m.display_name || m.model_name,
      is_enabled: m.is_enabled,
    }));
  });

  // 工具函數
  const getAgentScenesByScope = (scope: AiBotScope): { value: string; label: string; description?: string }[] => {
    const systemScenes = computed(() => {
      if (scope === AiBotScope.SYSTEM && systemFeatures.value?.length) {
        return systemFeatures.value.map((feature) => ({
          value: feature.key,
          label: feature.name,
          description: feature.description,
        }));
      }
      return [];
    });

    const nonSystemScenes = computed(() => {
      if (scope !== AiBotScope.SYSTEM) {
        return DEFAULT_AGENT_SCENES.filter(scene => 
          !systemFeatures.value?.some(feature => feature.key === scene.value)
        );
      }
      return [];
    });

    return [...systemScenes.value, ...nonSystemScenes.value];
  };

  // API 操作
  const loadAgents = async () => {
    try {
      agentLoading.value = true;
      const agents = await AiBotsService.findAll({});
      agentList.value = agents;
    } catch (error) {
      console.error("[useAIAgentManager] 載入 Agent 列表失敗:", error);
      notification.toast.error("載入 Agent 列表失敗");
    } finally {
      agentLoading.value = false;
    }
  };

  const loadEnabledModels = async () => {
    try {
      const response = await httpService.get<AiModel[]>("/admin/ai/models");
      enabledModels.value = response.filter((m) => m.is_enabled);
    } catch (error) {
      console.error("[useAIAgentManager] 載入模型列表失敗:", error);
    }
  };

  const loadKeys = async () => {
    try {
      const keys = await AiKeysService.getAll();
      availableKeys.value = keys;

      if (
        selectedAgent.value &&
        (!editData.value.key_id || editData.value.key_id === "") &&
        keys.length > 0
      ) {
        const enabledKeys = keys.filter((k) => k.is_enabled);
        if (enabledKeys.length > 0) {
          editData.value.key_id = enabledKeys[0].id;
        } else if (keys.length > 0) {
          editData.value.key_id = keys[0].id;
        }
      }
    } catch (error) {
      console.error("[useAIAgentManager] 載入金鑰列表失敗:", error);
    }
  };

  const saveAgent = async (dataToSave: AiBot) => {
    try {
      isSaving.value = true;
      let result: AiBot;

      if (isNew.value) {
        const createDto: CreateBotDto = {
          name: dataToSave.name,
          description: dataToSave.description,
          scene: dataToSave.scene,
          provider_type: dataToSave.provider_type,
          model_id: dataToSave.model_id,
          key_id: dataToSave.key_id,
          temperature: dataToSave.temperature,
          system_prompt: dataToSave.system_prompt,
          response_format: dataToSave.response_format,
          scope: dataToSave.scope,
          is_enabled: dataToSave.is_enabled,
          is_template: dataToSave.is_template,
        };
        result = await AiBotsService.create(createDto);
        agentList.value.push(result);
        notification.toast.success("Agent 建立成功");
      } else {
        // 更新時排除 provider_type 和 scope 欄位，因為後端 UpdateBotDto 不允許這些欄位
        const updateDto: UpdateBotDto = {
          name: dataToSave.name,
          description: dataToSave.description,
          scene: dataToSave.scene,
          model_id: dataToSave.model_id,
          key_id: dataToSave.key_id,
          temperature: dataToSave.temperature,
          system_prompt: dataToSave.system_prompt,
          response_format: dataToSave.response_format,
          is_enabled: dataToSave.is_enabled,
          is_template: dataToSave.is_template,
        };
        result = await AiBotsService.update(dataToSave.id, updateDto);
        const index = agentList.value.findIndex((b) => b.id === dataToSave.id);
        if (index > -1) {
          agentList.value[index] = result;
        }
        notification.toast.success("Agent 更新成功");
      }

      isDirty.value = false;
      return result;
    } catch (error) {
      console.error("[useAIAgentManager] 保存 Agent 失敗:", error);
      notification.toast.error("保存 Agent 失敗");
      throw error;
    } finally {
      isSaving.value = false;
    }
  };

  const createAgent = async (scope: AiBotScope = AiBotScope.WORKSPACE, scene?: string, name?: string) => {
    isNew.value = true;
    isEditing.value = true;
    selectedAgentId.value = "";

    const availableScenes = getAgentScenesByScope(scope);
    const defaultScene = scene || (
      availableScenes.length > 0
        ? availableScenes[0].value
        : "general_assistant"
    );

    const sceneLabel = availableScenes.find(s => s.value === defaultScene)?.label || defaultScene;

    const autoName = `${sceneLabel} (${scope})`;
    const autoDescription = `${sceneLabel}的 AI 助手`;

    // 根據第一個可用的 API 金鑰設定 provider_type
    const firstAvailableKey = availableKeys.value.length > 0 ? availableKeys.value[0] : null;
    const providerType = firstAvailableKey
      ? mapProviderToAiBotProviderType(firstAvailableKey.provider)
      : AiBotProviderType.OPENAI;

    editData.value = {
      id: "",
      name: autoName,
      description: autoDescription,
      scene: defaultScene,
      provider_type: providerType,
      model_id: enabledModels.value.length > 0 ? enabledModels.value[0].id : "",
      key_id: availableKeys.value.length > 0 ? availableKeys.value[0].id : "",
      temperature: 0.7,
      system_prompt: "",
      response_format: AiBotResponseFormat.TEXT,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      scope,
      is_template: false,
      is_enabled: true,
    };

    isDirty.value = false;
  };

  const selectAgent = async (id: string) => {
    const agent = agentList.value.find((b) => b.id === id);
    if (!agent) {
      console.warn("[useAIAgentManager] 找不到指定的 Agent:", id);
      return;
    }

    selectedAgentId.value = id;
    isNew.value = false;
    editData.value = { ...agent };
    isDirty.value = false;
    chatMessages.value = [];
  };

  const deleteAgent = async () => {
    if (!selectedAgent.value) return;

    try {
      await AiBotsService.delete(selectedAgent.value.id);
      agentList.value = agentList.value.filter(
        (b) => b.id !== selectedAgent.value!.id
      );
      selectedAgentId.value = "";
      isEditing.value = false;
      notification.toast.success("Agent 刪除成功");
    } catch (error) {
      console.error("[useAIAgentManager] 刪除 Agent 失敗:", error);
      notification.toast.error("刪除 Agent 失敗");
    }
  };

  const duplicateAgent = async () => {
    if (!selectedAgent.value) return;

    try {
      const originalAgent = selectedAgent.value;
      const duplicateData: CreateBotDto = {
        name: `${originalAgent.name} (副本)`,
        description: originalAgent.description,
        scene: originalAgent.scene,
        provider_type: originalAgent.provider_type,
        model_id: originalAgent.model_id,
        key_id: originalAgent.key_id,
        temperature: originalAgent.temperature,
        system_prompt: originalAgent.system_prompt,
        response_format: originalAgent.response_format,
        scope: originalAgent.scope,
        is_template: originalAgent.is_template,
        is_enabled: false, // 複製的 agent 預設為禁用狀態
      };

      const newAgent = await AiBotsService.create(duplicateData);
      agentList.value.push(newAgent);
      selectedAgentId.value = newAgent.id;
      editData.value = { ...newAgent };
      isNew.value = false;
      isDirty.value = false;
      notification.toast.success("Agent 複製成功");
    } catch (error) {
      console.error("[useAIAgentManager] 複製 Agent 失敗:", error);
      notification.toast.error("複製 Agent 失敗");
    }
  };

  // 聊天測試功能
  const sendMessage = async () => {
    if (!testMessage.value.trim() || !selectedAgent.value || isSending.value)
      return;

    try {
      isSending.value = true;
      const userMessage = testMessage.value.trim();
      chatMessages.value.push({ role: "user", content: userMessage });

      const testDto: TestBotDto = {
        agentId: selectedAgent.value.id,
        message: userMessage,
      };

      const response = await AiBotsService.test(testDto);
      chatMessages.value.push({
        role: "assistant",
        content: response.response,
      });
      testMessage.value = "";
    } catch (error) {
      console.error("[useAIAgentManager] 發送測試訊息失敗:", error);
      notification.toast.error("測試失敗");
    } finally {
      isSending.value = false;
    }
  };

  const clearChat = () => {
    chatMessages.value = [];
  };

  // 提示詞優化功能
  const optimizePrompt = async (scene: string, prompt: string) => {
    try {
      const getProviderString = (
        providerType: AiBotProviderType
      ): "openai" | "claude" | "openai-compatible" => {
        switch (providerType) {
          case AiBotProviderType.OPENAI:
            return "openai";
          case AiBotProviderType.CLAUDE:
            return "claude";
          case AiBotProviderType.OPENAI_COMPATIBLE:
            return "openai-compatible";
          default:
            return "openai";
        }
      };

      const optimizeDto: OptimizePromptDto = {
        id: selectedAgent.value?.id || "",
        provider: selectedAgent.value?.provider_type
          ? getProviderString(selectedAgent.value.provider_type)
          : "openai",
        apiKey: "",
        scene,
        prompt,
      };
      const response = await httpService.post<{ optimized_prompt: string }>(
        "/admin/ai/agents/optimize-prompt",
        optimizeDto
      );
      return response.optimized_prompt;
    } catch (error) {
      console.error("[useAIAgentManager] 優化提示詞失敗:", error);
      throw error;
    }
  };

  // 表單管理
  const updateEditData = (key: string, value: any) => {
    (editData.value as any)[key] = value;
    isDirty.value = true;
  };

  const updateSystemFeature = (featureKey: string) => {
    const feature = systemFeatures.value.find((f) => f.key === featureKey);
    if (feature) {
      editData.value.scene = feature.key;
      editData.value.name = feature.name;
      editData.value.description = feature.description || "";
      isDirty.value = true;
    }
  };

  const saveChanges = async () => {
    // 對於新建的機器人，即使沒有修改也需要保存
    // 對於編輯的機器人，只有在有修改時才保存
    if (isNew.value || isDirty.value) {
      await saveAgent(editData.value);
    }
  };

  // 初始化
  const initialize = async () => {
    await Promise.all([loadAgents(), loadEnabledModels(), loadKeys()]);
  };

  // 監聽變化
  watch(
    () => editData.value,
    () => {
      if (!isNew.value && selectedAgent.value) {
        const hasChanges =
          JSON.stringify(editData.value) !== JSON.stringify(selectedAgent.value);
        isDirty.value = hasChanges;
      }
    },
    { deep: true }
  );

  return {
    // 狀態
    agentList,
    enabledModels,
    availableKeys,
    selectedAgentId,
    selectedAgent,
    agentLoading,
    isSaving,
    isDirty,
    isNew,
    isEditing,
    editData,
    isSystemAgent,
    canDeleteAgent,
    availableModels,

    // 聊天測試
    testMessage,
    isSending,
    chatMessages,

    // 方法
    loadAgents,
    loadEnabledModels,
    loadKeys,
    saveAgent,
    createAgent,
    selectAgent,
    deleteAgent,
    duplicateAgent,
    sendMessage,
    clearChat,
    optimizePrompt,
    updateEditData,
    updateSystemFeature,
    saveChanges,
    initialize,
    getAgentScenesByScope,
  };
}

// 工具函數
export function getModelName(
  models: AiModel[] | undefined,
  modelId: string
): string {
  const model = models?.find((m) => m.id === modelId);
  return model?.display_name || model?.model_name || "未知模型";
}
