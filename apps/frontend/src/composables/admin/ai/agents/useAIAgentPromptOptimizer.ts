import { ref, nextTick } from "vue";
import type { Ref } from "vue";
import { AiBotsService } from "@/services/admin/ai/ai-agents.service";
import type { AiBot } from "@/types/models/ai.model";
import { AiBotProviderType } from "@/types/models/ai.model";
import type { useNotification } from "@/composables/shared/useNotification";

// 從 AIAgentTester.vue 移過來的 API 回應類型定義
interface OptimizePromptResponse {
  success: boolean;
  response: {
    // 假設 response 物件及其 content 欄位在成功時總是存在
    content: string;
    usage?: {
      inputTokens: number;
      outputTokens: number;
    };
  };
}

const OPTIMIZING_TEXTS = [
  "正在分析你的提示詞...",
  "搜尋最佳表達方式...",
  "調整提示詞結構...",
  "優化指令明確性...",
  "提升提示詞效能...",
];

export function useAIAgentPromptOptimizer(
  selectedAgent: Ref<AiBot | null>,
  editData: Ref<AiBot>, // 用於獲取 model_id, key_id, provider_type, scene，以及更新 system_prompt
  notification: ReturnType<typeof useNotification>
) {
  const isOptimizing = ref(false);
  const optimizingText = ref("正在優化你的提示詞...");
  const popoverPrompt = ref(""); // Popover 內部的獨立輸入狀態

  const optimizePopoverPrompt = async () => {
    if (!popoverPrompt.value) {
      notification.toast.warning("請輸入要優化的提示詞");
      return;
    }

    if (!editData.value.model_id) {
      notification.toast.warning("請先選擇 AI 模型");
      return;
    }

    // Robust check for agent ID
    if (
      !selectedAgent.value ||
      !selectedAgent.value.id ||
      typeof selectedAgent.value.id !== "string" ||
      selectedAgent.value.id.trim() === ""
    ) {
      notification.toast.error("有效的 AI Agent ID 未選擇，無法優化");
      return;
    }

    // Key ID check can remain, as apiKey in payload is more of a placeholder if backend uses agent's key
    if (!editData.value.key_id) {
      notification.toast.warning("請先選擇 API 金鑰 (用於確定提供者)"); // Clarified message
      return;
    }

    isOptimizing.value = true;
    const textInterval = setInterval(() => {
      optimizingText.value =
        OPTIMIZING_TEXTS[Math.floor(Math.random() * OPTIMIZING_TEXTS.length)];
    }, 1000);

    try {
      const agentId = selectedAgent.value.id;
      const currentProviderType = editData.value.provider_type;
      const currentScene = editData.value.scene ?? ""; // Ensure scene is not undefined

      // Explicitly type providerPayloadValue to match expected DTO provider type if known,
      // or ensure all paths lead to a compatible type.
      // The error suggests the DTO expects: 'openai' | 'claude' | 'openai-compatible'
      let providerPayloadValue: "openai" | "claude" | "openai-compatible";

      switch (currentProviderType) {
        case AiBotProviderType.OPENAI:
          providerPayloadValue = "openai";
          break;
        case AiBotProviderType.OPENAI_COMPATIBLE:
          providerPayloadValue = "openai-compatible";
          break;
        case AiBotProviderType.CLAUDE:
          providerPayloadValue = "claude";
          break;
        default:
          notification.toast.error(
            `不支援的提供者類型進行優化: ${currentProviderType}`
          );
          return;
      }

      const payload = {
        id: agentId,
        provider: providerPayloadValue,
        apiKey: editData.value.key_id
          ? `key_id_ref:${editData.value.key_id}`
          : undefined,
        prompt: popoverPrompt.value,
        scene: currentScene,
      };

      if (payload.apiKey === undefined) {
        delete payload.apiKey;
      }

      const response = (await AiBotsService.optimizePrompt(
        payload as any
      )) as unknown as OptimizePromptResponse;

      let optimizedPromptContent: string | undefined;
      // 直接從新的固定結構中獲取 content
      if (
        response.success &&
        response.response &&
        typeof response.response.content === "string"
      ) {
        optimizedPromptContent = response.response.content;
      } else if (!response.success) {
        // 如果後端明確回傳 success: false，可以根據需要處理錯誤訊息
        throw new Error(
          response.response?.content || "提示詞優化失敗，但未提供明確原因。"
        );
      }

      if (!optimizedPromptContent) {
        throw new Error("未收到優化後的提示詞");
      }

      editData.value.system_prompt = optimizedPromptContent;

      // 清理狀態
      isOptimizing.value = false;
      clearInterval(textInterval);

      notification.toast.success(
        "提示詞已優化並填入左側系統提示詞欄位，請確認後儲存"
      );
      popoverPrompt.value = ""; // 清空 popover 輸入框
    } catch (error) {
      console.error("優化提示詞失敗 (useAIAgentPromptOptimizer):", error);
      notification.toast.error(
        error instanceof Error ? error.message : "優化提示詞失敗"
      );
    } finally {
      isOptimizing.value = false;
      optimizingText.value = "正在優化你的提示詞..."; // 重置文字
    }
  };

  return {
    isOptimizing,
    optimizingText,
    popoverPrompt,
    optimizePopoverPrompt,
  };
}
