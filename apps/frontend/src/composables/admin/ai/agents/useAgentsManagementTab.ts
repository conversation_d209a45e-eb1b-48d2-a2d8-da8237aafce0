import { ref, computed, unref, type Ref } from "vue";
import type {
  <PERSON>Bot as CoreAiBot,
  AiBotWithRelations,
  AiModel as CoreAiModel,
  AiKey as CoreAiKey,
  AiBotScope,
} from "@/types/models/ai.model";
import { getModelName } from "./useAIAgentManager";

export function useAgentsManagementTab() {
  // 篩選和排序狀態
  const searchTerm = ref("");
  const scopeFilter = ref<string>("all");
  const statusFilter = ref<string>("all");
  const sortField = ref<keyof CoreAiBot | null>(null);
  const sortDirection = ref<"asc" | "desc">("asc");

  /**
   * 篩選 Agent 列表
   */
  const filterAgents = (
    agents: (CoreAiBot | AiBotWithRelations)[],
    search: string,
    scope: string,
    status: string
  ) => {
    return agents.filter((agent) => {
      // 搜尋條件
      const matchesSearch =
        !search ||
        agent.name.toLowerCase().includes(search.toLowerCase()) ||
        (agent.description &&
          agent.description.toLowerCase().includes(search.toLowerCase())) ||
        (agent.scene && agent.scene.toLowerCase().includes(search.toLowerCase()));

      // 範疇篩選
      const matchesScope = scope === "all" || agent.scope === scope;

      // 狀態篩選
      const matchesStatus =
        status === "all" ||
        (status === "enabled" && agent.is_enabled) ||
        (status === "disabled" && !agent.is_enabled);

      return matchesSearch && matchesScope && matchesStatus;
    });
  };

  /**
   * 排序 Agent 列表
   */
  const sortAgents = (
    agents: (CoreAiBot | AiBotWithRelations)[],
    field: keyof CoreAiBot | null,
    direction: "asc" | "desc"
  ) => {
    if (!field) return agents;

    return [...agents].sort((a, b) => {
      let aValue = a[field];
      let bValue = b[field];

      // 處理不同類型的值
      if (typeof aValue === "string" && typeof bValue === "string") {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return direction === "asc" ? -1 : 1;
      if (aValue > bValue) return direction === "asc" ? 1 : -1;
      return 0;
    });
  };

  /**
   * 建立篩選和排序後的 Agent 列表
   */
  const createFilteredAndSortedAgents = (
    agents: Ref<(CoreAiBot | AiBotWithRelations)[] | undefined>,
    aiModels?: Ref<CoreAiModel[] | undefined>,
    aiKeys?: Ref<CoreAiKey[] | undefined>
  ) => {
    return computed(() => {
      const agentsArray = unref(agents) || [];
      if (!agentsArray.length) return [];

      // 先篩選
      const filtered = filterAgents(
        agentsArray,
        searchTerm.value,
        scopeFilter.value,
        statusFilter.value
      );

      // 再排序
      return sortAgents(filtered, sortField.value, sortDirection.value);
    });
  };

  /**
   * 切換排序
   */
  const toggleSort = (field: keyof CoreAiBot) => {
    if (sortField.value === field) {
      sortDirection.value = sortDirection.value === "asc" ? "desc" : "asc";
    } else {
      sortField.value = field;
      sortDirection.value = "asc";
    }
  };

  /**
   * 重置篩選
   */
  const resetFilters = () => {
    searchTerm.value = "";
    scopeFilter.value = "all";
    statusFilter.value = "all";
    sortField.value = null;
    sortDirection.value = "asc";
  };

  /**
   * 獲取 Agent 統計資訊
   */
  const getAgentStats = (agents: Ref<(CoreAiBot | AiBotWithRelations)[] | undefined>) => {
    return computed(() => {
      const agentsArray = unref(agents) || [];
      if (!agentsArray.length) return { total: 0, enabled: 0, disabled: 0, byScope: {} };

      const stats = {
        total: agentsArray.length,
        enabled: agentsArray.filter((agent) => agent.is_enabled).length,
        disabled: agentsArray.filter((agent) => !agent.is_enabled).length,
        byScope: {} as Record<string, number>,
      };

      // 按範疇統計
      agentsArray.forEach((agent) => {
        const scope = agent.scope || "UNKNOWN";
        stats.byScope[scope] = (stats.byScope[scope] || 0) + 1;
      });

      return stats;
    });
  };

  /**
   * 檢查 Agent 是否可以刪除
   */
  const canDeleteAgent = (agent: CoreAiBot | AiBotWithRelations) => {
    // 系統 Agent 不能刪除
    return agent.scope !== "SYSTEM";
  };

  /**
   * 檢查 Agent 是否可以編輯
   */
  const canEditAgent = (agent: CoreAiBot | AiBotWithRelations) => {
    // 所有 Agent 都可以編輯，但可能有不同的限制
    return true;
  };

  /**
   * 獲取範疇顯示名稱
   */
  const getScopeDisplayName = (scope: string) => {
    const scopeNames = {
      SYSTEM: "系統",
      TENANT_TEMPLATE: "租戶範本",
      WORKSPACE: "工作空間",
    };
    return scopeNames[scope as keyof typeof scopeNames] || scope;
  };

  /**
   * 獲取狀態顯示樣式
   */
  const getStatusVariant = (isEnabled: boolean): "default" | "secondary" => {
    return isEnabled ? "default" : "secondary";
  };

  /**
   * 格式化 Agent 顯示資訊
   */
  const formatAgentForDisplay = (
    agent: CoreAiBot | AiBotWithRelations,
    aiModels?: CoreAiModel[],
    aiKeys?: CoreAiKey[]
  ) => {
    // 如果 agent 包含關聯資料，優先使用
    const agentWithRelations = agent as AiBotWithRelations;
    
    // 獲取模型名稱：優先使用內嵌的 model 資料，否則從 aiModels 查找
    const modelName = agentWithRelations.model?.display_name || 
                     agentWithRelations.model?.model_name ||
                     getModelName(aiModels, agent.model_id);
    
    // 獲取金鑰名稱：優先使用內嵌的 key 資料，否則從 aiKeys 查找  
    const keyName = agentWithRelations.key?.name ||
                   getKeyName(aiKeys, agent.key_id);

    return {
      ...agent,
      modelName,
      keyName,
      scopeDisplayName: getScopeDisplayName(agent.scope || ""),
      statusVariant: getStatusVariant(agent.is_enabled || false),
      canDelete: canDeleteAgent(agent),
      canEdit: canEditAgent(agent),
    };
  };

  // 輔助函數：獲取金鑰名稱
  const getKeyName = (keys: CoreAiKey[] | undefined, keyId: string): string => {
    const key = keys?.find((k) => k.id === keyId);
    return key?.name || "未知金鑰";
  };

  return {
    // 狀態
    searchTerm,
    scopeFilter,
    statusFilter,
    sortField,
    sortDirection,

    // 方法
    filterAgents,
    sortAgents,
    createFilteredAndSortedAgents,
    toggleSort,
    resetFilters,
    getAgentStats,
    canDeleteAgent,
    canEditAgent,
    getScopeDisplayName,
    getStatusVariant,
    formatAgentForDisplay,
  };
}
