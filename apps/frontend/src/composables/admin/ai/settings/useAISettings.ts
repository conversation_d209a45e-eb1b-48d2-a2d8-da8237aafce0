import { ref, computed, onMounted } from "vue";
import { useNotification } from "@/composables/shared/useNotification";
import { useAiFeatureConfig } from "@/composables/admin/ai/features/useAiFeatureConfig";
import { useAIAgentManager } from "@/composables/admin/ai/agents/useAIAgentManager";
import { useAIModelManager } from "@/composables/admin/ai/models/useAIModelManager";
import { useAIKeyManager } from "@/composables/admin/ai/keys/useAIKeyManager";
import { useAiGlobalSettings } from "@/composables/admin/ai/settings/useAiGlobalSettings";
import { useAiSettingsTabs } from "@/composables/admin/ai/settings/useAiSettingsTabs";
import {
  AiBotScope,
  type AiBot,
  type AiKey,
  type AiModel,
  type AiSystemFeatureDefinition,
  AiBotResponseFormat,
} from "@/types/models/ai.model";
import { AgentResponseFormat } from "@/types/models/ai-agent.model";
import { useRouter } from "vue-router";

import type {
  AiKey as CoreAiKey,
  AiModel as CoreAiModel,
  AiBot as CoreAiBot,
  AiBotWithRelations,
  AiGlobalSetting,
} from "@/types/models/ai.model";

// Internal type for model data when interacting with the AIModelEditor component (camelCase for form binding)
interface AiModelEditorData {
  id?: string;
  provider?: string;
  modelName?: string;
  displayName?: string;
  isEnabled?: boolean;
  inputPricePer1kTokens?: number | string;
  outputPricePer1kTokens?: number | string;
  currency?: string;
  contextWindowTokens?: number | string;
  notes?: string | null;
  description?: string;
  price_last_updated_at?: string;
}

export function useAISettings() {
  const { toast } = useNotification();
  const router = useRouter();

  // UI 狀態管理
  const isPageLoading = ref(false);
  const showModelSheet = ref(false);
  const showBotSheet = ref(false);
  const isCreatingNewBot = ref(false);
  const isKeySheetOpen = ref(false);
  const isUpdatingSettings = ref(false);

  // 編輯器資料
  const currentModelForEditor = ref<Partial<AiModelEditorData>>({});
  const currentKeyForEditor = ref<Partial<CoreAiKey>>({});

  // 計算屬性
  const isModelSheetOpen = computed(() => showModelSheet.value);
  const isEditingModel = computed(() => !!currentModelForEditor.value?.id);
  const isEditingKeyForEditor = computed(() => !!currentKeyForEditor.value?.id);

  // Tab 管理
  const { activeTab, updateActiveTabFromQuery } = useAiSettingsTabs("global-settings");

  // 功能配置管理
  const {
    systemFeatureDefinitions,
    fetchSystemFeatureDefinitions,
    updateFeatureConfig,
    syncFeatureDefinitions,
  } = useAiFeatureConfig();

  // Agent 管理
  const botManagerComposable = useAIAgentManager(systemFeatureDefinitions);
  const {
    botList: aiBots,
    enabledModels,
    availableKeys,
    initialize: initializeBots,
    loadBots: fetchAiBots,
    createBot: createBotInManager,
    deleteBot: deleteBotInManager,
    selectBot: selectBotInManagerFromComposable,
    editData: currentBotFromManager,
    saveChanges: saveBotChangesFromManager,
    updateEditData,
    updateSystemFeature,
    getBotScenesByScope,
    duplicateBot,
  } = botManagerComposable;

  // 模型管理
  const modelManager = useAIModelManager();
  const { modelList, initialize: initializeModels } = modelManager;

  // 金鑰管理
  const keyManagerComposable = useAIKeyManager((event) => {
    if (event === "close") {
      isKeySheetOpen.value = false;
    }
  });
  const {
    keyList,
    initialize: initializeKeys,
    handleSelect: selectKey,
    handleNew: createNewKey,
    handleSave: saveKey,
    handleDelete: deleteKeyFromManager,
    isLoading: keysLoading,
    editingKey,
    isNew: isNewKey,
  } = keyManagerComposable;

  // 全域設定管理
  const {
    fetch: fetchAiGlobalSettings,
    aiGlobalSettings,
    loading,
    isUpdating,
  } = useAiGlobalSettings();

  // 計算屬性 - 為 Tab 組件準備資料
  const aiModelsForTabs = computed(() =>
    (modelList.value || []).map((model) => ({
      id: model.id,
      provider: model.provider,
      model_name: model.model_name,
      display_name: model.display_name,
      is_enabled: model.is_enabled,
      input_price_per_1k_tokens: model.input_price_per_1k_tokens,
      output_price_per_1k_tokens: model.output_price_per_1k_tokens,
      currency: model.currency,
      context_window_tokens: model.context_window_tokens,
      notes: model.notes,
    }))
  );

  const aiKeysForTabs = computed(() => keyList.value || []);

  // 為 AIBotEditor 映射模型資料格式
  const mappedEnabledModels = computed(() =>
    enabledModels.value.map((model) => ({
      id: model.id,
      name: model.display_name,
      is_enabled: model.is_enabled,
    }))
  );

  // 計算屬性 - 為當前機器人提供場景選項
  const currentBotScenes = computed(() => {
    const scope = currentBotFromManager.value?.scope || AiBotScope.WORKSPACE;
    return getBotScenesByScope(scope);
  });

  // 計算屬性 - 系統功能資訊
  const currentBotSystemFeatureInfo = computed(() => {
    if (
      !currentBotFromManager.value?.scene ||
      currentBotFromManager.value.scope !== AiBotScope.SYSTEM
    ) {
      return null;
    }

    const feature = systemFeatureDefinitions.value?.find(
      (def) => def.key === currentBotFromManager.value.scene
    );

    return feature ? { name: feature.name } : null;
  });

  // 回覆格式選項
  const responseFormats = computed(() => [
    { value: BotResponseFormat.TEXT, label: "純文字" },
    { value: BotResponseFormat.JSON_OBJECT, label: "JSON 物件" },
  ]);

  // 供應商列表
  const KEY_PROVIDERS_LIST = computed(() => [
    {
      value: "openai",
      label: "OpenAI",
      url: "https://platform.openai.com/api-keys",
    },
    {
      value: "anthropic",
      label: "Anthropic Claude",
      url: "https://console.anthropic.com/keys",
    },
    {
      value: "google-gemini",
      label: "Google Gemini",
      url: "https://aistudio.google.com/app/apikey",
    },
    { value: "openai-compatible", label: "OpenAI 相容性", url: "" },
  ]);

  // 回覆格式列表
  const RESPONSE_FORMATS_LIST = computed(() => [
    { value: "text", label: "Text" },
    { value: "json", label: "JSON" },
  ]);

  // 全域設定相關方法已不需要，因為 GlobalSettingsTab 直接使用 useAiGlobalSettings

  // 機器人相關方法
  const getEffectiveBotForFeatureProp = (
    featureDefinition: any
  ): CoreAiBot | AiBotWithRelations | undefined => {
    // 修正：從 config 中讀取 bot_id，而不是直接從 featureDefinition 讀取
    const botId = featureDefinition?.config?.bot_id;
    if (!botId || !aiBots.value?.length) {
      return undefined;
    }
    return aiBots.value.find((bot) => bot.id === botId);
  };

  const getBotById = (
    botId: string
  ): CoreAiBot | AiBotWithRelations | undefined => {
    return aiBots.value?.find((bot) => bot.id === botId);
  };

  const updateAiBotForFeature = async (
    botId: string,
    data: Partial<CoreAiBot>
  ): Promise<CoreAiBot | null> => {
    try {
      selectBotInManagerFromComposable(botId);
      Object.assign(currentBotFromManager.value, data);
      await saveBotChangesFromManager();
      await fetchAiBots();
      const updatedBot = getBotById(botId);
      return updatedBot || null;
    } catch (error) {
      console.error("更新機器人失敗:", error);
      toast({
        title: "錯誤",
        description: "更新機器人失敗",
        variant: "destructive",
      });
      return null;
    }
  };

  // Bot Sheet 處理方法
  const openNewBotSheetHandler = (options: {
    scope: AiBotScope;
    scene?: string;
    name?: string;
  }) => {
    isCreatingNewBot.value = true;
    showBotSheet.value = true;

    // 延遲處理焦點，避免 aria-hidden 警告
    setTimeout(() => {
      createBotInManager(options.scope, options.scene, options.name);
      // 確保 response_format 有預設值
      if (!currentBotFromManager.value.response_format) {
        currentBotFromManager.value.response_format = BotResponseFormat.TEXT;
      }
    }, 50);
  };

  const openEditBotSheetHandler = (bot: CoreAiBot) => {
    showBotSheet.value = true;
    isCreatingNewBot.value = false;
    selectBotInManagerFromComposable(bot.id);
  };

  const saveBotHandler = async () => {
    try {
      await saveBotChangesFromManager();
      showBotSheet.value = false;

      // 重新載入相關資料，特別是系統功能定義
      // 這確保新建的系統級機器人能正確關聯到對應的功能配置
      await Promise.all([fetchSystemFeatureDefinitions(), fetchAiBots()]);

      toast({
        title: "成功",
        description: isCreatingNewBot.value
          ? "機器人建立成功"
          : "機器人更新成功",
      });
    } catch (error) {
      console.error("儲存機器人失敗:", error);
      toast({
        title: "錯誤",
        description: "儲存機器人失敗",
        variant: "destructive",
      });
    }
  };

  const deleteBotHandlerFromPage = async (botId: string) => {
    try {
      selectBotInManagerFromComposable(botId);
      await deleteBotInManager();
      toast({
        title: "成功",
        description: "機器人刪除成功",
      });
    } catch (error) {
      console.error("刪除機器人失敗:", error);
      toast({
        title: "錯誤",
        description: "刪除機器人失敗",
        variant: "destructive",
      });
    }
  };

  // 模型管理方法
  const openNewModelSheet = () => {
    showModelSheet.value = true;
    currentModelForEditor.value = {
      provider: "openai",
      modelName: "",
      displayName: "",
      isEnabled: true,
      inputPricePer1kTokens: 0,
      outputPricePer1kTokens: 0,
      currency: "USD",
      contextWindowTokens: 4096,
      notes: "",
    };
  };

  const openEditModelSheetHandler = (model: CoreAiModel) => {
    showModelSheet.value = true;
    currentModelForEditor.value = {
      id: model.id,
      provider: model.provider,
      modelName: model.model_name,
      displayName: model.display_name,
      isEnabled: model.is_enabled,
      inputPricePer1kTokens: model.input_price_per_1k_tokens?.toString() || "0",
      outputPricePer1kTokens:
        model.output_price_per_1k_tokens?.toString() || "0",
      currency: model.currency,
      contextWindowTokens: model.context_window_tokens?.toString() || "4096",
      notes: model.notes || "",
    };
  };

  const closeModelSheet = () => {
    showModelSheet.value = false;
    currentModelForEditor.value = {};
  };

  const handleSaveModelComposable = async (
    modelDataFromEditor: Partial<AiModelEditorData>
  ) => {
    try {
      const modelToSave: Partial<CoreAiModel> = {
        id: modelDataFromEditor.id,
        provider: modelDataFromEditor.provider || "openai",
        model_name: modelDataFromEditor.modelName || "",
        display_name: modelDataFromEditor.displayName || "",
        is_enabled: modelDataFromEditor.isEnabled ?? true,
        input_price_per_1k_tokens:
          typeof modelDataFromEditor.inputPricePer1kTokens === "string"
            ? parseFloat(modelDataFromEditor.inputPricePer1kTokens)
            : (modelDataFromEditor.inputPricePer1kTokens as number) || 0,
        output_price_per_1k_tokens:
          typeof modelDataFromEditor.outputPricePer1kTokens === "string"
            ? parseFloat(modelDataFromEditor.outputPricePer1kTokens)
            : (modelDataFromEditor.outputPricePer1kTokens as number) || 0,
        currency: modelDataFromEditor.currency || "USD",
        context_window_tokens:
          typeof modelDataFromEditor.contextWindowTokens === "string"
            ? parseInt(modelDataFromEditor.contextWindowTokens, 10)
            : (modelDataFromEditor.contextWindowTokens as number) || 4096,
        notes: modelDataFromEditor.notes || "",
      };

      if (modelToSave.id) {
        await modelManager.updateModel(modelToSave.id, modelToSave);
        toast({
          title: "成功",
          description: "模型更新成功",
        });
      } else {
        await modelManager.createModel(modelToSave);
        toast({
          title: "成功",
          description: "模型建立成功",
        });
      }

      closeModelSheet();
      await initializeModels();
    } catch (error) {
      console.error("儲存模型失敗:", error);
      toast({
        title: "錯誤",
        description: "儲存模型失敗",
        variant: "destructive",
      });
    }
  };

  const deleteModelHandler = async (modelId: string) => {
    try {
      await modelManager.deleteModel(modelId);
      toast({
        title: "成功",
        description: "模型刪除成功",
      });
    } catch (error) {
      console.error("刪除模型失敗:", error);
      toast({
        title: "錯誤",
        description: "刪除模型失敗",
        variant: "destructive",
      });
    }
  };

  // 金鑰管理方法
  const openNewKeySheetHandler = () => {
    isKeySheetOpen.value = true;
    createNewKey();
    currentKeyForEditor.value = editingKey.value
      ? {
          id: editingKey.value.id,
          name: editingKey.value.name,
          provider: editingKey.value.provider,
          api_key: editingKey.value.apiKey,
          api_url: editingKey.value.apiUrl,
          description: editingKey.value.description,
          is_enabled: editingKey.value.isEnabled,
        }
      : {};
  };

  const openEditKeySheetHandler = (key: CoreAiKey) => {
    isKeySheetOpen.value = true;
    selectKey(key.id);
    currentKeyForEditor.value = { ...key };
  };

  const saveKeyHandler = async (keyData: Partial<CoreAiKey>) => {
    try {
      await saveKey({
        id: keyData.id,
        name: keyData.name,
        provider: keyData.provider,
        api_key: keyData.api_key,
        api_url: keyData.api_url,
        description: keyData.description,
        is_enabled: keyData.is_enabled,
      });
      isKeySheetOpen.value = false;
      toast({
        title: "成功",
        description: isNewKey.value ? "金鑰建立成功" : "金鑰更新成功",
      });
      await initializeKeys();
    } catch (error) {
      console.error("儲存金鑰失敗:", error);
      toast({
        title: "錯誤",
        description: "儲存金鑰失敗",
        variant: "destructive",
      });
    }
  };

  const deleteKeyHandler = async (keyId: string) => {
    try {
      await deleteKeyFromManager(keyId);
      toast({
        title: "成功",
        description: "金鑰刪除成功",
      });
    } catch (error) {
      console.error("刪除金鑰失敗:", error);
      toast({
        title: "錯誤",
        description: "刪除金鑰失敗",
        variant: "destructive",
      });
    }
  };

  // 導航方法
  const actualNavigateToBotTester = (botId: string) => {
    console.log("導航到機器人測試頁面:", botId);
    router.push({
      name: "ai-studio",
      params: { id: botId },
    });
  };

  // 初始化方法
  const initialLoad = async () => {
    try {
      isPageLoading.value = true;
      await Promise.all([
        fetchSystemFeatureDefinitions(),
        initializeBots(),
        initializeModels(),
        initializeKeys(),
        fetchAiGlobalSettings(),
      ]);
    } catch (error) {
      console.error("初始化失敗:", error);
      toast({
        title: "錯誤",
        description: "載入資料失敗",
        variant: "destructive",
      });
    } finally {
      isPageLoading.value = false;
    }
  };

  return {
    // UI 狀態
    isPageLoading,
    showModelSheet,
    showBotSheet,
    isCreatingNewBot,
    isKeySheetOpen,
    isUpdatingSettings,
    isModelSheetOpen,
    isEditingModel,
    isEditingKeyForEditor,

    // Tab 管理
    activeTab,
    updateActiveTabFromQuery,

    // 編輯器資料
    currentModelForEditor,
    currentKeyForEditor,
    currentBotFromManager,
    enabledModels,
    availableKeys,

    // 資料列表
    aiBots,
    aiModelsForTabs,
    aiKeysForTabs,
    keyList,
    systemFeatureDefinitions,
    aiGlobalSettings,

    // 載入狀態
    loading,
    isUpdating,
    keysLoading,

    // 管理器
    modelManager,

    // 常數
    KEY_PROVIDERS_LIST,
    RESPONSE_FORMATS_LIST,

    // 方法
    initialLoad,
    updateFeatureConfig,
    fetchSystemFeatureDefinitions,
    syncFeatureDefinitions,
    getEffectiveBotForFeatureProp,
    getBotById,
    updateAiBotForFeature,
    fetchAiBots,
    openNewBotSheetHandler,
    openEditBotSheetHandler,
    saveBotHandler,
    deleteBotHandlerFromPage,
    updateEditData,
    updateSystemFeature,
    getBotScenesByScope,
    openNewModelSheet,
    openEditModelSheetHandler,
    closeModelSheet,
    handleSaveModelComposable,
    deleteModelHandler,
    openNewKeySheetHandler,
    openEditKeySheetHandler,
    saveKeyHandler,
    deleteKeyHandler,
    actualNavigateToBotTester,
    duplicateBot,
    currentBotSystemFeatureInfo,
    currentBotScenes,
    mappedEnabledModels,
    responseFormats,
  };
}
