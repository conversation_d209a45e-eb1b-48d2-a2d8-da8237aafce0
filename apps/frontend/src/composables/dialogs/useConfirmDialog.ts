/**
 * 統一確認對話框 Composable
 * 
 * 提供統一的確認對話框功能，支援不同類型的確認操作
 * 整合載入狀態、錯誤處理和區域主題差異
 * 
 * <AUTHOR> Design System Team
 * @version 1.0.0
 */

import { useDialogState, type DialogOptions, type DialogType } from './useDialogState'

// ============================================================================
// 類型定義
// ============================================================================

export interface ConfirmDialogOptions {
  /** 確認對話框標題 */
  title: string
  /** 確認對話框描述 */
  description?: string
  /** 確認按鈕文字 */
  confirmText?: string
  /** 取消按鈕文字 */
  cancelText?: string
  /** 對話框類型 */
  type?: DialogType
  /** 確認按鈕變體 */
  confirmVariant?: 'default' | 'destructive' | 'outline' | 'secondary'
  /** 是否危險操作 */
  isDangerous?: boolean
  /** 自定義圖示 */
  icon?: any
  /** 確認前的驗證函數 */
  beforeConfirm?: () => Promise<boolean> | boolean
  /** 確認後的回調函數 */
  onConfirm?: () => Promise<void> | void
  /** 取消後的回調函數 */
  onCancel?: () => void
}

export interface DeleteConfirmOptions {
  /** 要刪除的項目名稱 */
  itemName: string
  /** 要刪除的項目類型 */
  itemType?: string
  /** 刪除操作函數 */
  onDelete: () => Promise<void> | void
  /** 額外的警告訊息 */
  warningMessage?: string
}

export interface AsyncConfirmOptions extends ConfirmDialogOptions {
  /** 異步操作函數 */
  asyncAction: () => Promise<any>
  /** 成功訊息 */
  successMessage?: string
  /** 失敗訊息 */
  errorMessage?: string
}

// ============================================================================
// 統一確認對話框 Composable
// ============================================================================

export function useConfirmDialog() {
  const dialog = useDialogState()

  // 基本確認對話框
  const confirm = async (options: ConfirmDialogOptions): Promise<boolean> => {
    const {
      title,
      description,
      confirmText = '確認',
      cancelText = '取消',
      type = 'confirm',
      isDangerous = false,
      beforeConfirm,
      onConfirm,
      onCancel,
    } = options

    // 設定對話框選項
    const dialogOptions: DialogOptions = {
      type: isDangerous ? 'warning' : type,
      title,
      description,
      confirmText,
      cancelText,
      showCancel: true,
      icon: options.icon,
    }

    try {
      const result = await dialog.open(dialogOptions)
      
      if (result) {
        // 執行確認前驗證
        if (beforeConfirm) {
          const isValid = await beforeConfirm()
          if (!isValid) {
            return false
          }
        }

        // 執行確認回調
        if (onConfirm) {
          await dialog.withLoading(async () => {
            await onConfirm()
          })
        }
      } else {
        // 執行取消回調
        onCancel?.()
      }

      return result
    } catch (error: any) {
      console.error('確認對話框操作失敗:', error)
      dialog.setError(error.message || '操作失敗')
      return false
    }
  }

  // 刪除確認對話框
  const confirmDelete = async (options: DeleteConfirmOptions): Promise<boolean> => {
    const {
      itemName,
      itemType = '項目',
      onDelete,
      warningMessage,
    } = options

    const description = warningMessage 
      ? `${warningMessage}\n\n確定要刪除${itemType}「${itemName}」嗎？此操作無法復原。`
      : `確定要刪除${itemType}「${itemName}」嗎？此操作無法復原。`

    return confirm({
      title: `刪除${itemType}`,
      description,
      confirmText: '刪除',
      cancelText: '取消',
      type: 'error',
      isDangerous: true,
      onConfirm: onDelete,
    })
  }

  // 異步操作確認對話框
  const confirmAsync = async (options: AsyncConfirmOptions): Promise<any> => {
    const {
      asyncAction,
      successMessage,
      errorMessage,
      ...confirmOptions
    } = options

    try {
      const confirmed = await confirm({
        ...confirmOptions,
        onConfirm: async () => {
          const result = await asyncAction()
          
          if (successMessage) {
            await dialog.showSuccess('操作成功', successMessage)
          }
          
          return result
        },
      })

      return confirmed
    } catch (error: any) {
      const message = errorMessage || error.message || '操作失敗'
      await dialog.showError('操作失敗', message)
      throw error
    }
  }

  // 批量操作確認
  const confirmBatch = async (
    items: any[],
    action: string,
    batchAction: (items: any[]) => Promise<void>
  ): Promise<boolean> => {
    if (items.length === 0) {
      await dialog.showWarning('沒有選擇項目', '請先選擇要操作的項目')
      return false
    }

    const itemCount = items.length
    const description = `確定要${action} ${itemCount} 個項目嗎？`

    return confirm({
      title: `批量${action}`,
      description,
      confirmText: action,
      cancelText: '取消',
      type: 'warning',
      onConfirm: () => batchAction(items),
    })
  }

  // 表單提交確認
  const confirmFormSubmit = async (
    formData: any,
    submitAction: (data: any) => Promise<void>,
    options?: {
      title?: string
      description?: string
      successMessage?: string
    }
  ): Promise<boolean> => {
    const {
      title = '提交表單',
      description = '確定要提交表單嗎？',
      successMessage = '表單提交成功',
    } = options || {}

    return confirmAsync({
      title,
      description,
      confirmText: '提交',
      cancelText: '取消',
      type: 'info',
      asyncAction: () => submitAction(formData),
      successMessage,
    })
  }

  // 狀態變更確認
  const confirmStatusChange = async (
    itemName: string,
    fromStatus: string,
    toStatus: string,
    changeAction: () => Promise<void>
  ): Promise<boolean> => {
    const description = `確定要將「${itemName}」的狀態從「${fromStatus}」變更為「${toStatus}」嗎？`

    return confirm({
      title: '變更狀態',
      description,
      confirmText: '變更',
      cancelText: '取消',
      type: 'warning',
      onConfirm: changeAction,
    })
  }

  // 離開頁面確認
  const confirmLeave = async (hasUnsavedChanges: boolean = true): Promise<boolean> => {
    if (!hasUnsavedChanges) {
      return true
    }

    return confirm({
      title: '離開頁面',
      description: '您有未儲存的變更，確定要離開此頁面嗎？',
      confirmText: '離開',
      cancelText: '留在此頁',
      type: 'warning',
      isDangerous: true,
    })
  }

  // 重置確認
  const confirmReset = async (resetAction: () => void): Promise<boolean> => {
    return confirm({
      title: '重置表單',
      description: '確定要重置表單嗎？所有未儲存的變更將會遺失。',
      confirmText: '重置',
      cancelText: '取消',
      type: 'warning',
      onConfirm: resetAction,
    })
  }

  return {
    // 基本方法
    confirm,
    confirmDelete,
    confirmAsync,
    
    // 特定場景方法
    confirmBatch,
    confirmFormSubmit,
    confirmStatusChange,
    confirmLeave,
    confirmReset,

    // 對話框狀態和方法
    ...dialog,
  }
}
