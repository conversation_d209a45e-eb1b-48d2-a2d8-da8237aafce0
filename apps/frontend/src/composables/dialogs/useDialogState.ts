/**
 * 統一對話框狀態管理 Composable
 * 
 * 提供統一的對話框狀態管理，支援載入狀態、錯誤處理和區域主題
 * 整合 HorizAI Design System 的設計規範
 * 
 * <AUTHOR> Design System Team
 * @version 1.0.0
 */

import { ref, computed, nextTick, type Ref } from 'vue'

// ============================================================================
// 類型定義
// ============================================================================

export type DialogType = 'info' | 'success' | 'warning' | 'error' | 'confirm' | 'custom'
export type DialogSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full'
export type DialogPosition = 'center' | 'top' | 'bottom'

export interface DialogOptions {
  /** 對話框類型 */
  type?: DialogType
  /** 對話框標題 */
  title: string
  /** 對話框描述 */
  description?: string
  /** 確認按鈕文字 */
  confirmText?: string
  /** 取消按鈕文字 */
  cancelText?: string
  /** 是否顯示取消按鈕 */
  showCancel?: boolean
  /** 對話框大小 */
  size?: DialogSize
  /** 對話框位置 */
  position?: DialogPosition
  /** 是否可點擊外部關閉 */
  closeOnOutsideClick?: boolean
  /** 是否可按 ESC 關閉 */
  closeOnEscape?: boolean
  /** 自定義圖示 */
  icon?: any
  /** 自定義樣式類別 */
  customClass?: string
  /** 是否持久化（不自動關閉） */
  persistent?: boolean
  /** 自動關閉時間（毫秒） */
  autoCloseDelay?: number
}

export interface DialogState {
  /** 是否開啟 */
  isOpen: boolean
  /** 是否載入中 */
  isLoading: boolean
  /** 錯誤訊息 */
  error: string | null
  /** 對話框選項 */
  options: DialogOptions | null
  /** 對話框 ID */
  id: string | null
}

export interface DialogActions {
  /** 開啟對話框 */
  open: (options: DialogOptions) => Promise<boolean>
  /** 關閉對話框 */
  close: (result?: boolean) => void
  /** 確認操作 */
  confirm: () => void
  /** 取消操作 */
  cancel: () => void
  /** 設定載入狀態 */
  setLoading: (loading: boolean) => void
  /** 設定錯誤 */
  setError: (error: string | null) => void
  /** 重置狀態 */
  reset: () => void
}

// ============================================================================
// 統一對話框狀態管理 Composable
// ============================================================================

export function useDialogState(initialOptions?: Partial<DialogOptions>) {
  // 狀態
  const state = ref<DialogState>({
    isOpen: false,
    isLoading: false,
    error: null,
    options: null,
    id: null,
  })

  // Promise 解析器
  let resolvePromise: ((value: boolean) => void) | null = null
  let autoCloseTimer: number | null = null

  // 計算屬性
  const isOpen = computed(() => state.value.isOpen)
  const isLoading = computed(() => state.value.isLoading)
  const error = computed(() => state.value.error)
  const options = computed(() => state.value.options)
  const dialogId = computed(() => state.value.id)

  const hasError = computed(() => Boolean(state.value.error))
  const canConfirm = computed(() => !state.value.isLoading && !hasError.value)
  const canCancel = computed(() => !state.value.isLoading)

  // 預設選項
  const defaultOptions: DialogOptions = {
    type: 'info',
    title: '',
    description: '',
    confirmText: '確認',
    cancelText: '取消',
    showCancel: true,
    size: 'md',
    position: 'center',
    closeOnOutsideClick: true,
    closeOnEscape: true,
    persistent: false,
    ...initialOptions,
  }

  // 方法
  const generateId = () => `dialog-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

  const open = async (dialogOptions: DialogOptions): Promise<boolean> => {
    // 如果已有對話框開啟，先關閉
    if (state.value.isOpen) {
      close(false)
    }

    // 合併選項
    const mergedOptions = { ...defaultOptions, ...dialogOptions }
    
    // 設定狀態
    state.value = {
      isOpen: true,
      isLoading: false,
      error: null,
      options: mergedOptions,
      id: generateId(),
    }

    // 設定自動關閉
    if (mergedOptions.autoCloseDelay && mergedOptions.autoCloseDelay > 0) {
      autoCloseTimer = window.setTimeout(() => {
        close(false)
      }, mergedOptions.autoCloseDelay)
    }

    // 返回 Promise
    return new Promise((resolve) => {
      resolvePromise = resolve
    })
  }

  const close = (result: boolean = false) => {
    if (!state.value.isOpen) return

    // 清除自動關閉計時器
    if (autoCloseTimer) {
      clearTimeout(autoCloseTimer)
      autoCloseTimer = null
    }

    // 關閉對話框
    state.value.isOpen = false
    
    // 解析 Promise
    if (resolvePromise) {
      resolvePromise(result)
      resolvePromise = null
    }

    // 延遲重置狀態，讓動畫完成
    setTimeout(() => {
      if (!state.value.isOpen) {
        reset()
      }
    }, 300)
  }

  const confirm = () => {
    if (!canConfirm.value) return
    close(true)
  }

  const cancel = () => {
    if (!canCancel.value) return
    close(false)
  }

  const setLoading = (loading: boolean) => {
    state.value.isLoading = loading
  }

  const setError = (errorMessage: string | null) => {
    state.value.error = errorMessage
  }

  const reset = () => {
    state.value = {
      isOpen: false,
      isLoading: false,
      error: null,
      options: null,
      id: null,
    }
  }

  // 鍵盤事件處理
  const handleKeydown = (event: KeyboardEvent) => {
    if (!state.value.isOpen) return

    if (event.key === 'Escape' && state.value.options?.closeOnEscape) {
      event.preventDefault()
      cancel()
    }

    if (event.key === 'Enter' && !state.value.isLoading) {
      event.preventDefault()
      confirm()
    }
  }

  // 外部點擊處理
  const handleOutsideClick = () => {
    if (state.value.options?.closeOnOutsideClick && canCancel.value) {
      cancel()
    }
  }

  // 載入狀態包裝器
  const withLoading = async <T>(asyncFn: () => Promise<T>): Promise<T> => {
    try {
      setLoading(true)
      setError(null)
      const result = await asyncFn()
      return result
    } catch (error: any) {
      setError(error.message || '操作失敗')
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 快捷方法
  const showInfo = (title: string, description?: string) => {
    return open({
      type: 'info',
      title,
      description,
      showCancel: false,
      confirmText: '確定',
    })
  }

  const showSuccess = (title: string, description?: string) => {
    return open({
      type: 'success',
      title,
      description,
      showCancel: false,
      confirmText: '確定',
      autoCloseDelay: 3000,
    })
  }

  const showWarning = (title: string, description?: string) => {
    return open({
      type: 'warning',
      title,
      description,
      showCancel: true,
    })
  }

  const showError = (title: string, description?: string) => {
    return open({
      type: 'error',
      title,
      description,
      showCancel: false,
      confirmText: '確定',
    })
  }

  const showConfirm = (title: string, description?: string) => {
    return open({
      type: 'confirm',
      title,
      description,
      showCancel: true,
    })
  }

  return {
    // 狀態
    state: computed(() => state.value),
    isOpen,
    isLoading,
    error,
    options,
    dialogId,
    hasError,
    canConfirm,
    canCancel,

    // 方法
    open,
    close,
    confirm,
    cancel,
    setLoading,
    setError,
    reset,
    withLoading,

    // 事件處理
    handleKeydown,
    handleOutsideClick,

    // 快捷方法
    showInfo,
    showSuccess,
    showWarning,
    showError,
    showConfirm,
  }
}
