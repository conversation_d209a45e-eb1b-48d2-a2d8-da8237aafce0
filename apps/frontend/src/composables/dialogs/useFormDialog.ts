/**
 * 統一表單對話框 Composable
 * 
 * 提供統一的表單對話框功能，整合表單驗證、提交處理和錯誤管理
 * 支援 Sheet 和 Dialog 兩種模式
 * 
 * <AUTHOR> Design System Team
 * @version 1.0.0
 */

import { ref, computed, watch, nextTick, type Ref } from 'vue'
import { useDialogState, type DialogOptions } from './useDialogState'
import { useFormValidation, type FormValidationConfig } from '@/composables/forms/useFormValidation'

// ============================================================================
// 類型定義
// ============================================================================

export type FormDialogMode = 'create' | 'edit' | 'view'
export type FormDialogType = 'dialog' | 'sheet'

export interface FormDialogOptions<T = any> {
  /** 對話框模式 */
  mode: FormDialogMode
  /** 對話框類型 */
  type?: FormDialogType
  /** 對話框標題 */
  title: string
  /** 對話框描述 */
  description?: string
  /** 表單初始值 */
  initialValues: T
  /** 表單驗證配置 */
  validationConfig?: FormValidationConfig<T>
  /** 提交按鈕文字 */
  submitText?: string
  /** 取消按鈕文字 */
  cancelText?: string
  /** 是否顯示取消按鈕 */
  showCancel?: boolean
  /** 對話框大小 */
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  /** 是否可點擊外部關閉 */
  closeOnOutsideClick?: boolean
  /** 提交前驗證函數 */
  beforeSubmit?: (values: T) => Promise<boolean> | boolean
  /** 提交處理函數 */
  onSubmit?: (values: T, mode: FormDialogMode) => Promise<any> | any
  /** 取消處理函數 */
  onCancel?: () => void
  /** 成功回調函數 */
  onSuccess?: (result: any, values: T) => void
  /** 錯誤回調函數 */
  onError?: (error: any) => void
}

export interface FormDialogState<T = any> {
  /** 是否開啟 */
  isOpen: boolean
  /** 當前模式 */
  mode: FormDialogMode
  /** 對話框類型 */
  type: FormDialogType
  /** 是否載入中 */
  isLoading: boolean
  /** 是否提交中 */
  isSubmitting: boolean
  /** 錯誤訊息 */
  error: string | null
  /** 表單值 */
  values: T
  /** 表單錯誤 */
  formErrors: Record<string, string>
  /** 表單是否有效 */
  isValid: boolean
  /** 表單是否已變更 */
  isDirty: boolean
}

// ============================================================================
// 統一表單對話框 Composable
// ============================================================================

export function useFormDialog<T extends Record<string, any> = any>() {
  // 狀態
  const state = ref<FormDialogState<T>>({
    isOpen: false,
    mode: 'create',
    type: 'dialog',
    isLoading: false,
    isSubmitting: false,
    error: null,
    values: {} as T,
    formErrors: {},
    isValid: false,
    isDirty: false,
  })

  // 對話框選項
  const currentOptions = ref<FormDialogOptions<T> | null>(null)
  
  // Promise 解析器
  let resolvePromise: ((value: any) => void) | null = null

  // 表單驗證
  let formValidation: ReturnType<typeof useFormValidation<T>> | null = null

  // 計算屬性
  const isOpen = computed(() => state.value.isOpen)
  const mode = computed(() => state.value.mode)
  const type = computed(() => state.value.type)
  const isLoading = computed(() => state.value.isLoading)
  const isSubmitting = computed(() => state.value.isSubmitting)
  const error = computed(() => state.value.error)
  const values = computed(() => state.value.values)
  const formErrors = computed(() => state.value.formErrors)
  const isValid = computed(() => state.value.isValid)
  const isDirty = computed(() => state.value.isDirty)

  const canSubmit = computed(() => {
    return state.value.isValid && 
           !state.value.isSubmitting && 
           !state.value.isLoading &&
           (state.value.mode !== 'view')
  })

  const canCancel = computed(() => {
    return !state.value.isSubmitting && !state.value.isLoading
  })

  const isReadonly = computed(() => state.value.mode === 'view')

  // 方法
  const open = async <TResult = any>(options: FormDialogOptions<T>): Promise<TResult | null> => {
    // 儲存選項
    currentOptions.value = options

    // 初始化表單驗證
    if (options.validationConfig) {
      formValidation = useFormValidation(
        ref(options.initialValues),
        options.validationConfig
      )

      // 監聽表單狀態變化
      watch([formValidation.values, formValidation.errors, formValidation.isValid, formValidation.isDirty], 
        ([newValues, newErrors, newIsValid, newIsDirty]) => {
          state.value.values = newValues
          state.value.formErrors = newErrors
          state.value.isValid = newIsValid
          state.value.isDirty = newIsDirty
        }, 
        { immediate: true, deep: true }
      )
    } else {
      // 沒有驗證配置時的簡單狀態管理
      state.value.values = { ...options.initialValues }
      state.value.isValid = true
      state.value.isDirty = false
    }

    // 設定狀態
    state.value = {
      ...state.value,
      isOpen: true,
      mode: options.mode,
      type: options.type || 'dialog',
      isLoading: false,
      isSubmitting: false,
      error: null,
    }

    // 返回 Promise
    return new Promise((resolve) => {
      resolvePromise = resolve
    })
  }

  const close = (result: any = null) => {
    if (!state.value.isOpen) return

    // 關閉對話框
    state.value.isOpen = false
    
    // 解析 Promise
    if (resolvePromise) {
      resolvePromise(result)
      resolvePromise = null
    }

    // 延遲重置狀態
    setTimeout(() => {
      if (!state.value.isOpen) {
        reset()
      }
    }, 300)
  }

  const submit = async () => {
    if (!canSubmit.value || !currentOptions.value) return

    const options = currentOptions.value

    try {
      state.value.isSubmitting = true
      state.value.error = null

      // 執行提交前驗證
      if (options.beforeSubmit) {
        const isValid = await options.beforeSubmit(state.value.values)
        if (!isValid) {
          return
        }
      }

      // 執行表單驗證
      if (formValidation) {
        const isFormValid = await formValidation.validateForm()
        if (!isFormValid) {
          state.value.error = '請檢查表單輸入'
          return
        }
      }

      // 執行提交處理
      let result = null
      if (options.onSubmit) {
        result = await options.onSubmit(state.value.values, state.value.mode)
      }

      // 執行成功回調
      if (options.onSuccess) {
        options.onSuccess(result, state.value.values)
      }

      // 關閉對話框並返回結果
      close(result)

    } catch (error: any) {
      console.error('表單提交失敗:', error)
      state.value.error = error.message || '提交失敗'
      
      // 執行錯誤回調
      if (options.onError) {
        options.onError(error)
      }
    } finally {
      state.value.isSubmitting = false
    }
  }

  const cancel = () => {
    if (!canCancel.value) return

    // 執行取消回調
    if (currentOptions.value?.onCancel) {
      currentOptions.value.onCancel()
    }

    close(null)
  }

  const reset = () => {
    state.value = {
      isOpen: false,
      mode: 'create',
      type: 'dialog',
      isLoading: false,
      isSubmitting: false,
      error: null,
      values: {} as T,
      formErrors: {},
      isValid: false,
      isDirty: false,
    }
    currentOptions.value = null
    formValidation = null
  }

  const setFieldValue = (field: keyof T, value: any) => {
    if (formValidation) {
      formValidation.setFieldValue(field as string, value)
    } else {
      state.value.values[field] = value
      state.value.isDirty = true
    }
  }

  const validateField = async (field: keyof T) => {
    if (formValidation) {
      return formValidation.validateField(field as string)
    }
    return true
  }

  const clearFieldError = (field: keyof T) => {
    if (formValidation) {
      formValidation.clearFieldError(field as string)
    } else {
      delete state.value.formErrors[field as string]
    }
  }

  const setError = (error: string | null) => {
    state.value.error = error
  }

  const setLoading = (loading: boolean) => {
    state.value.isLoading = loading
  }

  // 快捷方法
  const openCreate = <TResult = any>(
    title: string,
    initialValues: T,
    onSubmit: (values: T) => Promise<TResult>,
    options?: Partial<FormDialogOptions<T>>
  ) => {
    return open<TResult>({
      mode: 'create',
      title,
      initialValues,
      onSubmit,
      submitText: '建立',
      ...options,
    })
  }

  const openEdit = <TResult = any>(
    title: string,
    initialValues: T,
    onSubmit: (values: T) => Promise<TResult>,
    options?: Partial<FormDialogOptions<T>>
  ) => {
    return open<TResult>({
      mode: 'edit',
      title,
      initialValues,
      onSubmit,
      submitText: '更新',
      ...options,
    })
  }

  const openView = (
    title: string,
    values: T,
    options?: Partial<FormDialogOptions<T>>
  ) => {
    return open({
      mode: 'view',
      title,
      initialValues: values,
      showCancel: false,
      submitText: '關閉',
      ...options,
    })
  }

  return {
    // 狀態
    state: computed(() => state.value),
    isOpen,
    mode,
    type,
    isLoading,
    isSubmitting,
    error,
    values,
    formErrors,
    isValid,
    isDirty,
    canSubmit,
    canCancel,
    isReadonly,

    // 方法
    open,
    close,
    submit,
    cancel,
    reset,
    setFieldValue,
    validateField,
    clearFieldError,
    setError,
    setLoading,

    // 快捷方法
    openCreate,
    openEdit,
    openView,

    // 表單驗證方法（如果有的話）
    formValidation: computed(() => formValidation),
  }
}
