/**
 * 統一表單驗證 Composable
 * 
 * 提供統一的表單驗證邏輯，支援多種驗證規則
 * 整合 Zod 和自定義驗證規則，提供一致的錯誤處理
 * 
 * <AUTHOR> Design System Team
 * @version 1.0.0
 */

import { ref, computed, reactive, type Ref } from 'vue'
import { z, type ZodSchema, type ZodError } from 'zod'

// ============================================================================
// 類型定義
// ============================================================================

/**
 * 表單欄位錯誤
 */
export interface FormFieldError {
  field: string
  message: string
  code?: string
}

/**
 * 驗證規則類型
 */
export type ValidationRule<T = any> = (value: T) => boolean | string | Promise<boolean | string>

/**
 * 表單驗證配置
 */
export interface FormValidationConfig<T extends Record<string, any>> {
  schema?: ZodSchema<T>
  rules?: Partial<Record<keyof T, ValidationRule[]>>
  validateOnChange?: boolean
  validateOnBlur?: boolean
  debounceMs?: number
}

/**
 * 表單狀態
 */
export interface FormState<T extends Record<string, any>> {
  values: T
  errors: Partial<Record<keyof T, string>>
  touched: Partial<Record<keyof T, boolean>>
  isValid: boolean
  isSubmitting: boolean
  isDirty: boolean
}

// ============================================================================
// 內建驗證規則
// ============================================================================

/**
 * 常用驗證規則
 */
export const validationRules = {
  /**
   * 必填驗證
   */
  required: (message = '此欄位為必填'): ValidationRule => {
    return (value: any) => {
      if (value === undefined || value === null || value === '' || 
          (Array.isArray(value) && value.length === 0)) {
        return message
      }
      return true
    }
  },

  /**
   * 最小長度驗證
   */
  minLength: (min: number, message?: string): ValidationRule => {
    return (value: string) => {
      if (!value) return true // 讓 required 處理空值
      if (value.length < min) {
        return message || `至少需要 ${min} 個字元`
      }
      return true
    }
  },

  /**
   * 最大長度驗證
   */
  maxLength: (max: number, message?: string): ValidationRule => {
    return (value: string) => {
      if (!value) return true
      if (value.length > max) {
        return message || `不能超過 ${max} 個字元`
      }
      return true
    }
  },

  /**
   * 電子郵件驗證
   */
  email: (message = '請輸入有效的電子郵件地址'): ValidationRule => {
    return (value: string) => {
      if (!value) return true
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      return emailRegex.test(value) ? true : message
    }
  },

  /**
   * 數字驗證
   */
  numeric: (message = '請輸入有效的數字'): ValidationRule => {
    return (value: any) => {
      if (value === '' || value === null || value === undefined) return true
      const numValue = Number(value)
      return !isNaN(numValue) ? true : message
    }
  },

  /**
   * 最小值驗證
   */
  min: (min: number, message?: string): ValidationRule => {
    return (value: any) => {
      if (value === '' || value === null || value === undefined) return true
      const numValue = Number(value)
      if (isNaN(numValue)) return true
      return numValue >= min ? true : (message || `不能小於 ${min}`)
    }
  },

  /**
   * 最大值驗證
   */
  max: (max: number, message?: string): ValidationRule => {
    return (value: any) => {
      if (value === '' || value === null || value === undefined) return true
      const numValue = Number(value)
      if (isNaN(numValue)) return true
      return numValue <= max ? true : (message || `不能大於 ${max}`)
    }
  },

  /**
   * 正則表達式驗證
   */
  pattern: (regex: RegExp, message = '格式不正確'): ValidationRule => {
    return (value: string) => {
      if (!value) return true
      return regex.test(value) ? true : message
    }
  },

  /**
   * 密碼強度驗證
   */
  password: (message?: string): ValidationRule => {
    return (value: string) => {
      if (!value) return true
      
      const hasUpperCase = /[A-Z]/.test(value)
      const hasLowerCase = /[a-z]/.test(value)
      const hasNumbers = /\d/.test(value)
      const hasMinLength = value.length >= 8
      
      if (!hasMinLength) {
        return '密碼至少需要 8 個字元'
      }
      if (!hasUpperCase) {
        return '密碼需要包含至少一個大寫字母'
      }
      if (!hasLowerCase) {
        return '密碼需要包含至少一個小寫字母'
      }
      if (!hasNumbers) {
        return '密碼需要包含至少一個數字'
      }
      
      return true
    }
  },

  /**
   * 確認密碼驗證
   */
  confirmPassword: (passwordField: string, message = '密碼確認不一致'): ValidationRule => {
    return (value: string, formValues: any) => {
      if (!value) return true
      return value === formValues[passwordField] ? true : message
    }
  },
}

// ============================================================================
// 表單驗證 Composable
// ============================================================================

/**
 * 表單驗證 composable
 */
export function useFormValidation<T extends Record<string, any>>(
  initialValues: T,
  config: FormValidationConfig<T> = {}
) {
  // 狀態
  const values = reactive<T>({ ...initialValues })
  const errors = ref<Partial<Record<keyof T, string>>>({})
  const touched = ref<Partial<Record<keyof T, boolean>>>({})
  const isSubmitting = ref(false)

  // 計算屬性
  const isValid = computed(() => {
    return Object.keys(errors.value).length === 0
  })

  const isDirty = computed(() => {
    return Object.keys(touched.value).some(key => touched.value[key as keyof T])
  })

  const formState = computed<FormState<T>>(() => ({
    values,
    errors: errors.value,
    touched: touched.value,
    isValid: isValid.value,
    isSubmitting: isSubmitting.value,
    isDirty: isDirty.value,
  }))

  /**
   * 驗證單個欄位
   */
  const validateField = async (field: keyof T): Promise<boolean> => {
    const value = values[field]
    let error: string | null = null

    try {
      // 使用 Zod schema 驗證
      if (config.schema) {
        const fieldSchema = config.schema.shape[field as string]
        if (fieldSchema) {
          fieldSchema.parse(value)
        }
      }

      // 使用自定義規則驗證
      if (config.rules && config.rules[field]) {
        const rules = config.rules[field]!
        for (const rule of rules) {
          const result = await rule(value, values)
          if (result !== true) {
            error = result as string
            break
          }
        }
      }
    } catch (err) {
      if (err instanceof z.ZodError) {
        error = err.errors[0]?.message || '驗證失敗'
      } else {
        error = '驗證過程中發生錯誤'
      }
    }

    // 更新錯誤狀態
    if (error) {
      errors.value[field] = error
    } else {
      delete errors.value[field]
    }

    return !error
  }

  /**
   * 驗證整個表單
   */
  const validateForm = async (): Promise<boolean> => {
    const fieldKeys = Object.keys(values) as (keyof T)[]
    const validationPromises = fieldKeys.map(field => validateField(field))
    const results = await Promise.all(validationPromises)
    
    return results.every(result => result)
  }

  /**
   * 設置欄位值
   */
  const setFieldValue = async (field: keyof T, value: any) => {
    values[field] = value
    touched.value[field] = true

    // 如果啟用即時驗證
    if (config.validateOnChange) {
      await validateField(field)
    }
  }

  /**
   * 設置欄位錯誤
   */
  const setFieldError = (field: keyof T, message: string) => {
    errors.value[field] = message
  }

  /**
   * 清除欄位錯誤
   */
  const clearFieldError = (field: keyof T) => {
    delete errors.value[field]
  }

  /**
   * 設置多個錯誤
   */
  const setErrors = (newErrors: Partial<Record<keyof T, string>>) => {
    errors.value = { ...errors.value, ...newErrors }
  }

  /**
   * 清除所有錯誤
   */
  const clearErrors = () => {
    errors.value = {}
  }

  /**
   * 重置表單
   */
  const resetForm = () => {
    Object.assign(values, initialValues)
    errors.value = {}
    touched.value = {}
    isSubmitting.value = false
  }

  /**
   * 標記欄位為已觸碰
   */
  const touchField = (field: keyof T) => {
    touched.value[field] = true
  }

  /**
   * 處理表單提交
   */
  const handleSubmit = async (onSubmit: (values: T) => Promise<void> | void) => {
    isSubmitting.value = true
    
    try {
      const isFormValid = await validateForm()
      
      if (isFormValid) {
        await onSubmit(values)
      }
    } catch (error) {
      console.error('表單提交失敗:', error)
      throw error
    } finally {
      isSubmitting.value = false
    }
  }

  return {
    // 狀態
    values,
    errors: errors as Ref<Partial<Record<keyof T, string>>>,
    touched: touched as Ref<Partial<Record<keyof T, boolean>>>,
    isValid,
    isDirty,
    isSubmitting,
    formState,

    // 方法
    validateField,
    validateForm,
    setFieldValue,
    setFieldError,
    clearFieldError,
    setErrors,
    clearErrors,
    resetForm,
    touchField,
    handleSubmit,
  }
}
