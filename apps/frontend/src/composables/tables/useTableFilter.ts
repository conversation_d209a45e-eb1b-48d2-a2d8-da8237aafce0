/**
 * 表格篩選 Composable
 * 
 * 提供統一的表格篩選功能
 * 支援多種篩選類型、自定義篩選函數、篩選狀態管理
 * 
 * <AUTHOR> Design System Team
 * @version 1.0.0
 */

import { ref, computed, type Ref } from 'vue'

// ============================================================================
// 類型定義
// ============================================================================

export type FilterOperator = 
  | 'equals' 
  | 'not_equals'
  | 'contains' 
  | 'not_contains'
  | 'starts_with'
  | 'ends_with'
  | 'greater_than'
  | 'greater_than_or_equal'
  | 'less_than'
  | 'less_than_or_equal'
  | 'between'
  | 'in'
  | 'not_in'
  | 'is_null'
  | 'is_not_null'
  | 'custom'

export interface FilterConfig {
  /** 篩選欄位 */
  field: string
  /** 篩選操作符 */
  operator: FilterOperator
  /** 篩選值 */
  value: any
  /** 自定義篩選函數 */
  customFn?: (item: any, value: any) => boolean
}

export interface FilterableColumn {
  /** 欄位鍵值 */
  key: string
  /** 是否可篩選 */
  filterable?: boolean
  /** 篩選類型 */
  filterType?: 'text' | 'number' | 'date' | 'select' | 'boolean' | 'custom'
  /** 篩選選項（select 類型使用） */
  filterOptions?: Array<{ label: string; value: any }>
  /** 自定義篩選函數 */
  filterFn?: (item: any, value: any) => boolean
}

export interface TableFilterOptions {
  /** 篩選變更回調 */
  onFilterChange?: (filters: FilterConfig[]) => void
  /** 是否啟用全域搜尋 */
  globalSearch?: boolean
  /** 全域搜尋欄位 */
  globalSearchFields?: string[]
}

// ============================================================================
// 表格篩選 Composable
// ============================================================================

export function useTableFilter<T = any>(
  data: Ref<T[]>,
  columns: Ref<FilterableColumn[]>,
  options: TableFilterOptions = {}
) {
  const {
    onFilterChange,
    globalSearch = false,
    globalSearchFields = [],
  } = options

  // 篩選狀態
  const filters = ref<FilterConfig[]>([])
  const globalSearchQuery = ref('')

  // 計算屬性
  const filteredData = computed(() => {
    let result = data.value

    // 應用欄位篩選
    if (filters.value.length > 0) {
      result = result.filter(item => {
        return filters.value.every(filter => {
          const column = columns.value.find(col => col.key === filter.field)
          
          if (filter.customFn) {
            return filter.customFn(item, filter.value)
          }
          
          if (column?.filterFn) {
            return column.filterFn(item, filter.value)
          }
          
          return applyFilter(item, filter)
        })
      })
    }

    // 應用全域搜尋
    if (globalSearch && globalSearchQuery.value.trim()) {
      const query = globalSearchQuery.value.toLowerCase().trim()
      const searchFields = globalSearchFields.length > 0 
        ? globalSearchFields 
        : columns.value.filter(col => col.filterable).map(col => col.key)

      result = result.filter(item => {
        return searchFields.some(field => {
          const value = getNestedValue(item, field)
          return String(value).toLowerCase().includes(query)
        })
      })
    }

    return result
  })

  const getFilter = (field: string): FilterConfig | undefined => {
    return filters.value.find(filter => filter.field === field)
  }

  const hasFilter = (field: string): boolean => {
    return filters.value.some(filter => filter.field === field)
  }

  const hasAnyFilter = computed(() => {
    return filters.value.length > 0 || (globalSearch && globalSearchQuery.value.trim())
  })

  const activeFilterCount = computed(() => {
    let count = filters.value.length
    if (globalSearch && globalSearchQuery.value.trim()) {
      count += 1
    }
    return count
  })

  // 方法
  const addFilter = (filter: FilterConfig) => {
    const existingIndex = filters.value.findIndex(f => f.field === filter.field)
    
    if (existingIndex >= 0) {
      filters.value[existingIndex] = filter
    } else {
      filters.value.push(filter)
    }
    
    onFilterChange?.(filters.value)
  }

  const removeFilter = (field: string) => {
    const index = filters.value.findIndex(filter => filter.field === field)
    if (index >= 0) {
      filters.value.splice(index, 1)
      onFilterChange?.(filters.value)
    }
  }

  const updateFilter = (field: string, updates: Partial<FilterConfig>) => {
    const filter = filters.value.find(f => f.field === field)
    if (filter) {
      Object.assign(filter, updates)
      onFilterChange?.(filters.value)
    }
  }

  const clearFilters = () => {
    filters.value = []
    globalSearchQuery.value = ''
    onFilterChange?.(filters.value)
  }

  const clearFieldFilter = (field: string) => {
    removeFilter(field)
  }

  const clearGlobalSearch = () => {
    globalSearchQuery.value = ''
  }

  // 快捷篩選方法
  const filterByText = (field: string, value: string, operator: FilterOperator = 'contains') => {
    if (!value.trim()) {
      removeFilter(field)
      return
    }
    
    addFilter({
      field,
      operator,
      value: value.trim(),
    })
  }

  const filterBySelect = (field: string, value: any) => {
    if (value === null || value === undefined || value === '') {
      removeFilter(field)
      return
    }
    
    addFilter({
      field,
      operator: 'equals',
      value,
    })
  }

  const filterByRange = (field: string, min: any, max: any) => {
    if (min === null && max === null) {
      removeFilter(field)
      return
    }
    
    if (min !== null && max !== null) {
      addFilter({
        field,
        operator: 'between',
        value: [min, max],
      })
    } else if (min !== null) {
      addFilter({
        field,
        operator: 'greater_than_or_equal',
        value: min,
      })
    } else if (max !== null) {
      addFilter({
        field,
        operator: 'less_than_or_equal',
        value: max,
      })
    }
  }

  return {
    // 狀態
    filters,
    globalSearchQuery,
    filteredData,
    
    // 計算屬性
    hasAnyFilter,
    activeFilterCount,
    
    // 方法
    getFilter,
    hasFilter,
    addFilter,
    removeFilter,
    updateFilter,
    clearFilters,
    clearFieldFilter,
    clearGlobalSearch,
    
    // 快捷方法
    filterByText,
    filterBySelect,
    filterByRange,
  }
}

// ============================================================================
// 篩選邏輯
// ============================================================================

function applyFilter(item: any, filter: FilterConfig): boolean {
  const fieldValue = getNestedValue(item, filter.field)
  const filterValue = filter.value

  switch (filter.operator) {
    case 'equals':
      return fieldValue === filterValue

    case 'not_equals':
      return fieldValue !== filterValue

    case 'contains':
      return String(fieldValue).toLowerCase().includes(String(filterValue).toLowerCase())

    case 'not_contains':
      return !String(fieldValue).toLowerCase().includes(String(filterValue).toLowerCase())

    case 'starts_with':
      return String(fieldValue).toLowerCase().startsWith(String(filterValue).toLowerCase())

    case 'ends_with':
      return String(fieldValue).toLowerCase().endsWith(String(filterValue).toLowerCase())

    case 'greater_than':
      return Number(fieldValue) > Number(filterValue)

    case 'greater_than_or_equal':
      return Number(fieldValue) >= Number(filterValue)

    case 'less_than':
      return Number(fieldValue) < Number(filterValue)

    case 'less_than_or_equal':
      return Number(fieldValue) <= Number(filterValue)

    case 'between':
      if (Array.isArray(filterValue) && filterValue.length === 2) {
        const [min, max] = filterValue
        return Number(fieldValue) >= Number(min) && Number(fieldValue) <= Number(max)
      }
      return false

    case 'in':
      return Array.isArray(filterValue) && filterValue.includes(fieldValue)

    case 'not_in':
      return Array.isArray(filterValue) && !filterValue.includes(fieldValue)

    case 'is_null':
      return fieldValue == null

    case 'is_not_null':
      return fieldValue != null

    default:
      return true
  }
}

function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}
