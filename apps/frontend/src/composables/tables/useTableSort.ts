/**
 * 表格排序 Composable
 * 
 * 提供統一的表格排序功能
 * 支援多欄位排序、自定義排序函數、排序狀態管理
 * 
 * <AUTHOR> Design System Team
 * @version 1.0.0
 */

import { ref, computed, type Ref } from 'vue'

// ============================================================================
// 類型定義
// ============================================================================

export type SortDirection = 'asc' | 'desc' | null

export interface SortConfig {
  /** 排序欄位 */
  field: string
  /** 排序方向 */
  direction: SortDirection
  /** 排序優先級（多欄位排序時使用） */
  priority?: number
}

export interface SortableColumn {
  /** 欄位鍵值 */
  key: string
  /** 是否可排序 */
  sortable?: boolean
  /** 自定義排序函數 */
  sortFn?: (a: any, b: any) => number
  /** 排序類型 */
  sortType?: 'string' | 'number' | 'date' | 'boolean' | 'custom'
}

export interface TableSortOptions {
  /** 是否支援多欄位排序 */
  multiSort?: boolean
  /** 預設排序配置 */
  defaultSort?: SortConfig[]
  /** 排序變更回調 */
  onSortChange?: (sortConfigs: SortConfig[]) => void
}

// ============================================================================
// 表格排序 Composable
// ============================================================================

export function useTableSort<T = any>(
  data: Ref<T[]>,
  columns: Ref<SortableColumn[]>,
  options: TableSortOptions = {}
) {
  const {
    multiSort = false,
    defaultSort = [],
    onSortChange,
  } = options

  // 排序狀態
  const sortConfigs = ref<SortConfig[]>(defaultSort)

  // 計算屬性
  const sortedData = computed(() => {
    if (sortConfigs.value.length === 0) {
      return data.value
    }

    return [...data.value].sort((a, b) => {
      for (const config of sortConfigs.value) {
        const column = columns.value.find(col => col.key === config.field)
        if (!column || !config.direction) continue

        let result = 0

        if (column.sortFn) {
          // 使用自定義排序函數
          result = column.sortFn(a, b)
        } else {
          // 使用預設排序邏輯
          result = defaultSortFunction(a, b, config.field, column.sortType)
        }

        if (result !== 0) {
          return config.direction === 'desc' ? -result : result
        }
      }
      return 0
    })
  })

  const getSortDirection = (field: string): SortDirection => {
    const config = sortConfigs.value.find(config => config.field === field)
    return config?.direction || null
  }

  const getSortPriority = (field: string): number | null => {
    const index = sortConfigs.value.findIndex(config => config.field === field)
    return index >= 0 ? index + 1 : null
  }

  const isSorted = (field: string): boolean => {
    return sortConfigs.value.some(config => config.field === field)
  }

  // 方法
  const toggleSort = (field: string) => {
    const column = columns.value.find(col => col.key === field)
    if (!column?.sortable) return

    const existingIndex = sortConfigs.value.findIndex(config => config.field === field)
    
    if (existingIndex >= 0) {
      const currentDirection = sortConfigs.value[existingIndex].direction
      
      if (currentDirection === 'asc') {
        // asc -> desc
        sortConfigs.value[existingIndex].direction = 'desc'
      } else if (currentDirection === 'desc') {
        // desc -> null (移除排序)
        sortConfigs.value.splice(existingIndex, 1)
      }
    } else {
      // 新增排序
      const newConfig: SortConfig = {
        field,
        direction: 'asc',
        priority: sortConfigs.value.length + 1,
      }

      if (multiSort) {
        sortConfigs.value.push(newConfig)
      } else {
        sortConfigs.value = [newConfig]
      }
    }

    // 觸發回調
    onSortChange?.(sortConfigs.value)
  }

  const setSortDirection = (field: string, direction: SortDirection) => {
    const existingIndex = sortConfigs.value.findIndex(config => config.field === field)
    
    if (direction === null) {
      // 移除排序
      if (existingIndex >= 0) {
        sortConfigs.value.splice(existingIndex, 1)
      }
    } else {
      if (existingIndex >= 0) {
        // 更新現有排序
        sortConfigs.value[existingIndex].direction = direction
      } else {
        // 新增排序
        const newConfig: SortConfig = {
          field,
          direction,
          priority: sortConfigs.value.length + 1,
        }

        if (multiSort) {
          sortConfigs.value.push(newConfig)
        } else {
          sortConfigs.value = [newConfig]
        }
      }
    }

    onSortChange?.(sortConfigs.value)
  }

  const clearSort = () => {
    sortConfigs.value = []
    onSortChange?.(sortConfigs.value)
  }

  const resetSort = () => {
    sortConfigs.value = [...defaultSort]
    onSortChange?.(sortConfigs.value)
  }

  return {
    // 狀態
    sortConfigs,
    sortedData,
    
    // 計算屬性
    getSortDirection,
    getSortPriority,
    isSorted,
    
    // 方法
    toggleSort,
    setSortDirection,
    clearSort,
    resetSort,
  }
}

// ============================================================================
// 預設排序函數
// ============================================================================

function defaultSortFunction(
  a: any,
  b: any,
  field: string,
  sortType: SortableColumn['sortType'] = 'string'
): number {
  const aValue = getNestedValue(a, field)
  const bValue = getNestedValue(b, field)

  // 處理 null/undefined 值
  if (aValue == null && bValue == null) return 0
  if (aValue == null) return 1
  if (bValue == null) return -1

  switch (sortType) {
    case 'number':
      return Number(aValue) - Number(bValue)
      
    case 'date':
      return new Date(aValue).getTime() - new Date(bValue).getTime()
      
    case 'boolean':
      return Number(aValue) - Number(bValue)
      
    case 'string':
    default:
      return String(aValue).localeCompare(String(bValue), 'zh-TW', {
        numeric: true,
        sensitivity: 'base',
      })
  }
}

function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}
