/**
 * 統一表格 Composable
 * 
 * 整合虛擬滾動、排序、篩選功能的統一表格解決方案
 * 提供高效能的大數據量表格處理能力
 * 
 * <AUTHOR> Design System Team
 * @version 1.0.0
 */

import { ref, computed, watch, type Ref } from 'vue'
import { useVirtualScroll, type VirtualScrollOptions } from './useVirtualScroll'
import { useTableSort, type SortableColumn, type TableSortOptions } from './useTableSort'
import { useTableFilter, type FilterableColumn, type TableFilterOptions } from './useTableFilter'

// ============================================================================
// 類型定義
// ============================================================================

export interface TableColumn extends SortableColumn, FilterableColumn {
  /** 欄位鍵值 */
  key: string
  /** 欄位標題 */
  title: string
  /** 欄位寬度 */
  width?: number | string
  /** 最小寬度 */
  minWidth?: number
  /** 是否固定欄位 */
  fixed?: 'left' | 'right' | false
  /** 是否可見 */
  visible?: boolean
  /** 欄位對齊方式 */
  align?: 'left' | 'center' | 'right'
  /** 自定義渲染函數 */
  render?: (value: any, item: any, index: number) => string
  /** 欄位樣式類別 */
  headerClass?: string
  /** 儲存格樣式類別 */
  cellClass?: string
}

export interface UnifiedTableOptions {
  /** 虛擬滾動配置 */
  virtualScroll?: VirtualScrollOptions
  /** 排序配置 */
  sort?: TableSortOptions
  /** 篩選配置 */
  filter?: TableFilterOptions
  /** 是否啟用虛擬滾動 */
  enableVirtualScroll?: boolean
  /** 是否啟用排序 */
  enableSort?: boolean
  /** 是否啟用篩選 */
  enableFilter?: boolean
  /** 載入狀態 */
  loading?: boolean
  /** 空狀態配置 */
  emptyState?: {
    title?: string
    description?: string
    icon?: string
  }
}

export interface TableState {
  /** 載入狀態 */
  loading: boolean
  /** 是否有資料 */
  hasData: boolean
  /** 總項目數 */
  totalCount: number
  /** 篩選後項目數 */
  filteredCount: number
  /** 可見項目數 */
  visibleCount: number
  /** 是否啟用虛擬滾動 */
  isVirtualScrollEnabled: boolean
}

// ============================================================================
// 統一表格 Composable
// ============================================================================

export function useUnifiedTable<T = any>(
  rawData: Ref<T[]>,
  columns: Ref<TableColumn[]>,
  options: UnifiedTableOptions = {}
) {
  const {
    virtualScroll: virtualScrollOptions,
    sort: sortOptions,
    filter: filterOptions,
    enableVirtualScroll = false,
    enableSort = true,
    enableFilter = true,
    loading = false,
    emptyState = {
      title: '沒有資料',
      description: '目前沒有可顯示的項目',
    },
  } = options

  // 狀態
  const isLoading = ref(loading)
  const selectedRows = ref<Set<string | number>>(new Set())
  const expandedRows = ref<Set<string | number>>(new Set())

  // 可見欄位
  const visibleColumns = computed(() => {
    return columns.value.filter(col => col.visible !== false)
  })

  // 排序功能
  const sortableColumns = computed(() => {
    return visibleColumns.value.filter(col => col.sortable !== false)
  })

  const {
    sortConfigs,
    sortedData,
    getSortDirection,
    getSortPriority,
    isSorted,
    toggleSort,
    setSortDirection,
    clearSort,
    resetSort,
  } = enableSort 
    ? useTableSort(rawData, sortableColumns, sortOptions)
    : {
        sortConfigs: ref([]),
        sortedData: rawData,
        getSortDirection: () => null,
        getSortPriority: () => null,
        isSorted: () => false,
        toggleSort: () => {},
        setSortDirection: () => {},
        clearSort: () => {},
        resetSort: () => {},
      }

  // 篩選功能
  const filterableColumns = computed(() => {
    return visibleColumns.value.filter(col => col.filterable !== false)
  })

  const {
    filters,
    globalSearchQuery,
    filteredData,
    hasAnyFilter,
    activeFilterCount,
    getFilter,
    hasFilter,
    addFilter,
    removeFilter,
    updateFilter,
    clearFilters,
    clearFieldFilter,
    clearGlobalSearch,
    filterByText,
    filterBySelect,
    filterByRange,
  } = enableFilter 
    ? useTableFilter(sortedData, filterableColumns, filterOptions)
    : {
        filters: ref([]),
        globalSearchQuery: ref(''),
        filteredData: sortedData,
        hasAnyFilter: computed(() => false),
        activeFilterCount: computed(() => 0),
        getFilter: () => undefined,
        hasFilter: () => false,
        addFilter: () => {},
        removeFilter: () => {},
        updateFilter: () => {},
        clearFilters: () => {},
        clearFieldFilter: () => {},
        clearGlobalSearch: () => {},
        filterByText: () => {},
        filterBySelect: () => {},
        filterByRange: () => {},
      }

  // 最終處理的資料
  const processedData = computed(() => filteredData.value)

  // 虛擬滾動功能
  const {
    containerRef,
    scrollTop,
    isScrolling,
    virtualScrollState,
    totalCount: virtualTotalCount,
    visibleCount: virtualVisibleCount,
    startIndex,
    endIndex,
    visibleItems,
    totalHeight,
    offsetY,
    scrollToItem,
    scrollToTop,
    scrollToBottom,
    updateItemHeight,
    measureItemHeight,
    getItemPosition,
    isItemVisible,
    handleScroll,
  } = enableVirtualScroll && virtualScrollOptions
    ? useVirtualScroll(processedData, virtualScrollOptions)
    : {
        containerRef: ref(),
        scrollTop: ref(0),
        isScrolling: ref(false),
        virtualScrollState: computed(() => ({})),
        totalCount: computed(() => processedData.value.length),
        visibleCount: computed(() => processedData.value.length),
        startIndex: computed(() => 0),
        endIndex: computed(() => processedData.value.length - 1),
        visibleItems: computed(() => processedData.value.map((data, index) => ({ data, index }))),
        totalHeight: computed(() => 0),
        offsetY: computed(() => 0),
        scrollToItem: () => {},
        scrollToTop: () => {},
        scrollToBottom: () => {},
        updateItemHeight: () => {},
        measureItemHeight: () => {},
        getItemPosition: () => 0,
        isItemVisible: () => true,
        handleScroll: () => {},
      }

  // 表格狀態
  const tableState = computed<TableState>(() => ({
    loading: isLoading.value,
    hasData: processedData.value.length > 0,
    totalCount: rawData.value.length,
    filteredCount: processedData.value.length,
    visibleCount: enableVirtualScroll ? virtualVisibleCount.value : processedData.value.length,
    isVirtualScrollEnabled: enableVirtualScroll,
  }))

  // 行選擇功能
  const isRowSelected = (rowKey: string | number) => {
    return selectedRows.value.has(rowKey)
  }

  const toggleRowSelection = (rowKey: string | number) => {
    if (selectedRows.value.has(rowKey)) {
      selectedRows.value.delete(rowKey)
    } else {
      selectedRows.value.add(rowKey)
    }
  }

  const selectAllRows = () => {
    processedData.value.forEach((item, index) => {
      const rowKey = getRowKey(item, index)
      selectedRows.value.add(rowKey)
    })
  }

  const clearSelection = () => {
    selectedRows.value.clear()
  }

  const isAllSelected = computed(() => {
    return processedData.value.length > 0 && 
           processedData.value.every((item, index) => {
             const rowKey = getRowKey(item, index)
             return selectedRows.value.has(rowKey)
           })
  })

  const isIndeterminate = computed(() => {
    const selectedCount = selectedRows.value.size
    return selectedCount > 0 && selectedCount < processedData.value.length
  })

  // 行展開功能
  const isRowExpanded = (rowKey: string | number) => {
    return expandedRows.value.has(rowKey)
  }

  const toggleRowExpansion = (rowKey: string | number) => {
    if (expandedRows.value.has(rowKey)) {
      expandedRows.value.delete(rowKey)
    } else {
      expandedRows.value.add(rowKey)
    }
  }

  const expandAllRows = () => {
    processedData.value.forEach((item, index) => {
      const rowKey = getRowKey(item, index)
      expandedRows.value.add(rowKey)
    })
  }

  const collapseAllRows = () => {
    expandedRows.value.clear()
  }

  // 工具方法
  const getRowKey = (item: any, index: number): string | number => {
    return item.id || item.key || index
  }

  const getCellValue = (item: any, column: TableColumn) => {
    const value = getNestedValue(item, column.key)
    return column.render ? column.render(value, item, 0) : value
  }

  const getNestedValue = (obj: any, path: string): any => {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  // 重新整理資料
  const refresh = () => {
    // 觸發資料重新載入的邏輯
    // 這裡可以根據實際需求實作
    clearSelection()
    collapseAllRows()
  }

  return {
    // 引用
    containerRef,
    
    // 狀態
    isLoading,
    tableState,
    selectedRows,
    expandedRows,
    
    // 資料
    rawData,
    processedData,
    visibleColumns,
    
    // 虛擬滾動
    scrollTop,
    isScrolling,
    virtualScrollState,
    startIndex,
    endIndex,
    visibleItems,
    totalHeight,
    offsetY,
    
    // 排序
    sortConfigs,
    getSortDirection,
    getSortPriority,
    isSorted,
    
    // 篩選
    filters,
    globalSearchQuery,
    hasAnyFilter,
    activeFilterCount,
    getFilter,
    hasFilter,
    
    // 選擇
    isAllSelected,
    isIndeterminate,
    
    // 方法 - 虛擬滾動
    scrollToItem,
    scrollToTop,
    scrollToBottom,
    updateItemHeight,
    measureItemHeight,
    getItemPosition,
    isItemVisible,
    handleScroll,
    
    // 方法 - 排序
    toggleSort,
    setSortDirection,
    clearSort,
    resetSort,
    
    // 方法 - 篩選
    addFilter,
    removeFilter,
    updateFilter,
    clearFilters,
    clearFieldFilter,
    clearGlobalSearch,
    filterByText,
    filterBySelect,
    filterByRange,
    
    // 方法 - 選擇
    isRowSelected,
    toggleRowSelection,
    selectAllRows,
    clearSelection,
    
    // 方法 - 展開
    isRowExpanded,
    toggleRowExpansion,
    expandAllRows,
    collapseAllRows,
    
    // 工具方法
    getRowKey,
    getCellValue,
    refresh,
  }
}
