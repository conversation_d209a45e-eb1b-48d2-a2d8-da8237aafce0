/**
 * 虛擬滾動 Composable
 * 
 * 提供高效能的虛擬滾動功能，支援大數據量表格渲染
 * 只渲染可見區域的項目，大幅提升效能
 * 
 * <AUTHOR> Design System Team
 * @version 1.0.0
 */

import { ref, computed, onMounted, onUnmounted, nextTick, type Ref } from 'vue'

// ============================================================================
// 類型定義
// ============================================================================

export interface VirtualScrollOptions {
  /** 每個項目的高度（像素） */
  itemHeight: number
  /** 容器高度（像素） */
  containerHeight: number
  /** 緩衝區項目數量（上下各增加的項目數） */
  buffer?: number
  /** 是否啟用動態高度（實驗性功能） */
  dynamicHeight?: boolean
  /** 滾動節流延遲（毫秒） */
  throttleDelay?: number
}

export interface VirtualScrollState {
  /** 滾動位置 */
  scrollTop: number
  /** 開始索引 */
  startIndex: number
  /** 結束索引 */
  endIndex: number
  /** 可見項目數量 */
  visibleCount: number
  /** 總高度 */
  totalHeight: number
  /** 偏移量 */
  offsetY: number
}

export interface VirtualScrollItem<T = any> {
  /** 項目資料 */
  data: T
  /** 項目索引 */
  index: number
  /** 項目高度（動態高度時使用） */
  height?: number
}

// ============================================================================
// 虛擬滾動 Composable
// ============================================================================

export function useVirtualScroll<T = any>(
  items: Ref<T[]>,
  options: VirtualScrollOptions
) {
  // 狀態
  const containerRef = ref<HTMLElement>()
  const scrollTop = ref(0)
  const isScrolling = ref(false)
  
  // 配置
  const {
    itemHeight,
    containerHeight,
    buffer = 5,
    dynamicHeight = false,
    throttleDelay = 16,
  } = options

  // 項目高度緩存（動態高度時使用）
  const itemHeights = ref<Map<number, number>>(new Map())
  
  // 計算屬性
  const totalCount = computed(() => items.value.length)
  
  const visibleCount = computed(() => {
    return Math.ceil(containerHeight / itemHeight) + buffer * 2
  })
  
  const startIndex = computed(() => {
    const index = Math.floor(scrollTop.value / itemHeight) - buffer
    return Math.max(0, index)
  })
  
  const endIndex = computed(() => {
    const index = startIndex.value + visibleCount.value
    return Math.min(totalCount.value - 1, index)
  })
  
  const visibleItems = computed<VirtualScrollItem<T>[]>(() => {
    const result: VirtualScrollItem<T>[] = []
    
    for (let i = startIndex.value; i <= endIndex.value; i++) {
      if (items.value[i]) {
        result.push({
          data: items.value[i],
          index: i,
          height: dynamicHeight ? itemHeights.value.get(i) || itemHeight : itemHeight,
        })
      }
    }
    
    return result
  })
  
  const totalHeight = computed(() => {
    if (dynamicHeight) {
      let height = 0
      for (let i = 0; i < totalCount.value; i++) {
        height += itemHeights.value.get(i) || itemHeight
      }
      return height
    }
    return totalCount.value * itemHeight
  })
  
  const offsetY = computed(() => {
    if (dynamicHeight) {
      let offset = 0
      for (let i = 0; i < startIndex.value; i++) {
        offset += itemHeights.value.get(i) || itemHeight
      }
      return offset
    }
    return startIndex.value * itemHeight
  })
  
  const virtualScrollState = computed<VirtualScrollState>(() => ({
    scrollTop: scrollTop.value,
    startIndex: startIndex.value,
    endIndex: endIndex.value,
    visibleCount: visibleCount.value,
    totalHeight: totalHeight.value,
    offsetY: offsetY.value,
  }))

  // 滾動節流
  let scrollTimer: number | null = null
  
  const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement
    
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }
    
    isScrolling.value = true
    scrollTop.value = target.scrollTop
    
    scrollTimer = window.setTimeout(() => {
      isScrolling.value = false
      scrollTimer = null
    }, throttleDelay)
  }

  // 動態高度支援
  const updateItemHeight = (index: number, height: number) => {
    if (dynamicHeight) {
      itemHeights.value.set(index, height)
    }
  }
  
  const measureItemHeight = async (index: number, element: HTMLElement) => {
    if (dynamicHeight && element) {
      await nextTick()
      const height = element.offsetHeight
      updateItemHeight(index, height)
    }
  }

  // 滾動到指定項目
  const scrollToItem = (index: number, behavior: ScrollBehavior = 'smooth') => {
    if (!containerRef.value) return
    
    let targetScrollTop = 0
    
    if (dynamicHeight) {
      for (let i = 0; i < index; i++) {
        targetScrollTop += itemHeights.value.get(i) || itemHeight
      }
    } else {
      targetScrollTop = index * itemHeight
    }
    
    containerRef.value.scrollTo({
      top: targetScrollTop,
      behavior,
    })
  }
  
  // 滾動到頂部
  const scrollToTop = (behavior: ScrollBehavior = 'smooth') => {
    scrollToItem(0, behavior)
  }
  
  // 滾動到底部
  const scrollToBottom = (behavior: ScrollBehavior = 'smooth') => {
    scrollToItem(totalCount.value - 1, behavior)
  }

  // 獲取項目在容器中的位置
  const getItemPosition = (index: number) => {
    if (dynamicHeight) {
      let position = 0
      for (let i = 0; i < index; i++) {
        position += itemHeights.value.get(i) || itemHeight
      }
      return position
    }
    return index * itemHeight
  }
  
  // 檢查項目是否可見
  const isItemVisible = (index: number) => {
    return index >= startIndex.value && index <= endIndex.value
  }

  // 生命週期
  onMounted(() => {
    if (containerRef.value) {
      containerRef.value.addEventListener('scroll', handleScroll, { passive: true })
    }
  })
  
  onUnmounted(() => {
    if (containerRef.value) {
      containerRef.value.removeEventListener('scroll', handleScroll)
    }
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }
  })

  return {
    // 引用
    containerRef,
    
    // 狀態
    scrollTop,
    isScrolling,
    virtualScrollState,
    
    // 計算屬性
    totalCount,
    visibleCount,
    startIndex,
    endIndex,
    visibleItems,
    totalHeight,
    offsetY,
    
    // 方法
    scrollToItem,
    scrollToTop,
    scrollToBottom,
    updateItemHeight,
    measureItemHeight,
    getItemPosition,
    isItemVisible,
    handleScroll,
  }
}
