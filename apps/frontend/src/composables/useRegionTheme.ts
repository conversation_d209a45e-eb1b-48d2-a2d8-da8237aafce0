/**
 * 區域主題管理 composable
 * 
 * 處理 Admin 和 Workspace 區域的主題差異
 * 提供區域特定的樣式類別和配置
 * 
 * <AUTHOR> Design System Team
 * @version 1.0.0
 */

import { computed, ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useTheme } from './useTheme'
import { regionConfigs, type RegionType } from '@/design-system/themes'
import adminTheme from '@/design-system/admin-theme'
import workspaceTheme from '@/design-system/workspace-theme'

/**
 * 區域主題 composable
 */
export function useRegionTheme() {
  const route = useRoute()
  const { isDarkMode, themeMode } = useTheme()
  
  // 當前區域
  const currentRegion = ref<RegionType>('admin')
  
  /**
   * 根據路由自動檢測當前區域
   */
  const detectRegionFromRoute = (): RegionType => {
    const path = route.path
    
    if (path.startsWith('/admin')) {
      return 'admin'
    } else if (path.startsWith('/workspace')) {
      return 'workspace'
    }
    
    // 預設為 admin
    return 'admin'
  }
  
  /**
   * 更新當前區域
   */
  const updateCurrentRegion = () => {
    currentRegion.value = detectRegionFromRoute()
  }
  
  // 計算屬性
  const regionConfig = computed(() => regionConfigs[currentRegion.value])
  const regionTheme = computed(() => {
    return currentRegion.value === 'admin' ? adminTheme : workspaceTheme
  })
  
  /**
   * 獲取區域頁面容器類別
   */
  const getPageClasses = computed(() => {
    return regionTheme.value.utils.getPageClasses()
  })
  
  /**
   * 獲取區域卡片類別
   */
  const getCardClasses = (variant: 'default' | 'hover' | 'glass' = 'default') => {
    if (currentRegion.value === 'admin') {
      return adminTheme.utils.getCardClasses(variant as 'default' | 'hover')
    } else {
      return workspaceTheme.utils.getCardClasses(variant)
    }
  }
  
  /**
   * 獲取區域按鈕類別
   */
  const getButtonClasses = (
    variant: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive' = 'primary'
  ) => {
    return regionTheme.value.utils.getButtonClasses(variant)
  }
  
  /**
   * 獲取區域狀態徽章類別
   */
  const getStatusClasses = (
    status: 'active' | 'inactive' | 'pending' | 'error'
  ) => {
    return regionTheme.value.utils.getStatusClasses(status)
  }
  
  /**
   * 獲取區域表格類別
   */
  const getTableClasses = () => {
    return regionTheme.value.utils.getTableClasses()
  }
  
  /**
   * 獲取區域導航項目類別（僅 Workspace）
   */
  const getNavItemClasses = (isActive: boolean = false) => {
    if (currentRegion.value === 'workspace') {
      return workspaceTheme.utils.getNavItemClasses(isActive)
    }
    
    // Admin 區域的導航項目類別
    const baseClasses = [
      'flex items-center gap-3 w-full py-2 px-3 rounded-md transition-colors',
      'text-sm font-medium',
    ]
    
    if (isActive) {
      baseClasses.push('text-primary bg-primary/10')
    } else {
      baseClasses.push('text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800')
    }
    
    return baseClasses.join(' ')
  }
  
  /**
   * 獲取區域特定的文字色彩類別
   */
  const getTextClasses = (type: 'primary' | 'secondary' | 'muted' | 'accent' = 'primary') => {
    return regionTheme.value.classes.text[type]
  }
  
  /**
   * 獲取區域特定的背景色彩類別
   */
  const getBackgroundClasses = (type: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' = 'primary') => {
    return regionTheme.value.classes.background[type]
  }
  
  /**
   * 獲取區域特定的邊框類別
   */
  const getBorderClasses = (type: 'default' | 'accent' | 'success' | 'warning' | 'error' = 'default') => {
    return regionTheme.value.classes.border[type]
  }
  
  /**
   * 獲取區域特定的間距類別
   */
  const getSpacingClasses = (type: 'page' | 'section' | 'card' | 'list' | 'form' = 'page') => {
    return regionTheme.value.classes.spacing[type]
  }

  /**
   * 獲取區域特定的表單類別
   */
  const getFormClasses = (type: 'container' | 'label' | 'input' | 'error' | 'help' = 'container') => {
    // 回退到預設樣式
    const defaultFormClasses = {
      container: currentRegion.value === 'admin' ? 'space-y-3' : 'space-y-4',
      label: 'text-sm font-medium text-foreground',
      input: 'w-full',
      error: 'text-sm text-destructive',
      help: 'text-sm text-muted-foreground',
    }

    return defaultFormClasses[type]
  }
  
  /**
   * 檢查是否為特定區域
   */
  const isRegion = (region: RegionType) => currentRegion.value === region
  const isAdmin = computed(() => currentRegion.value === 'admin')
  const isWorkspace = computed(() => currentRegion.value === 'workspace')
  
  /**
   * 獲取區域主題資訊
   */
  const getRegionInfo = () => ({
    region: currentRegion.value,
    config: regionConfig.value,
    theme: regionTheme.value,
    isAdmin: isAdmin.value,
    isWorkspace: isWorkspace.value,
    isDark: isDarkMode.value,
    themeMode: themeMode.value,
  })
  
  /**
   * 手動設置區域（用於測試或特殊情況）
   */
  const setRegion = (region: RegionType) => {
    currentRegion.value = region
  }
  
  /**
   * 獲取區域特定的 CSS 變數
   */
  const getRegionVariables = () => {
    const config = regionConfig.value
    const variables: Record<string, string> = {}
    
    // 根據區域配置設置特定變數
    if (config.backgroundColorSystem === 'gray') {
      variables['--region-bg'] = isDarkMode.value ? 'rgb(17 24 39)' : 'rgb(249 250 251)'
    } else if (config.backgroundColorSystem === 'zinc') {
      variables['--region-bg'] = isDarkMode.value ? 'rgb(24 24 27)' : 'rgb(250 250 250 / 0.3)'
    }
    
    variables['--region-primary'] = config.primaryColor
    variables['--region-accent'] = config.accentColor
    variables['--region-spacing'] = config.spacingMode === 'compact' ? '1rem' : '1.5rem'
    
    return variables
  }
  
  // 初始化
  onMounted(() => {
    updateCurrentRegion()
    
    // 監聽路由變化
    const unwatch = route && typeof route === 'object' ? 
      (() => {
        // 簡單的路由監聽實作
        let currentPath = route.path
        const checkRouteChange = () => {
          if (route.path !== currentPath) {
            currentPath = route.path
            updateCurrentRegion()
          }
        }
        
        const interval = setInterval(checkRouteChange, 100)
        return () => clearInterval(interval)
      })() : 
      () => {}
    
    return unwatch
  })
  
  return {
    // 狀態
    currentRegion,
    regionConfig,
    regionTheme,
    isAdmin,
    isWorkspace,

    // 類別獲取方法
    getPageClasses,
    getCardClasses,
    getButtonClasses,
    getStatusClasses,
    getTableClasses,
    getNavItemClasses,
    getTextClasses,
    getBackgroundClasses,
    getBorderClasses,
    getSpacingClasses,
    getFormClasses,

    // 工具方法
    isRegion,
    getRegionInfo,
    getRegionVariables,
    setRegion,
    updateCurrentRegion,
  }
}
