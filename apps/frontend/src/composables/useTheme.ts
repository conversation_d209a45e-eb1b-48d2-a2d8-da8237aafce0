import { ref, onMounted, watch, computed } from 'vue'
import { themes, applyTheme, type ThemeMode } from '@/design-system/themes'

/**
 * HorizAI 主題管理 composable
 * 整合 shadcn/ui 和 HorizAI Design System
 * 支援 light/dark 模式切換和區域主題配置
 *
 * <AUTHOR> Design System Team
 * @version 2.0.0
 */
export function useTheme() {
  // 主題狀態
  const isDarkMode = ref(false)
  const currentTheme = ref<ThemeMode>('light')

  // 計算屬性
  const themeMode = computed<ThemeMode>(() => isDarkMode.value ? 'dark' : 'light')
  const theme = computed(() => themes[themeMode.value])

  /**
   * 設置初始主題
   * 優先級：localStorage > 系統偏好 > 預設 light
   */
  const initTheme = () => {
    try {
      // 檢查本地存儲
      const savedTheme = localStorage.getItem('horizai-theme')
      if (savedTheme === 'dark' || savedTheme === 'light') {
        setThemeMode(savedTheme as ThemeMode)
      } else {
        // 如果未設置，根據系統偏好
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
        setThemeMode(prefersDark ? 'dark' : 'light')
      }
    } catch (error) {
      console.warn('[useTheme] 初始化主題失敗，使用預設主題:', error)
      setThemeMode('light')
    }
  }

  /**
   * 切換主題模式
   */
  const toggleTheme = () => {
    const newMode = isDarkMode.value ? 'light' : 'dark'
    setThemeMode(newMode)
  }

  /**
   * 設置主題模式
   * @param mode - 主題模式 ('light' | 'dark')
   */
  const setThemeMode = (mode: ThemeMode) => {
    try {
      isDarkMode.value = mode === 'dark'
      currentTheme.value = mode

      // 保存到本地存儲
      localStorage.setItem('horizai-theme', mode)

      // 應用主題到 DOM
      applyTheme(themes[mode], mode === 'dark')

      // 觸發自定義事件，通知其他組件主題變更
      window.dispatchEvent(new CustomEvent('horizai-theme-change', {
        detail: { mode, theme: themes[mode] }
      }))

    } catch (error) {
      console.error('[useTheme] 設置主題失敗:', error)
    }
  }

  /**
   * 設置暗色模式（向後相容）
   * @param value - 是否為暗色模式
   */
  const setDarkMode = (value: boolean) => {
    setThemeMode(value ? 'dark' : 'light')
  }

  /**
   * 監聽系統主題變化
   */
  const listenForSystemThemeChanges = () => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

    const handleChange = (e: MediaQueryListEvent) => {
      // 只在未手動設置主題時應用系統主題
      const savedTheme = localStorage.getItem('horizai-theme')
      if (!savedTheme) {
        setThemeMode(e.matches ? 'dark' : 'light')
      }
    }

    mediaQuery.addEventListener('change', handleChange)

    // 返回清理函數
    return () => {
      mediaQuery.removeEventListener('change', handleChange)
    }
  }

  /**
   * 獲取當前主題的 CSS 變數
   */
  const getThemeVariables = () => {
    const currentThemeData = themes[themeMode.value]
    const variables: Record<string, string> = {}

    Object.entries(currentThemeData.colors).forEach(([key, value]) => {
      const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase()
      variables[`--${cssKey}`] = value
    })

    variables['--radius'] = currentThemeData.borderRadius

    return variables
  }

  /**
   * 檢查是否為特定主題模式
   */
  const isTheme = (mode: ThemeMode) => currentTheme.value === mode

  /**
   * 獲取主題狀態資訊
   */
  const getThemeInfo = () => ({
    mode: currentTheme.value,
    isDark: isDarkMode.value,
    isLight: !isDarkMode.value,
    theme: theme.value,
    variables: getThemeVariables(),
  })

  // 初始化
  onMounted(() => {
    initTheme()
    const cleanup = listenForSystemThemeChanges()

    // 組件卸載時清理
    return cleanup
  })

  // 監聽主題變化，確保 DOM 同步
  watch(themeMode, (newMode) => {
    applyTheme(themes[newMode], newMode === 'dark')
  }, { immediate: false })

  return {
    // 狀態
    isDarkMode,
    currentTheme,
    theme,
    themeMode,

    // 方法
    toggleTheme,
    setThemeMode,
    setDarkMode, // 向後相容
    getThemeVariables,
    getThemeInfo,
    isTheme,

    // 工具方法
    initTheme,
  }
}