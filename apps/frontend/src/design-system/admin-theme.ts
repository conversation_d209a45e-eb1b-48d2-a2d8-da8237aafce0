/**
 * Admin 區域主題配置
 * 
 * 專業、高效的視覺風格，適用於系統管理和數據展示
 * 使用藍色系作為主色調，gray 色系作為背景
 * 
 * <AUTHOR> Design System Team
 * @version 1.0.0
 */

import { type RegionThemeConfig } from './themes';

// ============================================================================
// Admin 區域主題配置
// ============================================================================

/**
 * Admin 區域的主題配置
 * 專業、數據導向的視覺風格
 */
export const adminThemeConfig: RegionThemeConfig = {
  primaryColor: 'hsl(var(--primary))',      // 藍色系 - 專業可信賴
  accentColor: 'hsl(var(--info))',          // 資訊藍色 - 強調重要資訊
  backgroundColorSystem: 'gray',            // 使用 gray 色系 - 中性專業
  spacingMode: 'compact',                   // 緊湊型間距 - 提高資訊密度
  visualStyle: 'professional',              // 專業風格 - 注重效率
};

// ============================================================================
// Admin 專用 CSS 類別定義
// ============================================================================

/**
 * Admin 區域專用的 CSS 類別組合
 */
export const adminClasses = {
  // 佈局類別
  layout: {
    container: 'bg-gray-50 dark:bg-gray-900',
    sidebar: 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700',
    header: 'bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700',
    content: 'bg-white dark:bg-gray-900',
  },
  
  // 文字色彩
  text: {
    primary: 'text-gray-900 dark:text-gray-100',
    secondary: 'text-gray-600 dark:text-gray-400',
    muted: 'text-gray-500 dark:text-gray-500',
    accent: 'text-primary',
    success: 'text-green-600 dark:text-green-400',
    warning: 'text-amber-600 dark:text-amber-400',
    error: 'text-red-600 dark:text-red-400',
  },
  
  // 背景色彩
  background: {
    primary: 'bg-primary hover:bg-primary/90',
    secondary: 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700',
    success: 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400',
    warning: 'bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400',
    error: 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400',
    info: 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400',
  },
  
  // 邊框樣式
  border: {
    default: 'border-gray-200 dark:border-gray-700',
    accent: 'border-primary',
    success: 'border-green-200 dark:border-green-800',
    warning: 'border-amber-200 dark:border-amber-800',
    error: 'border-red-200 dark:border-red-800',
  },
  
  // 間距模式 - 緊湊型
  spacing: {
    page: 'p-4 space-y-4',
    section: 'p-4 space-y-3',
    card: 'p-4',
    list: 'space-y-2',
    form: 'space-y-3',
  },
  
  // 按鈕樣式
  button: {
    primary: 'bg-primary hover:bg-primary/90 text-primary-foreground',
    secondary: 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100',
    outline: 'border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800',
    ghost: 'hover:bg-gray-100 dark:hover:bg-gray-800',
    destructive: 'bg-red-600 hover:bg-red-700 text-white',
  },
  
  // 表格樣式
  table: {
    container: 'border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden',
    header: 'bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700',
    row: 'border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50',
    cell: 'px-4 py-3 text-sm',
  },
  
  // 卡片樣式
  card: {
    default: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm',
    hover: 'hover:shadow-md transition-shadow duration-200',
    header: 'border-b border-gray-200 dark:border-gray-700 p-4',
    content: 'p-4',
  },
  
  // 表單樣式
  form: {
    label: 'text-sm font-medium text-gray-700 dark:text-gray-300',
    input: 'border-gray-200 dark:border-gray-700 focus:border-primary focus:ring-primary',
    error: 'text-red-600 dark:text-red-400 text-xs',
    help: 'text-gray-500 dark:text-gray-400 text-xs',
  },
  
  // 狀態指示器
  status: {
    active: 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 border-green-200 dark:border-green-800',
    inactive: 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border-gray-200 dark:border-gray-700',
    pending: 'bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400 border-amber-200 dark:border-amber-800',
    error: 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 border-red-200 dark:border-red-800',
  },
} as const;

// ============================================================================
// Admin 專用工具函數
// ============================================================================

/**
 * 獲取 Admin 區域的頁面容器類別
 */
export function getAdminPageClasses(): string {
  return [
    adminClasses.layout.container,
    adminClasses.spacing.page,
    'min-h-screen',
  ].join(' ');
}

/**
 * 獲取 Admin 區域的卡片類別
 */
export function getAdminCardClasses(variant: 'default' | 'hover' = 'default'): string {
  const baseClasses = [adminClasses.card.default];
  
  if (variant === 'hover') {
    baseClasses.push(adminClasses.card.hover);
  }
  
  return baseClasses.join(' ');
}

/**
 * 獲取 Admin 區域的按鈕類別
 */
export function getAdminButtonClasses(
  variant: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive' = 'primary'
): string {
  const baseClasses = [
    'inline-flex items-center justify-center rounded-md text-sm font-medium',
    'transition-colors duration-200 focus-visible:outline-none focus-visible:ring-2',
    'focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',
    'h-10 px-4 py-2',
  ];
  
  baseClasses.push(adminClasses.button[variant]);
  
  return baseClasses.join(' ');
}

/**
 * 獲取 Admin 區域的狀態徽章類別
 */
export function getAdminStatusClasses(
  status: 'active' | 'inactive' | 'pending' | 'error'
): string {
  const baseClasses = [
    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border',
  ];
  
  baseClasses.push(adminClasses.status[status]);
  
  return baseClasses.join(' ');
}

/**
 * 獲取 Admin 區域的表格類別
 */
export function getAdminTableClasses(): {
  container: string;
  header: string;
  row: string;
  cell: string;
} {
  return {
    container: adminClasses.table.container,
    header: adminClasses.table.header,
    row: adminClasses.table.row,
    cell: adminClasses.table.cell,
  };
}

// ============================================================================
// 導出 Admin 主題
// ============================================================================

export default {
  config: adminThemeConfig,
  classes: adminClasses,
  utils: {
    getPageClasses: getAdminPageClasses,
    getCardClasses: getAdminCardClasses,
    getButtonClasses: getAdminButtonClasses,
    getStatusClasses: getAdminStatusClasses,
    getTableClasses: getAdminTableClasses,
  },
} as const;
