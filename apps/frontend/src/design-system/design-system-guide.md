# HorizAI Design System 使用指南

## 📋 目錄

1. [設計系統概述](#設計系統概述)
2. [色彩系統](#色彩系統)
3. [字體系統](#字體系統)
4. [間距系統](#間距系統)
5. [組件使用規範](#組件使用規範)
6. [區域主題差異](#區域主題差異)
7. [最佳實踐](#最佳實踐)

## 🎨 設計系統概述

HorizAI Design System 基於 shadcn/ui 組件庫，提供統一的視覺語言和使用者體驗。系統支援 Admin 和 Workspace 兩個區域的差異化設計，同時保持整體一致性。

### 核心原則

- **一致性**：所有組件遵循統一的設計規範
- **可訪問性**：符合 WCAG 2.1 AA 標準
- **響應式**：適配所有設備尺寸
- **主題支援**：完整的 light/dark 模式支援
- **區域差異**：Admin 專業風格，Workspace 協作風格

## 🎨 色彩系統

### 語意化色彩

```typescript
// 主要色彩
primary: 'hsl(221.2 83.2% 53.3%)'    // 藍色 - 主要操作
success: 'hsl(142.1 76.2% 36.3%)'    // 綠色 - 成功狀態
warning: 'hsl(32.2 95% 44%)'         // 橙色 - 警告狀態
error: 'hsl(0 84.2% 60.2%)'          // 紅色 - 錯誤狀態
info: 'hsl(221.2 83.2% 53.3%)'       // 藍色 - 資訊提示
```

### 使用規範

```vue
<!-- ✅ 正確：使用語意化色彩 -->
<Button variant="primary">主要操作</Button>
<Badge variant="success">成功</Badge>
<Alert variant="warning">警告訊息</Alert>

<!-- ❌ 錯誤：直接使用硬編碼色彩 -->
<div class="bg-blue-500">不要這樣做</div>
```

### 區域色彩差異

**Admin 區域：**
- 主色調：藍色系 (`text-primary`)
- 背景：gray 色系 (`bg-gray-50 dark:bg-gray-900`)
- 強調：專業、數據導向

**Workspace 區域：**
- 主色調：綠色系 (`text-emerald-600 dark:text-emerald-400`)
- 背景：zinc 色系 (`bg-zinc-50/30 dark:bg-zinc-900`)
- 強調：協作、體驗導向

## 📝 字體系統

### 字體層級

```css
/* 標題層級 */
text-4xl: 2.25rem (36px) - 主要頁面標題
text-3xl: 1.875rem (30px) - 區塊標題
text-2xl: 1.5rem (24px) - 子標題
text-xl: 1.25rem (20px) - 卡片標題
text-lg: 1.125rem (18px) - 重要文字

/* 內容層級 */
text-base: 1rem (16px) - 正文內容
text-sm: 0.875rem (14px) - 輔助文字
text-xs: 0.75rem (12px) - 標籤、說明
```

### 使用規範

```vue
<!-- 頁面標題 -->
<h1 class="text-2xl font-bold tracking-tight">頁面標題</h1>

<!-- 區塊標題 -->
<h2 class="text-lg font-semibold">區塊標題</h2>

<!-- 正文內容 -->
<p class="text-sm text-muted-foreground">說明文字</p>

<!-- 標籤文字 -->
<span class="text-xs text-muted-foreground">標籤</span>
```

## 📏 間距系統

### 間距規範

```css
/* 基礎間距 (基於 4px) */
space-1: 0.25rem (4px)
space-2: 0.5rem (8px)
space-3: 0.75rem (12px)
space-4: 1rem (16px)
space-6: 1.5rem (24px)
space-8: 2rem (32px)
space-12: 3rem (48px)
space-16: 4rem (64px)
```

### 區域間距差異

**Admin 區域 - 緊湊型：**
```vue
<div class="p-4 space-y-4">
  <!-- 內容 -->
</div>
```

**Workspace 區域 - 舒適型：**
```vue
<div class="p-6 space-y-6">
  <!-- 內容 -->
</div>
```

## 🧩 組件使用規範

### Button 組件

```vue
<!-- 主要操作按鈕 -->
<Button variant="default" size="default">
  主要操作
</Button>

<!-- 次要操作按鈕 -->
<Button variant="secondary" size="default">
  次要操作
</Button>

<!-- 危險操作按鈕 -->
<Button variant="destructive" size="default">
  刪除
</Button>

<!-- 幽靈按鈕 -->
<Button variant="ghost" size="sm">
  <Icon class="w-4 h-4" />
</Button>
```

### Card 組件

```vue
<!-- 標準卡片 -->
<Card>
  <CardHeader>
    <CardTitle>卡片標題</CardTitle>
    <CardDescription>卡片描述</CardDescription>
  </CardHeader>
  <CardContent>
    <!-- 卡片內容 -->
  </CardContent>
  <CardFooter>
    <!-- 卡片底部 -->
  </CardFooter>
</Card>

<!-- 懸停效果卡片 -->
<Card class="hover:shadow-lg transition-shadow">
  <!-- 內容 -->
</Card>
```

### Table 組件

```vue
<Table>
  <TableHeader>
    <TableRow>
      <TableHead>欄位1</TableHead>
      <TableHead>欄位2</TableHead>
      <TableHead class="text-right">操作</TableHead>
    </TableRow>
  </TableHeader>
  <TableBody>
    <TableRow>
      <TableCell>資料1</TableCell>
      <TableCell>資料2</TableCell>
      <TableCell class="text-right">
        <Button variant="ghost" size="sm">編輯</Button>
      </TableCell>
    </TableRow>
  </TableBody>
</Table>
```

### Form 組件

```vue
<form class="space-y-4">
  <div class="space-y-2">
    <Label for="email">電子郵件</Label>
    <Input
      id="email"
      type="email"
      placeholder="請輸入電子郵件"
      v-model="email"
    />
    <p class="text-xs text-muted-foreground">
      我們不會分享您的電子郵件
    </p>
  </div>
  
  <div class="space-y-2">
    <Label for="password">密碼</Label>
    <Input
      id="password"
      type="password"
      v-model="password"
    />
  </div>
  
  <Button type="submit" class="w-full">
    登入
  </Button>
</form>
```

### Dialog 組件

```vue
<Dialog v-model:open="isOpen">
  <DialogContent>
    <DialogHeader>
      <DialogTitle>對話框標題</DialogTitle>
      <DialogDescription>
        對話框描述文字
      </DialogDescription>
    </DialogHeader>
    
    <!-- 對話框內容 -->
    <div class="py-4">
      <!-- 表單或其他內容 -->
    </div>
    
    <DialogFooter>
      <Button variant="outline" @click="isOpen = false">
        取消
      </Button>
      <Button @click="handleConfirm">
        確認
      </Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

## 🏢 區域主題差異

### Admin 區域使用方式

```vue
<script setup lang="ts">
import adminTheme from '@/design-system/admin-theme';

// 使用 Admin 主題工具函數
const pageClasses = adminTheme.utils.getPageClasses();
const cardClasses = adminTheme.utils.getCardClasses('hover');
const buttonClasses = adminTheme.utils.getButtonClasses('primary');
</script>

<template>
  <div :class="pageClasses">
    <Card :class="cardClasses">
      <CardContent>
        <Button :class="buttonClasses">
          Admin 操作
        </Button>
      </CardContent>
    </Card>
  </div>
</template>
```

### Workspace 區域使用方式

```vue
<script setup lang="ts">
import workspaceTheme from '@/design-system/workspace-theme';

// 使用 Workspace 主題工具函數
const pageClasses = workspaceTheme.utils.getPageClasses();
const cardClasses = workspaceTheme.utils.getCardClasses('glass');
const buttonClasses = workspaceTheme.utils.getButtonClasses('primary');
</script>

<template>
  <div :class="pageClasses">
    <Card :class="cardClasses">
      <CardContent>
        <Button :class="buttonClasses">
          Workspace 操作
        </Button>
      </CardContent>
    </Card>
  </div>
</template>
```

## ✅ 最佳實踐

### 1. 色彩使用

```vue
<!-- ✅ 使用語意化色彩變數 -->
<div class="text-primary bg-primary/10">
<div class="text-success bg-success/10">
<div class="text-error bg-error/10">

<!-- ❌ 避免硬編碼色彩 -->
<div class="text-blue-600 bg-blue-100">
<div class="text-green-600 bg-green-100">
```

### 2. 間距使用

```vue
<!-- ✅ 使用一致的間距系統 -->
<div class="p-4 space-y-4">        <!-- Admin 緊湊型 -->
<div class="p-6 space-y-6">        <!-- Workspace 舒適型 -->

<!-- ❌ 避免任意間距值 -->
<div class="p-5 space-y-5">
<div class="p-7 space-y-7">
```

### 3. 響應式設計

```vue
<!-- ✅ 使用標準斷點 -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
<div class="text-sm md:text-base lg:text-lg">

<!-- ✅ 移動端優先 -->
<Button size="sm" class="md:size-default">
```

### 4. 無障礙設計

```vue
<!-- ✅ 提供適當的 ARIA 標籤 -->
<Button aria-label="關閉對話框">
  <X class="w-4 h-4" />
</Button>

<!-- ✅ 確保鍵盤導航 -->
<div role="tablist" aria-orientation="horizontal">
  <button role="tab" aria-selected="true">標籤1</button>
  <button role="tab" aria-selected="false">標籤2</button>
</div>
```

### 5. 效能優化

```vue
<!-- ✅ 使用 CSS 變數進行主題切換 -->
<div class="bg-background text-foreground">

<!-- ✅ 避免內聯樣式 -->
<div class="transition-colors duration-200">

<!-- ❌ 避免複雜的計算樣式 -->
<div :style="{ color: computedColor }">
```

---

**版本：** 1.0.0  
**最後更新：** 2024-12-19  
**維護團隊：** HorizAI Design System Team
