/**
 * HorizAI Design System
 * 
 * 統一導出所有設計系統相關的功能
 * 包含 tokens、主題、區域配置和工具函數
 * 
 * <AUTHOR> Design System Team
 * @version 1.0.0
 */

// ============================================================================
// Design Tokens
// ============================================================================
export { 
  designTokens,
  semanticColors,
  chartColors,
  typography,
  spacing,
  borderRadius,
  shadows,
  animation,
  breakpoints,
} from './tokens'

// ============================================================================
// 主題系統
// ============================================================================
export {
  themes,
  lightTheme,
  darkTheme,
  regionConfigs,
  adminThemeConfig,
  workspaceThemeConfig,
  themeToCSS,
  applyTheme,
  getRegionClasses,
  type Theme,
  type RegionThemeConfig,
  type ThemeMode,
  type RegionType,
} from './themes'

// ============================================================================
// Admin 區域主題
// ============================================================================
export {
  default as adminTheme,
  adminThemeConfig as adminConfig,
  adminClasses,
  getAdminPageClasses,
  getAdminCardClasses,
  getAdminButtonClasses,
  getAdminStatusClasses,
  getAdminTableClasses,
} from './admin-theme'

// ============================================================================
// Workspace 區域主題
// ============================================================================
export {
  default as workspaceTheme,
  workspaceThemeConfig as workspaceConfig,
  workspaceClasses,
  getWorkspacePageClasses,
  getWorkspaceCardClasses,
  getWorkspaceButtonClasses,
  getWorkspaceStatusClasses,
  getWorkspaceTableClasses,
  getWorkspaceNavItemClasses,
} from './workspace-theme'

// ============================================================================
// 常用工具函數
// ============================================================================

/**
 * 根據區域類型獲取對應的主題配置
 */
export function getThemeByRegion(region: 'admin' | 'workspace') {
  if (region === 'admin') {
    const adminTheme = require('./admin-theme').default
    return adminTheme
  } else {
    const workspaceTheme = require('./workspace-theme').default
    return workspaceTheme
  }
}

/**
 * 獲取語意化色彩的 CSS 變數名稱
 */
export function getSemanticColorVar(color: 'info' | 'success' | 'warning' | 'error'): string {
  return `hsl(var(--${color}))`
}

/**
 * 獲取語意化色彩的前景色 CSS 變數名稱
 */
export function getSemanticColorForegroundVar(color: 'info' | 'success' | 'warning' | 'error'): string {
  return `hsl(var(--${color}-foreground))`
}

/**
 * 生成狀態徽章的完整類別字串
 */
export function generateStatusBadgeClasses(
  status: 'active' | 'inactive' | 'pending' | 'error',
  region: 'admin' | 'workspace' = 'admin'
): string {
  const baseClasses = [
    'inline-flex items-center rounded-full text-xs font-medium border',
  ]
  
  if (region === 'admin') {
    baseClasses.push('px-2.5 py-0.5')
    const adminTheme = require('./admin-theme').default
    baseClasses.push(adminTheme.classes.status[status])
  } else {
    baseClasses.push('px-3 py-1')
    const workspaceTheme = require('./workspace-theme').default
    baseClasses.push(workspaceTheme.classes.status[status])
  }
  
  return baseClasses.join(' ')
}

/**
 * 生成按鈕的完整類別字串
 */
export function generateButtonClasses(
  variant: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive' = 'primary',
  size: 'sm' | 'default' | 'lg' = 'default',
  region: 'admin' | 'workspace' = 'admin'
): string {
  const baseClasses = [
    'inline-flex items-center justify-center rounded-md font-medium',
    'transition-colors focus-visible:outline-none focus-visible:ring-2',
    'focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',
  ]
  
  // 尺寸類別
  switch (size) {
    case 'sm':
      baseClasses.push('h-9 px-3 text-xs')
      break
    case 'lg':
      baseClasses.push('h-11 px-8 text-base')
      break
    default:
      baseClasses.push('h-10 px-4 py-2 text-sm')
  }
  
  // 區域特定樣式
  if (region === 'admin') {
    const adminTheme = require('./admin-theme').default
    baseClasses.push(adminTheme.classes.button[variant])
  } else {
    const workspaceTheme = require('./workspace-theme').default
    baseClasses.push(workspaceTheme.classes.button[variant])
  }
  
  return baseClasses.join(' ')
}

/**
 * 生成卡片的完整類別字串
 */
export function generateCardClasses(
  variant: 'default' | 'hover' | 'glass' = 'default',
  region: 'admin' | 'workspace' = 'admin'
): string {
  if (region === 'admin') {
    const adminTheme = require('./admin-theme').default
    return adminTheme.utils.getCardClasses(variant as 'default' | 'hover')
  } else {
    const workspaceTheme = require('./workspace-theme').default
    return workspaceTheme.utils.getCardClasses(variant)
  }
}

/**
 * 獲取響應式間距類別
 */
export function getResponsiveSpacing(
  base: keyof typeof spacing,
  md?: keyof typeof spacing,
  lg?: keyof typeof spacing
): string {
  const classes = [`space-y-${base}`]
  
  if (md) {
    classes.push(`md:space-y-${md}`)
  }
  
  if (lg) {
    classes.push(`lg:space-y-${lg}`)
  }
  
  return classes.join(' ')
}

/**
 * 獲取響應式字體大小類別
 */
export function getResponsiveFontSize(
  base: keyof typeof typography.fontSize,
  md?: keyof typeof typography.fontSize,
  lg?: keyof typeof typography.fontSize
): string {
  const classes = [`text-${base}`]
  
  if (md) {
    classes.push(`md:text-${md}`)
  }
  
  if (lg) {
    classes.push(`lg:text-${lg}`)
  }
  
  return classes.join(' ')
}

/**
 * 檢查是否為深色主題
 */
export function isDarkTheme(): boolean {
  return document.documentElement.classList.contains('dark')
}

/**
 * 獲取當前主題模式
 */
export function getCurrentThemeMode(): 'light' | 'dark' {
  return isDarkTheme() ? 'dark' : 'light'
}

// ============================================================================
// 類型導出
// ============================================================================
export type {
  Theme,
  RegionThemeConfig,
  ThemeMode,
  RegionType,
} from './themes'

// ============================================================================
// 常數導出
// ============================================================================
export const DESIGN_SYSTEM_VERSION = '1.0.0'
export const SUPPORTED_REGIONS = ['admin', 'workspace'] as const
export const SUPPORTED_THEMES = ['light', 'dark'] as const
export const SEMANTIC_COLORS = ['info', 'success', 'warning', 'error'] as const
export const BUTTON_VARIANTS = ['primary', 'secondary', 'outline', 'ghost', 'destructive'] as const
export const BUTTON_SIZES = ['sm', 'default', 'lg'] as const
export const STATUS_TYPES = ['active', 'inactive', 'pending', 'error'] as const
