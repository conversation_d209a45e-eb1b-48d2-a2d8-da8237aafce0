<!--
  HorizAI Design System 主題測試組件
  
  用於測試和驗證主題切換功能
  展示 Admin 和 Workspace 區域的樣式差異
  
  使用方式：
  1. 在任何頁面中引入此組件
  2. 測試主題切換功能
  3. 驗證區域樣式差異
-->

<template>
  <div class="p-6 space-y-8">
    <!-- 標題區域 -->
    <div class="text-center space-y-4">
      <h1 class="text-3xl font-bold">HorizAI Design System 測試</h1>
      <p class="text-muted-foreground">測試主題切換和區域樣式差異</p>
    </div>

    <!-- 主題控制區域 -->
    <Card>
      <CardHeader>
        <CardTitle>主題控制</CardTitle>
        <CardDescription>測試主題切換功能</CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="flex items-center gap-4">
          <Button @click="toggleTheme" variant="outline">
            <Sun v-if="!isDarkMode" class="w-4 h-4 mr-2" />
            <Moon v-else class="w-4 h-4 mr-2" />
            {{ isDarkMode ? '切換到淺色模式' : '切換到深色模式' }}
          </Button>
          
          <div class="text-sm text-muted-foreground">
            當前主題：{{ themeMode }}
          </div>
        </div>
        
        <div class="grid grid-cols-2 gap-4">
          <Button @click="setRegion('admin')" :variant="isAdmin ? 'default' : 'outline'">
            Admin 區域
          </Button>
          <Button @click="setRegion('workspace')" :variant="isWorkspace ? 'default' : 'outline'">
            Workspace 區域
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 區域資訊顯示 -->
    <Card>
      <CardHeader>
        <CardTitle>當前區域：{{ currentRegion }}</CardTitle>
        <CardDescription>{{ regionConfig.visualStyle }} 風格</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <div class="font-medium">主色調</div>
            <div class="text-muted-foreground">{{ regionConfig.primaryColor }}</div>
          </div>
          <div>
            <div class="font-medium">背景色系</div>
            <div class="text-muted-foreground">{{ regionConfig.backgroundColorSystem }}</div>
          </div>
          <div>
            <div class="font-medium">間距模式</div>
            <div class="text-muted-foreground">{{ regionConfig.spacingMode }}</div>
          </div>
          <div>
            <div class="font-medium">視覺風格</div>
            <div class="text-muted-foreground">{{ regionConfig.visualStyle }}</div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 語意化色彩測試 -->
    <Card>
      <CardHeader>
        <CardTitle>語意化色彩</CardTitle>
        <CardDescription>測試 HorizAI 專用的語意化色彩系統</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="space-y-2">
            <div class="w-full h-12 bg-info rounded-md"></div>
            <div class="text-center text-sm font-medium">Info</div>
          </div>
          <div class="space-y-2">
            <div class="w-full h-12 bg-success rounded-md"></div>
            <div class="text-center text-sm font-medium">Success</div>
          </div>
          <div class="space-y-2">
            <div class="w-full h-12 bg-warning rounded-md"></div>
            <div class="text-center text-sm font-medium">Warning</div>
          </div>
          <div class="space-y-2">
            <div class="w-full h-12 bg-error rounded-md"></div>
            <div class="text-center text-sm font-medium">Error</div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 按鈕樣式測試 -->
    <Card>
      <CardHeader>
        <CardTitle>按鈕樣式</CardTitle>
        <CardDescription>測試不同區域的按鈕樣式</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div class="flex flex-wrap gap-2">
            <Button :class="getButtonClasses('primary')">Primary</Button>
            <Button :class="getButtonClasses('secondary')">Secondary</Button>
            <Button :class="getButtonClasses('outline')">Outline</Button>
            <Button :class="getButtonClasses('ghost')">Ghost</Button>
            <Button :class="getButtonClasses('destructive')">Destructive</Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 狀態徽章測試 -->
    <Card>
      <CardHeader>
        <CardTitle>狀態徽章</CardTitle>
        <CardDescription>測試不同狀態的徽章樣式</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="flex flex-wrap gap-2">
          <span :class="getStatusClasses('active')">Active</span>
          <span :class="getStatusClasses('inactive')">Inactive</span>
          <span :class="getStatusClasses('pending')">Pending</span>
          <span :class="getStatusClasses('error')">Error</span>
        </div>
      </CardContent>
    </Card>

    <!-- 表格樣式測試 -->
    <Card>
      <CardHeader>
        <CardTitle>表格樣式</CardTitle>
        <CardDescription>測試區域特定的表格樣式</CardDescription>
      </CardHeader>
      <CardContent>
        <div :class="tableClasses.container">
          <table class="w-full">
            <thead :class="tableClasses.header">
              <tr>
                <th :class="tableClasses.cell + ' text-left'">名稱</th>
                <th :class="tableClasses.cell + ' text-left'">狀態</th>
                <th :class="tableClasses.cell + ' text-left'">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr :class="tableClasses.row">
                <td :class="tableClasses.cell">測試項目 1</td>
                <td :class="tableClasses.cell">
                  <span :class="getStatusClasses('active')">Active</span>
                </td>
                <td :class="tableClasses.cell">
                  <Button size="sm" variant="ghost">編輯</Button>
                </td>
              </tr>
              <tr :class="tableClasses.row">
                <td :class="tableClasses.cell">測試項目 2</td>
                <td :class="tableClasses.cell">
                  <span :class="getStatusClasses('pending')">Pending</span>
                </td>
                <td :class="tableClasses.cell">
                  <Button size="sm" variant="ghost">編輯</Button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>

    <!-- 主題變數資訊 -->
    <Card>
      <CardHeader>
        <CardTitle>主題變數</CardTitle>
        <CardDescription>當前主題的 CSS 變數值</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs font-mono">
          <div v-for="(value, key) in themeVariables" :key="key" class="flex justify-between">
            <span class="text-muted-foreground">{{ key }}:</span>
            <span>{{ value }}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Sun, Moon } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useTheme } from '@/composables/useTheme'
import { useRegionTheme } from '@/composables/useRegionTheme'

// 使用主題 composables
const { isDarkMode, themeMode, toggleTheme, getThemeVariables } = useTheme()
const {
  currentRegion,
  regionConfig,
  isAdmin,
  isWorkspace,
  getButtonClasses,
  getStatusClasses,
  getTableClasses,
  setRegion,
} = useRegionTheme()

// 計算屬性
const themeVariables = computed(() => getThemeVariables())
const tableClasses = computed(() => getTableClasses())
</script>
