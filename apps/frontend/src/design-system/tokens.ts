/**
 * HorizAI Design Tokens
 * 
 * 基於 shadcn/ui 標準的 HorizAI 專用設計 token 系統
 * 提供統一的色彩、字體、間距、圓角、陰影等設計變數
 * 
 * <AUTHOR> Design System Team
 * @version 1.0.0
 */

// ============================================================================
// 色彩系統 (Color System)
// ============================================================================

/**
 * 語意化色彩定義
 * 基於 shadcn/ui 標準，擴展 HorizAI 專用的語意化色彩
 */
export const semanticColors = {
  // 主要色彩 - 基於 shadcn/ui 標準
  primary: {
    DEFAULT: 'hsl(221.2 83.2% 53.3%)', // 藍色
    foreground: 'hsl(210 40% 98%)',
    light: 'hsl(221.2 83.2% 63.3%)',
    dark: 'hsl(221.2 83.2% 43.3%)',
  },
  
  // HorizAI 專用語意化色彩
  info: {
    DEFAULT: 'hsl(221.2 83.2% 53.3%)', // 使用 primary 色調
    foreground: 'hsl(210 40% 98%)',
    light: 'hsl(221.2 83.2% 63.3%)',
    dark: 'hsl(221.2 83.2% 43.3%)',
  },
  
  success: {
    DEFAULT: 'hsl(142.1 76.2% 36.3%)', // 綠色
    foreground: 'hsl(210 40% 98%)',
    light: 'hsl(142.1 76.2% 46.3%)',
    dark: 'hsl(142.1 76.2% 26.3%)',
  },
  
  warning: {
    DEFAULT: 'hsl(32.2 95% 44%)', // 橙色
    foreground: 'hsl(210 40% 98%)',
    light: 'hsl(32.2 95% 54%)',
    dark: 'hsl(32.2 95% 34%)',
  },
  
  error: {
    DEFAULT: 'hsl(0 84.2% 60.2%)', // 使用 destructive 色調
    foreground: 'hsl(210 40% 98%)',
    light: 'hsl(0 84.2% 70.2%)',
    dark: 'hsl(0 84.2% 50.2%)',
  },
} as const;

/**
 * 圖表色彩系統
 * 用於數據視覺化和圖表組件
 */
export const chartColors = {
  1: 'hsl(142.1 76.2% 36.3%)', // Success Green
  2: 'hsl(221.2 83.2% 53.3%)', // Primary Blue
  3: 'hsl(32.2 95% 44%)',      // Warning Orange
  4: 'hsl(0 84.2% 60.2%)',     // Error Red
  5: 'hsl(271.5 81.3% 55.9%)', // Purple
  6: 'hsl(197.4 71.4% 52.5%)', // Cyan
  7: 'hsl(47.9 95.8% 53.1%)',  // Yellow
  8: 'hsl(339.6 82.2% 51.6%)', // Pink
} as const;

// ============================================================================
// 字體系統 (Typography System)
// ============================================================================

/**
 * 字體尺寸和行高系統
 * 基於 Tailwind CSS 的字體層級，提供一致的字體使用規範
 */
export const typography = {
  // 字體尺寸
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
  },
  
  // 行高
  lineHeight: {
    xs: '1rem',       // 16px
    sm: '1.25rem',    // 20px
    base: '1.5rem',   // 24px
    lg: '1.75rem',    // 28px
    xl: '1.75rem',    // 28px
    '2xl': '2rem',    // 32px
    '3xl': '2.25rem', // 36px
    '4xl': '2.5rem',  // 40px
  },
  
  // 字重
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
} as const;

// ============================================================================
// 間距系統 (Spacing System)
// ============================================================================

/**
 * 間距系統
 * 基於 4px 基準的間距系統，提供一致的佈局間距
 */
export const spacing = {
  // 基礎間距 (基於 4px)
  xs: '0.5rem',   // 8px
  sm: '0.75rem',  // 12px
  md: '1rem',     // 16px
  lg: '1.5rem',   // 24px
  xl: '2rem',     // 32px
  '2xl': '3rem',  // 48px
  '3xl': '4rem',  // 64px
  
  // 組件專用間距
  component: {
    padding: {
      xs: '0.5rem',   // 8px
      sm: '0.75rem',  // 12px
      md: '1rem',     // 16px
      lg: '1.5rem',   // 24px
    },
    margin: {
      xs: '0.5rem',   // 8px
      sm: '0.75rem',  // 12px
      md: '1rem',     // 16px
      lg: '1.5rem',   // 24px
    },
  },
} as const;

// ============================================================================
// 圓角系統 (Border Radius System)
// ============================================================================

/**
 * 圓角系統
 * 提供一致的圓角使用規範
 */
export const borderRadius = {
  none: '0',
  sm: '0.125rem',   // 2px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px - 預設值
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  full: '9999px',   // 完全圓形
} as const;

// ============================================================================
// 陰影系統 (Shadow System)
// ============================================================================

/**
 * 陰影系統
 * 提供不同層級的陰影效果
 */
export const shadows = {
  soft: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  medium: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  strong: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
} as const;

// ============================================================================
// 動畫系統 (Animation System)
// ============================================================================

/**
 * 動畫時間和緩動函數
 * 提供一致的動畫體驗
 */
export const animation = {
  // 動畫時間
  duration: {
    fast: '150ms',
    normal: '200ms',
    slow: '300ms',
    slower: '500ms',
  },
  
  // 緩動函數
  easing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
    linear: 'linear',
  },
  
  // 常用動畫組合
  transition: {
    fast: 'all 150ms ease-out',
    normal: 'all 200ms ease-out',
    slow: 'all 300ms ease-out',
    colors: 'color 200ms ease-out, background-color 200ms ease-out, border-color 200ms ease-out',
  },
} as const;

// ============================================================================
// 響應式斷點 (Responsive Breakpoints)
// ============================================================================

/**
 * 響應式斷點系統
 * 基於 Tailwind CSS 的標準斷點
 */
export const breakpoints = {
  sm: '640px',    // 小型設備
  md: '768px',    // 平板設備
  lg: '1024px',   // 筆記型電腦
  xl: '1280px',   // 桌面設備
  '2xl': '1536px', // 大型桌面設備
} as const;

// ============================================================================
// 導出所有 Design Tokens
// ============================================================================

/**
 * HorizAI Design Tokens 完整集合
 */
export const designTokens = {
  colors: semanticColors,
  chart: chartColors,
  typography,
  spacing,
  borderRadius,
  shadows,
  animation,
  breakpoints,
} as const;

export default designTokens;
