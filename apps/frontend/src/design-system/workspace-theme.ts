/**
 * Workspace 區域主題配置
 * 
 * 創新、協作的視覺風格，適用於工作流程和團隊協作
 * 使用綠色系作為主色調，zinc 色系作為背景
 * 
 * <AUTHOR> Design System Team
 * @version 1.0.0
 */

import { type RegionThemeConfig } from './themes';

// ============================================================================
// Workspace 區域主題配置
// ============================================================================

/**
 * Workspace 區域的主題配置
 * 協作、體驗導向的視覺風格
 */
export const workspaceThemeConfig: RegionThemeConfig = {
  primaryColor: 'hsl(var(--success))',      // 綠色系 - 積極進取
  accentColor: 'hsl(var(--success))',       // 成功綠色 - 強調成就
  backgroundColorSystem: 'zinc',            // 使用 zinc 色系 - 現代時尚
  spacingMode: 'comfortable',               // 舒適型間距 - 提升體驗
  visualStyle: 'collaborative',             // 協作風格 - 注重互動
};

// ============================================================================
// Workspace 專用 CSS 類別定義
// ============================================================================

/**
 * Workspace 區域專用的 CSS 類別組合
 */
export const workspaceClasses = {
  // 佈局類別
  layout: {
    container: 'bg-zinc-50/30 dark:bg-zinc-900',
    sidebar: 'bg-white dark:bg-zinc-800 border-zinc-200/50 dark:border-zinc-800/50',
    header: 'bg-white/80 dark:bg-zinc-900/80 backdrop-blur-xl border-zinc-200/50 dark:border-zinc-800/50',
    content: 'bg-white dark:bg-zinc-900',
  },
  
  // 文字色彩
  text: {
    primary: 'text-zinc-900 dark:text-zinc-100',
    secondary: 'text-zinc-600 dark:text-zinc-400',
    muted: 'text-zinc-500 dark:text-zinc-500',
    accent: 'text-emerald-600 dark:text-emerald-400',
    success: 'text-emerald-600 dark:text-emerald-400',
    warning: 'text-amber-600 dark:text-amber-400',
    error: 'text-red-600 dark:text-red-400',
  },
  
  // 背景色彩
  background: {
    primary: 'bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-500 dark:hover:bg-emerald-600',
    secondary: 'bg-zinc-100 dark:bg-zinc-800 hover:bg-zinc-200 dark:hover:bg-zinc-700',
    success: 'bg-emerald-50 dark:bg-emerald-500/10 text-emerald-600 dark:text-emerald-400',
    warning: 'bg-amber-50 dark:bg-amber-500/10 text-amber-600 dark:text-amber-400',
    error: 'bg-red-50 dark:bg-red-500/10 text-red-600 dark:text-red-400',
    info: 'bg-blue-50 dark:bg-blue-500/10 text-blue-600 dark:text-blue-400',
  },
  
  // 邊框樣式
  border: {
    default: 'border-zinc-200/50 dark:border-zinc-800/50',
    accent: 'border-emerald-500/20 dark:border-emerald-400/20',
    success: 'border-emerald-200 dark:border-emerald-800',
    warning: 'border-amber-200 dark:border-amber-800',
    error: 'border-red-200 dark:border-red-800',
  },
  
  // 間距模式 - 舒適型
  spacing: {
    page: 'p-6 space-y-6',
    section: 'p-6 space-y-4',
    card: 'p-6',
    list: 'space-y-3',
    form: 'space-y-4',
  },
  
  // 按鈕樣式
  button: {
    primary: 'bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-500 dark:hover:bg-emerald-600 text-white',
    secondary: 'bg-zinc-100 dark:bg-zinc-800 hover:bg-zinc-200 dark:hover:bg-zinc-700 text-zinc-900 dark:text-zinc-100',
    outline: 'border border-zinc-200 dark:border-zinc-700 hover:bg-zinc-50 dark:hover:bg-zinc-800',
    ghost: 'hover:bg-emerald-50 dark:hover:bg-emerald-500/10 hover:text-emerald-600 dark:hover:text-emerald-400',
    destructive: 'bg-red-600 hover:bg-red-700 text-white',
  },
  
  // 表格樣式
  table: {
    container: 'border border-zinc-200/50 dark:border-zinc-800/50 rounded-lg overflow-hidden',
    header: 'bg-zinc-50 dark:bg-zinc-800 border-b border-zinc-200/50 dark:border-zinc-800/50',
    row: 'border-b border-zinc-200/50 dark:border-zinc-800/50 hover:bg-zinc-50 dark:hover:bg-zinc-800/50',
    cell: 'px-6 py-4 text-sm',
  },
  
  // 卡片樣式
  card: {
    default: 'bg-white dark:bg-zinc-800 border border-zinc-200/50 dark:border-zinc-800/50 rounded-lg shadow-sm',
    hover: 'hover:shadow-lg hover:shadow-zinc-200/20 dark:hover:shadow-zinc-900/30 transition-all duration-300',
    header: 'border-b border-zinc-200/50 dark:border-zinc-800/50 p-6',
    content: 'p-6',
  },
  
  // 表單樣式
  form: {
    label: 'text-sm font-medium text-zinc-700 dark:text-zinc-300',
    input: 'border-zinc-200 dark:border-zinc-700 focus:border-emerald-500 focus:ring-emerald-500',
    error: 'text-red-600 dark:text-red-400 text-sm',
    help: 'text-zinc-500 dark:text-zinc-400 text-sm',
  },
  
  // 狀態指示器
  status: {
    active: 'bg-emerald-50 dark:bg-emerald-500/10 text-emerald-600 dark:text-emerald-400 border-emerald-200 dark:border-emerald-800',
    inactive: 'bg-zinc-100 dark:bg-zinc-800 text-zinc-600 dark:text-zinc-400 border-zinc-200 dark:border-zinc-700',
    pending: 'bg-amber-50 dark:bg-amber-500/10 text-amber-600 dark:text-amber-400 border-amber-200 dark:border-amber-800',
    error: 'bg-red-50 dark:bg-red-500/10 text-red-600 dark:text-red-400 border-red-200 dark:border-red-800',
  },
  
  // 特殊效果
  effects: {
    glassmorphism: 'backdrop-blur-xl bg-white/80 dark:bg-zinc-900/80',
    gradient: 'bg-gradient-to-r from-emerald-500 to-emerald-600',
    glow: 'shadow-lg shadow-emerald-500/25',
  },
} as const;

// ============================================================================
// Workspace 專用工具函數
// ============================================================================

/**
 * 獲取 Workspace 區域的頁面容器類別
 */
export function getWorkspacePageClasses(): string {
  return [
    workspaceClasses.layout.container,
    workspaceClasses.spacing.page,
    'min-h-screen',
  ].join(' ');
}

/**
 * 獲取 Workspace 區域的卡片類別
 */
export function getWorkspaceCardClasses(variant: 'default' | 'hover' | 'glass' = 'default'): string {
  const baseClasses = [workspaceClasses.card.default];
  
  if (variant === 'hover') {
    baseClasses.push(workspaceClasses.card.hover);
  } else if (variant === 'glass') {
    baseClasses.push(workspaceClasses.effects.glassmorphism);
  }
  
  return baseClasses.join(' ');
}

/**
 * 獲取 Workspace 區域的按鈕類別
 */
export function getWorkspaceButtonClasses(
  variant: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive' = 'primary'
): string {
  const baseClasses = [
    'inline-flex items-center justify-center rounded-md text-sm font-medium',
    'transition-all duration-200 focus-visible:outline-none focus-visible:ring-2',
    'focus-visible:ring-emerald-500 disabled:pointer-events-none disabled:opacity-50',
    'h-10 px-4 py-2',
  ];
  
  baseClasses.push(workspaceClasses.button[variant]);
  
  return baseClasses.join(' ');
}

/**
 * 獲取 Workspace 區域的狀態徽章類別
 */
export function getWorkspaceStatusClasses(
  status: 'active' | 'inactive' | 'pending' | 'error'
): string {
  const baseClasses = [
    'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border',
  ];
  
  baseClasses.push(workspaceClasses.status[status]);
  
  return baseClasses.join(' ');
}

/**
 * 獲取 Workspace 區域的表格類別
 */
export function getWorkspaceTableClasses(): {
  container: string;
  header: string;
  row: string;
  cell: string;
} {
  return {
    container: workspaceClasses.table.container,
    header: workspaceClasses.table.header,
    row: workspaceClasses.table.row,
    cell: workspaceClasses.table.cell,
  };
}

/**
 * 獲取 Workspace 區域的導航項目類別
 */
export function getWorkspaceNavItemClasses(isActive: boolean = false): string {
  const baseClasses = [
    'flex items-center gap-3 w-full py-2 px-3 rounded-md transition-colors',
    'text-sm font-medium',
  ];
  
  if (isActive) {
    baseClasses.push('text-emerald-600 dark:text-emerald-400 bg-emerald-50 dark:bg-emerald-500/10');
  } else {
    baseClasses.push('text-zinc-600 dark:text-zinc-400 hover:bg-emerald-50 dark:hover:bg-emerald-500/10');
  }
  
  return baseClasses.join(' ');
}

// ============================================================================
// 導出 Workspace 主題
// ============================================================================

export default {
  config: workspaceThemeConfig,
  classes: workspaceClasses,
  utils: {
    getPageClasses: getWorkspacePageClasses,
    getCardClasses: getWorkspaceCardClasses,
    getButtonClasses: getWorkspaceButtonClasses,
    getStatusClasses: getWorkspaceStatusClasses,
    getTableClasses: getWorkspaceTableClasses,
    getNavItemClasses: getWorkspaceNavItemClasses,
  },
} as const;
