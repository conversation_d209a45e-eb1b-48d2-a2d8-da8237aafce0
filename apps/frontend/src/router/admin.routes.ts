import { type RouteRecordRaw } from "vue-router";
import AdminLayout from "@/components/layouts/AdminLayout.vue";

export const adminRoutes: RouteRecordRaw[] = [
  {
    path: "/admin",
    component: AdminLayout,
    meta: {
      requiresAuth: true,
      permission: {
        action: "access",
        subject: "AdminPanel",
      },
    },
    children: [
      {
        path: "",
        redirect: { name: "admin-dashboard" },
      },
      {
        path: "dashboard",
        name: "admin-dashboard",
        component: () => import("@/views/admin/dashboard/DashboardView.vue"),
        meta: {
          title: "管理儀表板",
          permission: {
            action: "read",
            subject: "Dashboard",
          },
        },
      },
      {
        path: "system-users",
        name: "admin-system-users",
        component: () =>
          import("@/views/admin/users/system/SystemUserManagement.vue"),
        meta: {
          title: "系統使用者管理",
          permission: {
            action: "manage",
            subject: "SystemUser",
          },
        },
      },
      {
        path: "tenants",
        name: "admin-tenants",
        component: () => import("@/views/admin/tenants/TenantsView.vue"),
        meta: {
          title: "租戶管理",
          permission: {
            action: "manage",
            subject: "Tenant",
          },
        },
      },
      {
        path: "tenant-users",
        name: "admin-tenant-users",
        component: () =>
          import("@/views/admin/users/tenant/TenantUserManagement.vue"),
        meta: {
          title: "租戶使用者管理",
          permission: {
            action: "manage",
            subject: "TenantUser",
          },
        },
      },
      {
        path: "plans",
        name: "admin-plans",
        component: () => import("@/views/admin/plans/PlansView.vue"),
        meta: {
          title: "訂閱方案",
          description: "管理訂閱方案與定價",
          permission: {
            action: "manage",
            subject: "Plan",
          },
        },
      },
      {
        path: "roles",
        name: "admin-roles",
        component: () => import("@/views/admin/roles/RoleListView.vue"),
        meta: {
          title: "角色管理",
          description: "管理系統角色",
          permission: {
            action: "manage",
            subject: "Role",
          },
        },
      },
      {
        path: "permissions",
        name: "admin-permissions",
        component: () =>
          import("@/views/admin/permissions/PermissionSettingView.vue"),
        meta: {
          title: "權限設定",
          description: "管理角色權限",
          permission: {
            action: "manage",
            subject: "Role",
          },
        },
      },
      {
        path: "permissions/sync",
        name: "admin-permissions-sync",
        component: () =>
          import("@/views/admin/permissions/PermissionSyncView.vue"),
        meta: {
          title: "權限同步管理",
          description: "管理權限掃描與同步",
          permission: {
            action: "manage",
            subject: "Permission",
          },
        },
      },
      {
        path: "workspaces",
        name: "admin-workspaces",
        component: () =>
          import("@/views/admin/workspaces/WorkspaceManagementView.vue"),
        meta: {
          title: "工作區管理",
          description: "管理系統工作區",
          permission: {
            action: "manage",
            subject: "Workspace",
          },
        },
      },
      {
        path: "settings",
        name: "admin-settings",
        component: () =>
          import("@/views/admin/settings/SystemSettingsView.vue"),
        meta: {
          title: "系統設定",
          description: "管理系統整體設定",
          permission: {
            action: "manage",
            subject: "SystemSettings",
          },
        },
      },
      {
        path: "orders",
        name: "admin-orders",
        component: () => import("@/views/admin/orders/OrdersView.vue"),
        meta: {
          title: "訂閱訂單",
          description: "管理訂閱訂單與付款",
          permission: {
            action: "manage",
            subject: "Order",
          },
        },
      },
      {
        path: "orders/:id",
        name: "admin-order-detail",
        component: () => import("@/views/admin/orders/OrderDetailView.vue"),
        meta: {
          title: "訂單詳情",
          permission: {
            action: "manage",
            subject: "Order",
          },
        },
      },
      // AI 管理系統路由 - 統一架構
      {
        path: "ai-dashboard",
        name: "admin-ai-dashboard",
        component: () => import("@/views/admin/ai/AIDashboard.vue"),
        meta: {
          title: "AI 管理中心",
          description: "AI 功能概覽和快速操作",
          permission: {
            action: "manage",
            subject: "SystemSettings",
          },
        },
      },
      {
        path: "ai-settings",
        name: "admin-ai-settings",
        component: () => import("@/views/admin/ai/settings/AISettings.vue"),
        meta: {
          title: "AI 系統管理",
          description: "統一的 AI 管理中心",
          permission: {
            action: "manage",
            subject: "SystemSettings",
          },
        },
      },
      {
        path: "line-settings",
        name: "admin-line-settings",
        component: () =>
          import("@/views/line/LineSettings.vue"),
        meta: {
          title: "Line 應用設定",
          description: "管理 Line 相關整合設定",
          permission: {
            action: "manage",
            subject: "SystemSettings",
          },
        },
      },
      {
        path: "system-logs",
        name: "admin-system-logs",
        component: () => import("@/views/admin/system-logs/SystemLogsView.vue"),
        meta: {
          title: "系統日誌",
          description: "檢視系統操作日誌",
          permission: {
            action: "manage",
            subject: "SystemLog",
          },
        },
      },
      // 開發工具路由
      {
        path: "forms/test",
        name: "admin-forms-test",
        component: () => import("@/components/forms/FormTestPage.vue"),
        meta: {
          title: "表單系統測試",
          description: "測試統一表單組件和驗證系統",
          permission: {
            action: "manage",
            subject: "SystemSettings",
          },
          isDevelopment: true, // 標記為開發工具
        },
      },
      {
        path: "tables/test",
        name: "admin-tables-test",
        component: () => import("@/components/tables/TableTestPage.vue"),
        meta: {
          title: "表格系統測試",
          description: "測試統一表格組件和虛擬滾動系統",
          permission: {
            action: "manage",
            subject: "SystemSettings",
          },
          isDevelopment: true, // 標記為開發工具
        },
      },
      {
        path: "dialogs/test",
        name: "admin-dialogs-test",
        component: () => import("@/components/dialogs/DialogTestPage.vue"),
        meta: {
          title: "對話框系統測試",
          description: "測試統一對話框組件和狀態管理系統",
          permission: {
            action: "manage",
            resource: "system",
          },
          isDevelopment: true, // 標記為開發工具
        },
      },
    ],
  },
  // AI 助理工作室 - 獨立頁面路由
  {
    path: "/ai-studio/:id?",
    name: "ai-studio",
    component: () => import("@/views/admin/ai/agents/AgentBuilder.vue"),
    meta: {
      title: "AI 助理工作室",
      description: "創建、配置和測試您的 AI 助理",
      permission: {
        action: "manage",
        subject: "SystemSettings",
      },
    },
  },
];

export default adminRoutes;







