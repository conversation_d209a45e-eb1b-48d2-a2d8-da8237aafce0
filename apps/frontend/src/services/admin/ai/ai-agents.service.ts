import { httpService } from '@/services/http.service';
import type {
  AiBot,
  AiBotWithRelations,
  AiBotScope,
  AiBotProviderType,
  AiBotResponseFormat,
} from '@/types/models/ai.model';
import type {
  CreateBotDto,
  UpdateBotDto,
  TestBotDto,
  OptimizePromptDto,
  ExecuteBotDto,
} from '@/types/dto/ai-agents.dto';

const BASE_URL = '/admin/ai/agents';

// API 回應的 Bot 型別
interface ApiBotResponse {
  id: string;
  name: string;
  description?: string;
  scene?: string;
  provider_type: AiBotProviderType;
  model_id: string;
  key_id: string;
  provider_config_override?: Record<string, any>;
  system_prompt?: string;
  temperature?: number;
  max_tokens?: number;
  response_format: AiBotResponseFormat;
  is_enabled: boolean;
  is_template: boolean;
  scope: AiBotScope;
  tenant_id?: string;
  workspace_id?: string;
  created_at: string;
  updated_at: string;
}

interface ApiResponse<T> {
  data: T;
}

export class AiAgentsService {
  /**
   * 獲取所有 Agent (需提供租戶與工作區參數)
   */
  static async findAll(params?: {
    tenant_id?: string;
    workspace_id?: string;
    scope?: AiBotScope;
  }): Promise<AiBotWithRelations[]> {
    // 如果提供 params 則帶入 query，否則直接取所有
    const url = params ? `${BASE_URL}?${new URLSearchParams(params as Record<string, string>).toString()}` : BASE_URL;
    return httpService.get<AiBotWithRelations[]>(url);
  }

  /**
   * 獲取單一 Agent
   */
  static async findOne(id: string): Promise<AiBot> {
    return httpService.get<AiBot>(`${BASE_URL}/${id}`);
  }

  /**
   * 建立新的 Agent
   */
  static async create(create_bot_dto: CreateBotDto): Promise<AiBot> {
    return httpService.post<AiBot>(BASE_URL, create_bot_dto);
  }

  /**
   * 更新 Agent
   */
  static async update(id: string, update_bot_dto: UpdateBotDto): Promise<AiBot> {
    return httpService.put<AiBot>(`${BASE_URL}/${id}`, update_bot_dto);
  }

  /**
   * 刪除 Agent
   */
  static async delete(id: string): Promise<void> {
    await httpService.delete(`${BASE_URL}/${id}`);
  }

  /**
   * 測試 Agent 設定 (按照 swagger: 無 id 路徑，請在 body 帶入 botId)
   */
  static async test(test_bot_dto: TestBotDto): Promise<{ response: string }> {
    return httpService.post<{ response: string }>(`${BASE_URL}/test`, test_bot_dto);
  }

  /**
   * 優化提示詞
   */
  static async optimize_prompt(
    optimize_prompt_dto: OptimizePromptDto,
  ): Promise<{ optimized_prompt: string }> {
    return httpService.post<{ optimized_prompt: string }>(
      `${BASE_URL}/optimize-prompt`,
      optimize_prompt_dto,
    );
  }

  /**
   * 使用指定 Agent 進行對話
   */
  static async chat(
    id: string,
    data: {
      message: string;
      temperature?: number;
      prompt?: string;
      system_prompt?: string;
    },
  ): Promise<{ content: string }> {
    const response = await httpService.post<ApiResponse<{ content: string }>>(
      `${BASE_URL}/${id}/chat`,
      data,
    );
    return response.data;
  }

  /**
   * 執行 Agent
   */
  static async execute(
    bot_id: string,
    execute_bot_dto: ExecuteBotDto,
  ): Promise<{ response: string }> {
    return httpService.post<{ response: string }>(`${BASE_URL}/${bot_id}/execute`, execute_bot_dto);
  }

  /**
   * 更新 Agent 啟用狀態
   */
  static async update_status(id: string, is_enabled: boolean): Promise<AiBot> {
    const response = await httpService.put<ApiResponse<AiBot>>(`${BASE_URL}/${id}/status`, {
      is_enabled,
    });
    return response.data;
  }
}

// 為了向後兼容，保留 AiBotsService 別名
export const AiBotsService = AiAgentsService; 