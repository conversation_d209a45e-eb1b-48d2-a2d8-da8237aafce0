import type { 
  AiBotScope, 
  AiBotProviderType, 
  AiBotResponseFormat 
} from '@/types/models/ai.model';

export interface CreateBotDto {
  name: string;
  scope: AiBotScope;
  provider_type: AiBotProviderType;
  model_id: string;
  key_id: string;
  provider_config_override?: Record<string, any>;
  system_prompt?: string;
  temperature?: number;
  max_tokens?: number;
  response_format?: AiBotResponseFormat;
  is_enabled?: boolean;
  is_template?: boolean;
  description?: string;
  scene?: string;
  tenant_id?: string;
  workspace_id?: string;
}

export interface UpdateBotDto {
  name?: string;
  scope?: AiBotScope;
  provider_type?: AiBotProviderType;
  model_id?: string;
  key_id?: string;
  provider_config_override?: Record<string, any>;
  system_prompt?: string;
  temperature?: number;
  max_tokens?: number;
  response_format?: AiBotResponseFormat;
  is_enabled?: boolean;
  is_template?: boolean;
  description?: string;
  scene?: string;
  tenant_id?: string;
  workspace_id?: string;
}

export interface TestBotDto {
  agentId: string;
  message: string;
  prompt?: string;
  temperature?: number;
}

export interface OptimizePromptDto {
  id: string;
  provider: 'openai' | 'claude' | 'openai-compatible';
  apiKey: string;
  apiUrl?: string;
  prompt: string;
  scene?: string;
  requirement?: string;
}

export interface ExecuteBotDto {
  messages: { role: string; content: string }[];
  temperature?: number;
}
