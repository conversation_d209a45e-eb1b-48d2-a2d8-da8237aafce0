// AI Agent 型別定義
import type { AIModel as BaseAIModel } from "@/types/models/ai.model";
import { AiBotProviderType } from "@/types/models/ai.model";
export type { AIModel } from "@/types/models/ai.model";

/**
 * AI Agent 範疇
 * SYSTEM: 系統範圍 Agent，由系統管理員管理
 * WORKSPACE: 工作區範圍 Agent，由工作區管理員管理
 * TENANT_TEMPLATE: 租戶模板 Agent，可供租戶下的工作區使用
 */
// export type AgentScope = \'SYSTEM\' | \'WORKSPACE\' | \'TENANT_TEMPLATE\'
// export type AgentResponseFormat = \'text\' | \'json_object\' | \'json_schema\'

export enum AgentScope {
  SYSTEM = "SYSTEM",
  WORKSPACE = "WORKSPACE",
  TENANT_TEMPLATE = "TENANT_TEMPLATE",
}

export enum AgentResponseFormat {
  TEXT = "TEXT",
  JSON_OBJECT = "JSON_OBJECT",
}

export interface AIAgent {
  id: string;
  name: string;
  description: string;
  scene: string; // 場景類型
  provider: "openai" | "claude" | "openai-compatible";
  provider_type: AiBotProviderType;
  apiKey?: string;
  apiUrl?: string;
  model: string;
  model_id?: string;
  temperature: number;
  prompt?: string; // 為向後相容保留
  system_prompt?: string; // 用於存儲系統提示詞，與後端 API 對應
  response_format?: AgentResponseFormat; // 從後端 response_format 映射
  createdAt: string;
  updatedAt: string;
  scope?: AgentScope;
  isTemplate?: boolean;
  isEnabled?: boolean;
  tenantId?: string;
  createdBy?: string;
  updatedBy?: string;
  isEditableCoreProps?: boolean;
  isDeletable?: boolean;
  keyId?: string;
  key_id?: string; // 與後端 API 對應
  is_template?: boolean; // 與後端 API 對應
  is_enabled?: boolean; // 與後端 API 對應
  created_at?: string; // 與後端 API 對應
  updated_at?: string; // 與後端 API 對應
}

export interface AgentTestRequest {
  agentId: string;
  message: string;
  temperature?: number;
  prompt?: string;
}

export interface AgentTestResponse {
  content: string;
}

export interface AgentScene {
  value: string;
  label: string;
  description: string;
}

export const DEFAULT_AGENT_SCENES: AgentScene[] = [
  { 
    value: "customer_service", 
    label: "客服助理",
    description: "提供專業的客戶服務支援，處理客戶諮詢、問題解答和售後服務"
  },
  { 
    value: "marketing", 
    label: "行銷助手",
    description: "協助制定行銷策略、內容創作和市場推廣活動的專業助理"
  },
  { 
    value: "tech_support", 
    label: "技術支援",
    description: "提供技術問題診斷、故障排除和系統維護支援的技術專家助理"
  },
  { 
    value: "internal_knowledge", 
    label: "知識管理",
    description: "整理、分析和管理企業內部知識庫，提供智能知識檢索服務"
  },
  { 
    value: "general_assistant", 
    label: "一般助理",
    description: "多功能智能助理，可處理各種日常任務和一般性問題"
  },
];
