import { useAuthStore } from '@horizai/auth'
import type { RouteLocationNormalized } from 'vue-router'
import { loggers, debugConfig } from '@/config/debug.config'

const logger = loggers.auth

/**
 * 認證檢查結果
 */
export interface AuthCheckResult {
    isAuthenticated: boolean
    hasValidToken: boolean
    needsRefresh: boolean
    user: any | null
    error?: string
}

/**
 * 路由認證要求
 */
export interface RouteAuthRequirement {
    requiresAuth: boolean
    requiresGuest: boolean
    allowUnauthenticated: boolean
}

/**
 * 檢查當前認證狀態
 * @returns {AuthCheckResult} 完整的認證狀態資訊
 */
export function checkAuthStatus(): AuthCheckResult {
    const authStore = useAuthStore()

    try {
        const isAuthenticated = authStore.isAuthenticated
        const user = authStore.currentUser
        const hasValidToken = isAuthenticated

        if (debugConfig.enableAuthLogs) {
            logger.debug('認證狀態檢查:', {
                processedIsAuthenticated: isAuthenticated,
                hasUser: !!user,
                hasValidToken,
                userId: user?.id || null
            })
        }

        return {
            isAuthenticated,
            hasValidToken,
            needsRefresh: false, // 可以根據需要實現 token 刷新邏輯
            user
        }
    } catch (error) {
        logger.error('認證狀態檢查失敗:', error)
        return {
            isAuthenticated: false,
            hasValidToken: false,
            needsRefresh: false,
            user: null,
            error: error instanceof Error ? error.message : '認證檢查失敗'
        }
    }
}

/**
 * 解析路由的認證要求
 */
export function parseRouteAuthRequirement(route: RouteLocationNormalized): RouteAuthRequirement {
    const requiresAuth = route.matched.some(
        (record) => record.meta.requiresAuth === true
    )
    const requiresGuest = route.matched.some(
        (record) => record.meta.requiresGuest === true
    )

    return {
        requiresAuth,
        requiresGuest,
        allowUnauthenticated: !requiresAuth && !requiresGuest
    }
}

/**
 * 檢查路由是否允許當前認證狀態訪問
 */
export function isRouteAccessible(
    route: RouteLocationNormalized,
    authStatus: AuthCheckResult
): { accessible: boolean; reason?: string } {
    const requirement = parseRouteAuthRequirement(route)

    // 如果路由不需要特定認證狀態，允許訪問
    if (requirement.allowUnauthenticated) {
        return { accessible: true }
    }

    // 如果需要認證但用戶未認證
    if (requirement.requiresAuth && !authStatus.isAuthenticated) {
        return {
            accessible: false,
            reason: 'authentication_required'
        }
    }

    // 如果需要訪客狀態但用戶已認證
    if (requirement.requiresGuest && authStatus.isAuthenticated) {
        return {
            accessible: false,
            reason: 'guest_only'
        }
    }

    return { accessible: true }
}

/**
 * 檢查用戶是否需要完成額外的設置步驟
 */
export async function checkUserSetupStatus(user: any, currentPath?: string): Promise<{
    needsSetup: boolean
    setupType?: 'tenant' | 'profile' | 'verification'
    redirectTo?: string
}> {
    if (!user) {
        return { needsSetup: false }
    }

    // Super Admin 跳過所有設置檢查
    if (user.role === 'SUPER_ADMIN' || user.email === '<EMAIL>') {
        return { needsSetup: false }
    }

    // 如果用戶正在進行公司設置流程，跳過租戶檢查
    const setupPaths = [
        '/welcome/no-tenant',
        '/welcome/personal',
        '/auth/company-setup',
        '/auth/company-setup/create',
        '/auth/company-setup/join',
        '/auth/company-setup/pending',
        '/auth/company-conflict'
    ]

    if (currentPath && setupPaths.some(path => currentPath.startsWith(path))) {
        return { needsSetup: false }
    }

    try {
        // 檢查是否需要租戶設置
        const tenantService = (await import('@/services/admin/tenant.service')).default
        const hasTenant = await tenantService.checkUserHasTenant()

        if (!hasTenant) {
            if (debugConfig.enableAuthLogs) {
                logger.info('用戶需要設置租戶，重定向到租戶設置頁面')
            }
            return {
                needsSetup: true,
                setupType: 'tenant',
                redirectTo: '/welcome/no-tenant'
            }
        }

        // 可以添加其他設置檢查，如：
        // - 郵箱驗證
        // - 個人資料完整性
        // - 多因素認證設置等

        return { needsSetup: false }
    } catch (error) {
        logger.error('檢查用戶設置狀態失敗:', error)
        return { needsSetup: false }
    }
}

/**
 * 獲取認證相關的錯誤訊息
 */
export function getAuthErrorMessage(reason: string): string {
    const messages: Record<string, string> = {
        'authentication_required': '請先登入以訪問此頁面',
        'guest_only': '此頁面僅供未登入用戶訪問',
        'token_expired': '登入已過期，請重新登入',
        'insufficient_permissions': '您沒有權限訪問此頁面',
        'tenant_required': '請先設置或加入公司',
        'verification_required': '請先驗證您的郵箱地址'
    }

    return messages[reason] || '訪問被拒絕'
}

/**
 * 獲取當前登入用戶
 * @returns {any | null} 當前用戶物件或 null
 */
export function getCurrentUser() {
    const authStore = useAuthStore()
    
    try {
        // currentUser 是一個 computed 屬性，直接返回值
        return authStore.currentUser
    } catch (error) {
        logger.error('獲取當前用戶失敗:', error)
        return null
    }
}

/**
 * 檢查用戶權限
 * @param action 操作類型
 * @param subject 操作對象
 * @param field 可選的欄位名稱
 * @returns {boolean} 是否有權限
 */
export function checkPermission(action: string, subject: string, field?: string): boolean {
    const authStore = useAuthStore()
    
    try {
        // 使用 getAbility() 方法獲取正確的 ability 對象
        const ability = authStore.getAbility()
        if (!ability || typeof ability.can !== 'function') {
            return false
        }
        
        return ability.can(action, subject, field)
    } catch (error) {
        logger.error('權限檢查失敗:', error)
        return false
    }
} 