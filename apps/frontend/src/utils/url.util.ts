/**
 * Converts an object to a URL query string, handling arrays correctly.
 * @param params - The object to convert.
 * @returns A URL query string.
 */
export function objectToQueryString(params: Record<string, any>): string {
  const searchParams = new URLSearchParams();

  for (const key in params) {
    if (Object.prototype.hasOwnProperty.call(params, key)) {
      const value = params[key];

      if (value === null || value === undefined || value === '') {
        continue;
      }

      if (Array.isArray(value)) {
        value.forEach(item => {
          searchParams.append(key, item);
        });
      } else {
        searchParams.set(key, String(value));
      }
    }
  }

  return searchParams.toString();
} 