<template>
  <div class="p-4 space-y-4">
    <!-- 頁面標題 -->
    <div class="flex items-center space-x-3">
      <div class="p-2 bg-primary/10 rounded-md">
        <Settings class="w-4 h-4 text-primary" />
      </div>
      <div>
        <h1 class="text-xl font-semibold text-gray-900 dark:text-zinc-100">AI 管理中心</h1>
        <p class="text-sm text-gray-600 dark:text-zinc-400 mt-0.5">
          統一管理平台的 AI 功能與智能服務
        </p>
      </div>
    </div>



      <!-- 統計卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
        <!-- 系統狀態 -->
        <Card class="p-4">
          <CardContent class="p-0">
            <div class="flex items-center gap-3">
              <div class="p-2 bg-green-100 dark:bg-green-900/30 rounded-md">
                <CheckCircle class="w-4 h-4 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p class="text-xs text-gray-600 dark:text-zinc-400">系統狀態</p>
                <p class="text-lg font-semibold text-gray-900 dark:text-zinc-100">99.9%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- AI 助理統計 -->
        <Card class="p-4">
          <CardContent class="p-0">
            <div class="flex items-center gap-3">
              <div class="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-md">
                <Bot class="w-4 h-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p class="text-xs text-gray-600 dark:text-zinc-400">AI 助理</p>
                <p class="text-lg font-semibold text-gray-900 dark:text-zinc-100">
                  {{ formatNumber(stats.totalBots) }}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- API 金鑰統計 -->
        <Card class="p-4">
          <CardContent class="p-0">
            <div class="flex items-center gap-3">
              <div class="p-2 bg-amber-100 dark:bg-amber-900/30 rounded-md">
                <Key class="w-4 h-4 text-amber-600 dark:text-amber-400" />
              </div>
              <div>
                <p class="text-xs text-gray-600 dark:text-zinc-400">API 金鑰</p>
                <p class="text-lg font-semibold text-gray-900 dark:text-zinc-100">
                  {{ formatNumber(stats.totalKeys) }}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 使用統計 -->
        <Card class="p-4">
          <CardContent class="p-0">
            <div class="flex items-center gap-3">
              <div class="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-md">
                <Activity class="w-4 h-4 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p class="text-xs text-gray-600 dark:text-zinc-400">本月請求</p>
                <p class="text-lg font-semibold text-gray-900 dark:text-zinc-100">
                  {{ (stats.monthlyRequests / 1000).toFixed(1) }}K
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 快速操作 -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-md bg-white dark:bg-zinc-800">
        <div class="p-4">
          <!-- 區塊標題 -->
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-md">
                <Zap class="w-4 h-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 class="text-sm font-semibold text-gray-900 dark:text-zinc-100">快速操作</h2>
                <p class="text-xs text-gray-600 dark:text-zinc-400 mt-0.5">
                  按照建議步驟順序，快速完成 AI 系統設定
                </p>
              </div>
            </div>
          </div>

          <!-- 操作按鈕網格 -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- 1. API 金鑰 -->
            <Button
              @click="router.push('/admin/ai-settings?tab=keys')"
              variant="outline"
              class="w-full h-auto p-0"
            >
              <div
                class="w-full p-4 border border-gray-200 dark:border-gray-700 rounded-md hover:border-green-300 dark:hover:border-green-600"
              >
                <div class="flex flex-col items-center space-y-3">
                  <div class="p-2 bg-green-100 dark:bg-green-900/30 rounded-md">
                    <Key class="w-5 h-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div class="text-center">
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-zinc-100">
                      API 金鑰
                    </h3>
                    <p class="text-xs text-gray-500 dark:text-zinc-400 mt-1">管理 API 憑證</p>
                  </div>
                </div>
              </div>
            </Button>

            <!-- 2. AI 模型 -->
            <Button
              @click="router.push('/admin/ai-settings?tab=models')"
              variant="outline"
              class="w-full h-auto p-0"
            >
              <div
                class="w-full p-4 border border-gray-200 dark:border-gray-700 rounded-md hover:border-orange-300 dark:hover:border-orange-600"
              >
                <div class="flex flex-col items-center space-y-3">
                  <div class="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-md">
                    <Brain class="w-5 h-5 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div class="text-center">
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-zinc-100">
                      AI 模型
                    </h3>
                    <p class="text-xs text-gray-500 dark:text-zinc-400 mt-1">配置 AI 模型</p>
                  </div>
                </div>
              </div>
            </Button>

            <!-- 3. AI 助理工作室 -->
            <Button
              @click="router.push('/ai-studio/new')"
              variant="outline"
              class="w-full h-auto p-0"
            >
              <div
                class="w-full p-4 border border-gray-200 dark:border-gray-700 rounded-md hover:border-blue-300 dark:hover:border-blue-600"
              >
                <div class="flex flex-col items-center space-y-3">
                  <div class="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-md">
                    <Bot class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div class="text-center">
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-zinc-100">
                      AI 助理工作室
                    </h3>
                    <p class="text-xs text-gray-500 dark:text-zinc-400 mt-1">創建智能助理</p>
                  </div>
                </div>
              </div>
            </Button>

            <!-- 4. 功能組態 -->
            <Button
              @click="router.push('/admin/ai-settings?tab=feature-configs')"
              variant="outline"
              class="w-full h-auto p-0"
            >
              <div
                class="w-full p-4 border border-gray-200 dark:border-gray-700 rounded-md hover:border-purple-300 dark:hover:border-purple-600"
              >
                <div class="flex flex-col items-center space-y-3">
                  <div class="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-md">
                    <Workflow class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div class="text-center">
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-zinc-100">
                      功能組態
                    </h3>
                    <p class="text-xs text-gray-500 dark:text-zinc-400 mt-1">配置系統功能</p>
                  </div>
                </div>
              </div>
            </Button>
          </div>
        </div>
      </div>

      <!-- 最近活動、使用統計、系統健康度 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <!-- 最近活動 -->
        <div
          class="border border-gray-200 dark:border-gray-700 rounded-md bg-white dark:bg-zinc-800"
        >
          <div class="p-4">
            <div class="flex items-center space-x-3 mb-4">
              <div class="p-2 bg-indigo-100 dark:bg-indigo-900/30 rounded-md">
                <Clock class="w-4 h-4 text-indigo-600 dark:text-indigo-400" />
              </div>
              <div>
                <h2 class="text-sm font-semibold text-gray-900 dark:text-zinc-100">最近活動</h2>
                <p class="text-xs text-gray-600 dark:text-zinc-400 mt-0.5">系統操作記錄</p>
              </div>
            </div>

            <div class="space-y-3">
              <div
                v-for="activity in recentActivities"
                :key="activity.id"
                class="flex items-center space-x-3 p-3 rounded-md bg-gray-50 dark:bg-zinc-700/50 border border-gray-200 dark:border-zinc-600"
              >
                <div class="flex-shrink-0">
                  <div class="p-2 rounded-md" :class="getActivityIconClass(activity.type)">
                    <component :is="getActivityIcon(activity.type)" class="w-4 h-4" />
                  </div>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-xs font-medium text-gray-900 dark:text-zinc-100 truncate">
                    {{ activity.title }}
                  </p>
                  <p class="text-xs text-gray-500 dark:text-zinc-400 mt-0.5 truncate">
                    {{ activity.description }}
                  </p>
                </div>
                <div class="flex-shrink-0">
                  <span
                    class="text-xs text-gray-500 dark:text-zinc-400 bg-gray-100 dark:bg-zinc-700 px-2 py-1 rounded"
                  >
                    {{ formatTime(activity.timestamp) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 查看更多 -->
            <div class="mt-4 pt-3 border-t border-gray-200 dark:border-zinc-700">
              <Button variant="outline" size="sm" class="w-full text-xs">
                <Clock class="w-3 h-3 mr-1" />
                查看完整活動記錄
              </Button>
            </div>
          </div>
        </div>

        <!-- 使用統計 -->
        <div
          class="border border-gray-200 dark:border-gray-700 rounded-md bg-white dark:bg-zinc-800"
        >
          <div class="p-4">
            <!-- 區塊標題 -->
            <div class="flex items-center space-x-3 mb-4">
              <div class="p-2 bg-green-100 dark:bg-green-900/30 rounded-md">
                <BarChart class="w-4 h-4 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h2 class="text-sm font-semibold text-gray-900 dark:text-zinc-100">使用統計</h2>
                <p class="text-xs text-gray-600 dark:text-zinc-400 mt-0.5">系統使用情況概覽</p>
              </div>
            </div>

            <!-- 統計數據 -->
            <div class="space-y-3">
              <!-- API 呼叫 -->
              <div
                class="flex items-center justify-between p-3 rounded-md bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800"
              >
                <div class="flex items-center space-x-2">
                  <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span class="text-xs font-medium text-gray-700 dark:text-zinc-300">API 呼叫</span>
                </div>
                <span class="text-sm font-bold text-gray-900 dark:text-zinc-100">{{
                  formatNumber(stats.apiCalls)
                }}</span>
              </div>

              <!-- Token 使用 -->
              <div
                class="flex items-center justify-between p-3 rounded-md bg-purple-50 dark:bg-purple-900/20 border border-purple-100 dark:border-purple-800"
              >
                <div class="flex items-center space-x-2">
                  <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span class="text-xs font-medium text-gray-700 dark:text-zinc-300"
                    >Token 使用</span
                  >
                </div>
                <span class="text-sm font-bold text-gray-900 dark:text-zinc-100">{{
                  formatNumber(stats.tokenUsage)
                }}</span>
              </div>

              <!-- 工作流程執行 -->
              <div
                class="flex items-center justify-between p-3 rounded-md bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800"
              >
                <div class="flex items-center space-x-2">
                  <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span class="text-xs font-medium text-gray-700 dark:text-zinc-300"
                    >工作流程執行</span
                  >
                </div>
                <span class="text-sm font-bold text-gray-900 dark:text-zinc-100">{{
                  stats.workflowExecutions
                }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 系統健康度 -->
        <div
          class="border border-gray-200 dark:border-gray-700 rounded-md bg-white dark:bg-zinc-800"
        >
          <div class="p-4">
            <div class="flex items-center space-x-3 mb-4">
              <div class="p-2 bg-green-100 dark:bg-green-900/30 rounded-md">
                <Shield class="w-4 h-4 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h2 class="text-sm font-semibold text-gray-900 dark:text-zinc-100">系統健康度</h2>
                <p class="text-xs text-gray-600 dark:text-zinc-400 mt-0.5">即時監控狀態</p>
              </div>
            </div>

            <div class="space-y-3">
              <div
                class="p-3 rounded-md bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span class="text-xs font-medium text-gray-700 dark:text-zinc-300"
                      >API 響應時間</span
                    >
                  </div>
                  <span class="text-sm font-bold text-green-600 dark:text-green-400"
                    >{{ healthMetrics.responseTime }}ms</span
                  >
                </div>
              </div>

              <div
                class="p-3 rounded-md bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span class="text-xs font-medium text-gray-700 dark:text-zinc-300">錯誤率</span>
                  </div>
                  <span class="text-sm font-bold text-yellow-600 dark:text-yellow-400"
                    >{{ healthMetrics.errorRate }}%</span
                  >
                </div>
              </div>

              <div
                class="p-3 rounded-md bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span class="text-xs font-medium text-gray-700 dark:text-zinc-300"
                      >系統可用性</span
                    >
                  </div>
                  <span class="text-sm font-bold text-blue-600 dark:text-blue-400"
                    >{{ healthMetrics.uptime }}%</span
                  >
                </div>
              </div>
            </div>

            <!-- 整體狀態 -->
            <div
              class="mt-4 p-3 rounded-md bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700"
            >
              <div class="flex items-center justify-center space-x-2">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <span class="text-xs font-bold text-green-700 dark:text-green-300"
                  >系統運行正常</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { useNotification } from '@/composables/shared/useNotification';
import {
  RefreshCw,
  Settings,
  CheckCircle,
  Bot,
  Workflow,
  Activity,
  Zap,
  Key,
  Brain,
  BarChart,
  Clock,
  Shield,
  AlertCircle,
  PlayCircle,
  X,
  TrendingUp,
} from 'lucide-vue-next';

const router = useRouter();
const notification = useNotification();
const loading = ref(false);
const showHelp = ref(false);

// 統計數據
const stats = reactive({
  totalBots: 8,
  activeBots: 6,
  totalWorkflows: 12,
  monthlyExecutions: 1247,
  apiCalls: 15632,
  tokenUsage: 245000,
  tokenLimit: 500000,
  workflowExecutions: 89,
  totalKeys: 100,
  activeKeys: 40,
  monthlyRequests: 20000,
});

// 最近活動
const recentActivities = ref([
  {
    id: 1,
    type: 'workflow',
    title: '客戶服務工作流程執行成功',
    description: '處理了 15 個客戶詢問',
    timestamp: new Date(Date.now() - 5 * 60000),
  },
  {
    id: 2,
    type: 'bot',
    title: '新增 AI 助理「產品推薦」',
    description: '配置完成並開始運行',
    timestamp: new Date(Date.now() - 25 * 60000),
  },
  {
    id: 3,
    type: 'error',
    title: 'API 金鑰即將到期',
    description: 'OpenAI API 金鑰將於 3 天後到期',
    timestamp: new Date(Date.now() - 2 * 3600000),
  },
  {
    id: 4,
    type: 'workflow',
    title: '數據分析工作流程完成',
    description: '生成了本週業務報告',
    timestamp: new Date(Date.now() - 4 * 3600000),
  },
]);

// 健康度指標
const healthMetrics = reactive({
  responseTime: 156,
  errorRate: 0.2,
  uptime: 99.8,
});

// 工具函數
const formatNumber = (num: number) => {
  return new Intl.NumberFormat('zh-TW').format(num);
};

const formatTime = (date: Date) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);

  if (minutes < 60) {
    return `${minutes} 分鐘前`;
  } else if (hours < 24) {
    return `${hours} 小時前`;
  } else {
    return date.toLocaleDateString('zh-TW');
  }
};

// 刷新數據
const refreshData = async () => {
  loading.value = true;
  try {
    // 模擬 API 調用
    await new Promise((resolve) => setTimeout(resolve, 1000));
    notification.toast.success('刷新成功', '數據已更新');
  } catch (error) {
    notification.toast.error('刷新失敗', '無法載入最新數據');
  } finally {
    loading.value = false;
  }
};

// 活動圖示相關函數
const getActivityIcon = (type: string) => {
  switch (type) {
    case 'workflow':
      return PlayCircle;
    case 'bot':
      return Bot;
    case 'error':
      return AlertCircle;
    default:
      return Activity;
  }
};

const getActivityIconClass = (type: string) => {
  switch (type) {
    case 'workflow':
      return 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400 border border-emerald-200 dark:border-emerald-800';
    case 'bot':
      return 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 border border-blue-200 dark:border-blue-800';
    case 'error':
      return 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 border border-red-200 dark:border-red-800';
    default:
      return 'bg-zinc-100 dark:bg-zinc-700 text-zinc-600 dark:text-zinc-400 border border-zinc-200 dark:border-zinc-600';
  }
};

onMounted(() => {
  // 載入初始數據
  refreshData();
});
</script>
