<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useNotification } from '@/composables/shared/useNotification';
import { useAIAgentManager } from '@/composables/admin/ai/agents/useAIAgentManager';
import { useAIAgentChat } from '@/composables/admin/ai/agents/useAIAgentChat';
import { useAiFeatureConfig } from '@/composables/admin/ai/features/useAiFeatureConfig';
import { useAIValidation } from '@/composables/admin/ai/shared/useAIValidation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft,
  Save,
  Sparkles,
  Settings,
  AlertTriangle,
  Loader2,
  Send,
  MessageSquare,
  Bot,
  RotateCcw,
} from 'lucide-vue-next';
import SystemPromptInput from '@/components/admin/ai/shared/SystemPromptInput.vue';
import { AiBotResponseFormat, AiBotScope, type AiBotProviderType } from '@/types/models/ai.model';
import { AiBotsService } from '@/services/admin/ai/ai-agents.service';

// 路由和狀態
const router = useRouter();
const route = useRoute();
const notification = useNotification();

// Tab 狀態
const activeTab = ref('settings');

// 對話模式相關狀態
const builderMessage = ref('');
const isBuilderSending = ref(false);
const builderMessages = ref<
  Array<{ role: 'user' | 'assistant'; content: string; timestamp: Date }>
>([]);

// 使用 composables
const { systemFeatureDefinitions, fetchSystemFeatureDefinitions } = useAiFeatureConfig();

const manager = useAIAgentManager(systemFeatureDefinitions);
const {
  selectedBot,
  editData,
  availableModels,
  availableKeys,
  getBotScenesByScope,
  initialize: initializeManager,
  selectBot: selectBotFromManager,
  createBot: createBotFromManager,
  saveChanges,
  isNew: isNewBotFromManager,
  botList,
} = manager;

// 聊天功能
const chatComposable = useAIAgentChat(selectedBot, editData, notification);
const {
  testMessage,
  isSending,
  chatMessages,
  aiTyping,
  typingText,
  chatEndRef,
  sendMessage,
  clearChat,
  renderMarkdown,
} = chatComposable;

// 表單狀態
const isLoading = ref(false);
const isSaving = ref(false);
const isEditing = computed(() => !!route.params.id && route.params.id !== 'new');

// 回應格式選項
const responseFormatOptions = [
  { value: AiBotResponseFormat.TEXT, label: '文字' },
  { value: AiBotResponseFormat.JSON_OBJECT, label: 'JSON 物件' },
];

// AI 金鑰驗證邏輯
const availableKeysRef = computed(() => availableKeys.value || []);
const selectedKeyIdRef = computed(() => editData.value?.key_id);

const {
  hasAnyValidKey,
  validateChatTestPrerequisites,
  showKeySetupNotification,
  getOperationReadiness,
  isBotKeyValid,
} = useAIValidation(availableKeysRef, selectedKeyIdRef);

// 檢查對話測試狀態
const chatTestStatus = computed(() => {
  if (!editData.value || !availableKeys.value) {
    return {
      isReady: false,
      message: '載入中...',
    };
  }

  const isValid = isBotKeyValid(editData.value);
  return {
    isReady: isValid,
    message: isValid ? '可以開始測試' : '此機器人尚未配置 API 金鑰，無法進行對話測試',
  };
});

const canStartChat = computed(() => chatTestStatus.value.isReady);

// 系統提示詞相關計算屬性
const systemPromptApiUrl = computed(() => {
  if (!editData.value?.key_id) return undefined;
  const key = availableKeys.value?.find((k) => k && k.id === editData.value.key_id);
  return key?.api_url || undefined;
});

const systemPromptValue = computed(() => editData.value?.system_prompt);
const systemPromptScene = computed(() => editData.value?.scene);
const systemPromptProviderType = computed(
  () => editData.value?.provider_type as AiBotProviderType | undefined,
);

const systemPromptApiKey = computed(() => {
  if (!editData.value?.key_id || !availableKeys.value) return undefined;
  const key = availableKeys.value.find((k) => k && k.id === editData.value.key_id);
  return (key?.api_key as string | undefined) || undefined;
});

// 當前場景標籤
const pageCurrentSceneLabel = computed(() => {
  if (!editData.value?.scene) return '通用';
  const scenes = getBotScenesByScope(editData.value.scope);
  const scene = scenes.find((s) => s.value === editData.value.scene);
  return scene?.label || editData.value.scene;
});

// 預覽重新整理狀態
const isRefreshing = ref(false);

// 方法
const goBack = () => {
  // 返回到 AI 管理中心
  router.push({ name: 'admin-ai-dashboard' });
};

const handleSave = async () => {
  try {
    isSaving.value = true;
    await saveChanges();
    notification.toast.success(
      isEditing.value ? 'AI 助理更新成功' : 'AI 助理創建成功',
      '操作已完成',
    );
  } catch (error) {
    console.error('Save failed:', error);
    notification.toast.error('保存失敗', '請稍後重試');
  } finally {
    isSaving.value = false;
  }
};

// 處理Enter鍵發送訊息
const handleEnterSend = () => {
  if (!canStartChat.value) {
    validateChatTestPrerequisites(editData.value);
    return;
  }

  if (!isSending.value && testMessage.value?.trim()) {
    sendMessage();
  }
};

// 處理建構器對話發送
const handleBuilderSend = async () => {
  if (!builderMessage.value.trim() || isBuilderSending.value) return;

  const userMessage = {
    role: 'user' as const,
    content: builderMessage.value,
    timestamp: new Date(),
  };

  builderMessages.value.push(userMessage);
  const currentMessage = builderMessage.value;
  builderMessage.value = '';
  isBuilderSending.value = true;

  try {
    // TODO: 這裡將來要串接後端 LLM API 來協助建立機器人
    // 目前先模擬回應
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const assistantMessage = {
      role: 'assistant' as const,
      content: `我收到您的需求：「${currentMessage}」\n\n我將協助您建立這個 AI 助理。請告訴我更多細節，例如：\n- 這個助理的主要功能是什麼？\n- 目標用戶是誰？\n- 需要什麼樣的回應風格？`,
      timestamp: new Date(),
    };

    builderMessages.value.push(assistantMessage);
  } catch (error) {
    console.error('Builder chat error:', error);
    notification.toast.error('對話失敗', '請稍後重試');
  } finally {
    isBuilderSending.value = false;
  }
};

// 處理建構器 Enter 鍵
const handleBuilderEnter = () => {
  if (!isBuilderSending.value && builderMessage.value?.trim()) {
    handleBuilderSend();
  }
};

// 導航到金鑰設定頁面
const goToKeySettings = () => {
  router.push('/admin/ai-settings?tab=keys');
};

// 處理預覽重新整理
const handlePreviewRefresh = async () => {
  isRefreshing.value = true;
  try {
    clearChat();
    // 模擬重新整理動畫
    await new Promise((resolve) => setTimeout(resolve, 800));
  } finally {
    isRefreshing.value = false;
  }
};

// 生命週期
onMounted(async () => {
  await fetchSystemFeatureDefinitions();
  await initializeManager();

  const botId = route.params.id as string;

  if (botId && botId !== 'new') {
    let existingBot = botList.value.find((b) => b.id === botId);

    if (existingBot) {
      await selectBotFromManager(botId);
    } else {
      try {
        const botFromApi = await AiBotsService.findOne(botId);
        botList.value.push(botFromApi);
        await selectBotFromManager(botId);
      } catch (error) {
        console.error('載入指定機器人失敗:', error);
        notification.toast.error(`AI 助手 (ID: ${botId}) 不存在或無法載入。`);
        // 返回到 AI 管理中心
        router.push({ name: 'admin-ai-dashboard' });
      }
    }
  } else if (botId === 'new') {
    await createBotFromManager(AiBotScope.WORKSPACE);
  } else {
    notification.toast.warning('未指定 AI 助手，將建立一個新的。');
    await createBotFromManager(AiBotScope.WORKSPACE);
    if (!editData.value?.name) {
      // 返回到 AI 管理中心
      router.push({ name: 'admin-ai-dashboard' });
    }
  }
});
</script>

<template>
  <div class="min-h-screen bg-gray-50 dark:bg-zinc-900">
    <!-- 顶部导航 -->
    <header class="sticky top-0 z-50 border-b bg-white dark:bg-zinc-800 shadow-sm">
      <div class="max-w-none mx-auto px-6 h-16 flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <Button variant="ghost" size="sm" @click="goBack" class="space-x-2">
            <ArrowLeft class="w-4 h-4" />
            <span>返回</span>
          </Button>
          <div class="h-6 w-px bg-gray-200 dark:bg-zinc-600" />
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-md">
              <Sparkles class="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h1 class="text-lg font-semibold text-gray-900 dark:text-zinc-100">AI 助理工作室</h1>
              <p class="text-xs text-gray-600 dark:text-zinc-400">配置並即時測試您的 AI 助理</p>
            </div>
          </div>
        </div>

        <Button
          @click="handleSave"
          :disabled="isSaving || isLoading || !selectedBot?.id"
          size="sm"
          class="space-x-2 rounded-full"
        >
          <Save class="w-4 h-4" />
          <span v-if="isSaving">保存中...</span>
          <span v-else>儲存</span>
          <Loader2 v-if="isSaving" class="w-4 h-4 animate-spin" />
        </Button>
      </div>
    </header>

    <!-- 主要内容 - 左右對半分配 -->
    <main class="h-[calc(100vh-4rem)] flex">
      <!-- 左侧面板 - 50% 寬度，含 Tab 切換 -->
      <div
        class="w-1/2 flex-shrink-0 border-r border-gray-200 dark:border-zinc-700 bg-white dark:bg-zinc-800 flex flex-col"
      >
        <!-- Tab 導航 - 方形設計，無圖示，大字體 -->
        <div class="flex-shrink-0 p-4">
          <Tabs v-model="activeTab" class="w-1/2 mx-auto">
            <TabsList class="grid w-full grid-cols-2 bg-gray-50 dark:bg-zinc-800 rounded-lg p-1">
              <TabsTrigger value="conversation" class="rounded-md text-base font-medium">
                建構
              </TabsTrigger>
              <TabsTrigger value="settings" class="rounded-md text-base font-medium">
                設定
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <!-- Tab 內容區域 -->
        <div class="flex-1 overflow-hidden">
          <Tabs v-model="activeTab" class="h-full">
            <!-- 對話模式 -->
            <TabsContent value="conversation" class="h-full m-0 p-0">
              <div class="h-full flex flex-col">
                <!-- 對話內容 -->
                <div class="flex-1 overflow-y-auto p-6">
                  <div
                    v-if="builderMessages.length === 0"
                    class="h-full flex items-center justify-center"
                  >
                    <div class="text-center space-y-6 max-w-md">
                      <div class="p-4 bg-purple-100 dark:bg-purple-900/30 rounded-xl mx-auto w-fit">
                        <Bot class="w-8 h-8 text-purple-600 dark:text-purple-400" />
                      </div>
                      <div>
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-zinc-100 mb-2">
                          開始建立您的 AI 助理
                        </h4>
                        <p class="text-sm text-gray-600 dark:text-zinc-400">
                          告訴我您想要什麼樣的 AI 助理，我會協助您一步步建立。例如：
                        </p>
                        <ul class="text-sm text-gray-500 dark:text-zinc-500 mt-3 space-y-1">
                          <li>• "我需要一個客服助理"</li>
                          <li>• "幫我做一個程式碼審查助理"</li>
                          <li>• "建立一個寫作輔助工具"</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <!-- 對話訊息 -->
                  <div v-else class="space-y-4 pb-24">
                    <div
                      v-for="(msg, index) in builderMessages"
                      :key="index"
                      :class="['flex', msg.role === 'user' ? 'justify-end' : 'justify-start']"
                    >
                      <div
                        class="max-w-[85%] p-4 rounded-2xl shadow-sm"
                        :class="
                          msg.role === 'user'
                            ? 'bg-blue-600 text-white rounded-br-md'
                            : 'bg-white dark:bg-zinc-700 text-gray-900 dark:text-zinc-100 rounded-bl-md border border-gray-200 dark:border-zinc-600'
                        "
                      >
                        <div class="text-sm break-words leading-relaxed whitespace-pre-wrap">
                          {{ msg.content }}
                        </div>
                        <div class="text-xs opacity-70 mt-2">
                          {{ msg.timestamp.toLocaleTimeString() }}
                        </div>
                      </div>
                    </div>

                    <!-- 建構師回覆等待動畫 -->
                    <div v-if="isBuilderSending" class="flex justify-start">
                      <div
                        class="max-w-[85%] p-4 rounded-2xl rounded-bl-md bg-white dark:bg-zinc-700 border border-gray-200 dark:border-zinc-600 shadow-sm"
                      >
                        <div class="flex items-center space-x-2">
                          <div class="flex space-x-1">
                            <div
                              class="w-2 h-2 rounded-full bg-purple-600 animate-pulse"
                              style="animation-delay: 0ms"
                            ></div>
                            <div
                              class="w-2 h-2 rounded-full bg-purple-600 animate-pulse"
                              style="animation-delay: 300ms"
                            ></div>
                            <div
                              class="w-2 h-2 rounded-full bg-purple-600 animate-pulse"
                              style="animation-delay: 600ms"
                            ></div>
                          </div>
                          <span class="text-xs text-gray-500">建構師正在思考...</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 對話輸入框 - 膠囊形狀 -->
                <div class="flex-shrink-0 p-6">
                  <div
                    class="bg-white dark:bg-zinc-800 rounded-full border border-gray-200 dark:border-zinc-700 p-4"
                  >
                    <div class="flex items-end space-x-3">
                      <Textarea
                        v-model="builderMessage"
                        placeholder="描述您想要的 AI 助理..."
                        rows="1"
                        class="flex-1 resize-none min-h-[40px] max-h-[120px] text-sm border-0 bg-transparent focus:ring-0 placeholder:text-gray-400"
                        @keydown.enter.exact.prevent="handleBuilderEnter"
                      />
                      <Button
                        :disabled="isBuilderSending || !builderMessage.trim()"
                        @click="handleBuilderSend"
                        size="sm"
                        class="flex-shrink-0 rounded-full w-10 h-10 p-0"
                      >
                        <Send v-if="!isBuilderSending" class="w-4 h-4" />
                        <Loader2 v-else class="w-4 h-4 animate-spin" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <!-- 設定模式 -->
            <TabsContent value="settings" class="h-full m-0 p-0">
              <div class="h-full overflow-y-auto">
                <div class="p-6 space-y-6" v-if="editData">
                  <!-- 名称 -->
                  <div class="space-y-2">
                    <Label class="text-sm font-medium text-gray-900 dark:text-zinc-100">名稱</Label>
                    <Input
                      v-model="editData.name"
                      @blur="saveChanges"
                      placeholder="輸入 AI 助理名稱"
                      class="h-10 text-sm border border-gray-200 dark:border-zinc-700 focus:border-blue-500 focus:ring-0 shadow-none"
                    />
                  </div>

                  <!-- 說明 -->
                  <div class="space-y-2">
                    <Label class="text-sm font-medium text-gray-900 dark:text-zinc-100">說明</Label>
                    <Input
                      v-model="editData.description"
                      @blur="saveChanges"
                      placeholder="說明此 AI 助理的用途"
                      class="h-10 text-sm border border-gray-200 dark:border-zinc-700 focus:border-blue-500 focus:ring-0 shadow-none"
                    />
                  </div>

                  <!-- 系统提示词 -->
                  <div class="space-y-2">
                    <Label class="text-sm font-medium text-gray-900 dark:text-zinc-100"
                      >系統提示詞</Label
                    >
                    <div
                      class="border border-gray-200 dark:border-zinc-700 rounded-lg p-4 bg-gray-50 dark:bg-zinc-800"
                    >
                      <Textarea
                        v-model="editData.system_prompt"
                        @blur="saveChanges"
                        :rows="8"
                        placeholder="定義 AI 助理的角色、行為和指令..."
                        class="w-full border-0 bg-transparent focus:ring-0 text-sm resize-none shadow-none"
                      />
                    </div>
                  </div>

                  <!-- 模型 -->
                  <div class="space-y-2">
                    <Label class="text-sm font-medium text-gray-900 dark:text-zinc-100"
                      >AI 模型</Label
                    >
                    <Select v-model="editData.model_id" @update:modelValue="saveChanges">
                      <SelectTrigger
                        class="h-10 text-sm border border-gray-200 dark:border-zinc-700 focus:border-blue-500 focus:ring-0 shadow-none"
                      >
                        <SelectValue placeholder="請選擇模型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem
                          v-for="m in availableModels.filter((m) => m.is_enabled)"
                          :key="m.id"
                          :value="m.id"
                        >
                          <div class="py-1">
                            <div class="font-medium text-sm">{{ m.name }}</div>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <!-- 金钥 -->
                  <div class="space-y-2">
                    <Label class="text-sm font-medium text-gray-900 dark:text-zinc-100"
                      >API 金鑰</Label
                    >
                    <Select v-model="editData.key_id" @update:modelValue="saveChanges">
                      <SelectTrigger
                        class="h-10 text-sm border border-gray-200 dark:border-zinc-700 focus:border-blue-500 focus:ring-0 shadow-none"
                      >
                        <SelectValue placeholder="請選擇金鑰" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem v-for="k in availableKeys" :key="k.id" :value="k.id">
                          <div class="py-1">
                            <div class="font-medium text-sm">{{ k.name }}</div>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <!-- 回复格式 -->
                  <div class="space-y-2">
                    <Label class="text-sm font-medium text-gray-900 dark:text-zinc-100"
                      >回覆格式</Label
                    >
                    <Select v-model="editData.response_format" @update:modelValue="saveChanges">
                      <SelectTrigger
                        class="h-10 text-sm border border-gray-200 dark:border-zinc-700 focus:border-blue-500 focus:ring-0 shadow-none"
                      >
                        <SelectValue placeholder="請選擇格式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem
                          v-for="opt in responseFormatOptions"
                          :key="opt.value"
                          :value="opt.value"
                        >
                          <div class="py-1">
                            <div class="font-medium text-sm">{{ opt.label }}</div>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <!-- 温度滑杆 -->
                  <div class="space-y-3">
                    <div class="flex items-center justify-between">
                      <Label class="text-sm font-medium text-gray-900 dark:text-zinc-100"
                        >溫度</Label
                      >
                      <Badge variant="outline" class="text-xs px-2 py-1">
                        {{ editData.temperature ?? 0.7 }}
                      </Badge>
                    </div>
                    <div class="space-y-2">
                      <input
                        type="range"
                        min="0"
                        max="1"
                        step="0.1"
                        v-model.number="editData.temperature"
                        @change="saveChanges"
                        class="w-full accent-blue-600"
                      />
                      <div class="flex justify-between text-xs text-gray-500">
                        <span>精確</span>
                        <span>平衡</span>
                        <span>創意</span>
                      </div>
                      <p class="text-xs text-gray-600 dark:text-zinc-400">
                        溫度越低回答越精確，溫度越高回答越有創意
                      </p>
                    </div>
                  </div>

                  <!-- 最大 Token -->
                  <div class="space-y-2">
                    <Label class="text-sm font-medium text-gray-900 dark:text-zinc-100"
                      >最大 Token</Label
                    >
                    <Input
                      v-model.number="editData.max_tokens"
                      @blur="saveChanges"
                      type="number"
                      min="1"
                      max="32000"
                      placeholder="設定最大輸出 Token 數"
                      class="h-10 text-sm border border-gray-200 dark:border-zinc-700 focus:border-blue-500 focus:ring-0 shadow-none"
                    />
                    <p class="text-xs text-gray-600 dark:text-zinc-400">控制 AI 回應的最大長度</p>
                  </div>

                  <!-- 隐藏的场景选择器 -->
                  <div class="hidden">
                    <Select v-model="editData.scene" @update:modelValue="saveChanges">
                      <SelectTrigger><SelectValue /></SelectTrigger>
                      <SelectContent>
                        <SelectItem
                          v-for="s in getBotScenesByScope(editData.scope)"
                          :key="s.value"
                          :value="s.value"
                        />
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div v-else class="p-6">
                  <div class="flex items-center justify-center h-32">
                    <p class="text-sm text-gray-600 dark:text-zinc-400">
                      請先選擇或建立一個 AI 助手。
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      <!-- 右侧对话区 - 50% 寬度，測試區域 -->
      <div class="w-1/2 flex-shrink-0 flex flex-col bg-gray-50 dark:bg-zinc-900 relative">
        <!-- 对话内容区域 - 無標題列 -->
        <div class="flex-1 overflow-y-auto p-6">
          <!-- 預覽按鈕 - 帶轉圈動畫 -->
          <div class="flex justify-end mb-6">
            <Button
              @click="handlePreviewRefresh"
              variant="ghost"
              size="sm"
              :disabled="isRefreshing"
              class="text-sm space-x-2"
            >
              <RotateCcw class="w-4 h-4" :class="{ 'animate-spin': isRefreshing }" />
              <span>預覽</span>
            </Button>
          </div>

          <!-- 金钥验证引导 -->
          <div v-if="!canStartChat" class="h-full flex items-center justify-center">
            <div
              class="text-center space-y-6 p-6 border border-dashed border-gray-300 dark:border-zinc-600 rounded-lg bg-white dark:bg-zinc-800"
            >
              <div class="p-3 bg-amber-100 dark:bg-amber-900/30 rounded-lg mx-auto w-fit">
                <AlertTriangle class="w-6 h-6 text-amber-600 dark:text-amber-400" />
              </div>
              <div class="text-lg font-semibold text-gray-900 dark:text-zinc-100">
                需要設定 API 金鑰
              </div>
              <p class="text-sm text-gray-600 dark:text-zinc-400 max-w-md">
                此機器人尚未配置有效的 API 金鑰，無法進行對話測試。請先在左側面板選擇有效的金鑰。
              </p>
              <Button @click="goToKeySettings" size="sm"> 前往金鑰設定 </Button>
            </div>
          </div>

          <!-- 對話訊息列表 -->
          <template v-else>
            <div class="space-y-4 pb-24">
              <div
                v-for="(msg, index) in chatMessages"
                :key="index"
                :class="['flex', msg.role === 'user' ? 'justify-end' : 'justify-start']"
              >
                <div
                  class="max-w-[70%] p-4 rounded-2xl shadow-sm"
                  :class="
                    msg.role === 'user'
                      ? 'bg-blue-600 text-white rounded-br-md'
                      : 'bg-white dark:bg-zinc-700 text-gray-900 dark:text-zinc-100 rounded-bl-md border border-gray-200 dark:border-zinc-600'
                  "
                >
                  <div
                    class="text-sm break-words leading-relaxed"
                    v-html="renderMarkdown(msg.content)"
                  ></div>
                </div>
              </div>

              <!-- AI 回复等待动画 -->
              <div v-if="aiTyping" class="flex justify-start">
                <div
                  class="max-w-[70%] p-4 rounded-2xl rounded-bl-md shadow-sm bg-white dark:bg-zinc-700 text-gray-900 dark:text-zinc-100 border border-gray-200 dark:border-zinc-600"
                >
                  <!-- 打字中效果 -->
                  <div v-if="typingText" class="text-sm break-words leading-relaxed">
                    <div v-html="renderMarkdown(typingText)"></div>
                    <span class="inline-block w-1 h-4 ml-1 bg-blue-600 animate-pulse"></span>
                  </div>
                  <!-- 思考中效果 -->
                  <div v-else class="flex items-center space-x-1">
                    <div class="flex space-x-1">
                      <div
                        class="w-2 h-2 rounded-full bg-blue-600 animate-pulse"
                        style="animation-delay: 0ms"
                      ></div>
                      <div
                        class="w-2 h-2 rounded-full bg-blue-600 animate-pulse"
                        style="animation-delay: 300ms"
                      ></div>
                      <div
                        class="w-2 h-2 rounded-full bg-blue-600 animate-pulse"
                        style="animation-delay: 600ms"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div ref="chatEndRef"></div>
          </template>
        </div>

        <!-- 漂浮式输入框 - 膠囊形狀，圓形發送按鈕 -->
        <div
          class="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-gray-50 via-gray-50 to-transparent dark:from-zinc-900 dark:via-zinc-900 dark:to-transparent"
        >
          <div
            class="bg-white dark:bg-zinc-800 rounded-full border border-gray-200 dark:border-zinc-700 p-4"
          >
            <div class="flex items-end space-x-3">
              <Textarea
                v-model="testMessage"
                :placeholder="
                  canStartChat ? '輸入任何內容測試 AI 助理...' : '請先設定有效的 API 金鑰'
                "
                rows="1"
                class="flex-1 resize-none min-h-[40px] max-h-[120px] text-sm border-0 bg-transparent focus:ring-0 placeholder:text-gray-400"
                :disabled="!canStartChat"
                @keydown.enter.exact.prevent="handleEnterSend"
              />
              <Button
                :disabled="isSending || !testMessage || !canStartChat"
                @click="handleEnterSend"
                size="sm"
                class="flex-shrink-0 rounded-full w-10 h-10 p-0"
              >
                <Send v-if="!isSending" class="w-4 h-4" />
                <Loader2 v-else class="w-4 h-4 animate-spin" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped>
/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

/* 基础动画 */
.animate-pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
}

/* 漸層背景確保輸入框清晰可見 */
.bg-gradient-to-t {
  pointer-events: none;
}

.bg-gradient-to-t > * {
  pointer-events: auto;
}
</style>
