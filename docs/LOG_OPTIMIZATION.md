# 日誌系統優化指南

## 概述

本項目已經實施了全面的日誌優化，包括前端和後端的統一日誌管理，旨在：

- 減少生產環境的噪音日誌
- 提供可配置的日誌級別
- 統一日誌格式和輸出
- 改善開發體驗

## 前端日誌配置

### 環境變數配置

在 `.env` 文件中添加以下配置：

```bash
# 日誌級別 (debug, info, warn, error, none)
VITE_LOG_LEVEL=debug

# 功能模組日誌開關
VITE_ENABLE_ROUTER_LOGS=true      # 路由守衛日誌
VITE_ENABLE_AUTH_LOGS=true        # 認證相關日誌
VITE_ENABLE_PERMISSION_LOGS=false # 權限檢查日誌
VITE_ENABLE_API_LOGS=true         # API 請求日誌
VITE_ENABLE_COMPONENT_LOGS=false  # 組件生命週期日誌
```

### 使用統一日誌器

```typescript
import { loggers } from '@/config/debug.config';

const logger = loggers.auth; // 或其他預定義的日誌器

// 不同級別的日誌
logger.debug('調試信息', { data: 'some data' });
logger.info('一般信息');
logger.warn('警告信息');
logger.error('錯誤信息', error);
```

### 預定義的日誌器

- `loggers.auth` - 認證相關
- `loggers.router` - 路由守衛
- `loggers.permission` - 權限檢查
- `loggers.api` - API 請求
- `loggers.component` - 組件相關
- `loggers.main` - 主應用

## 後端日誌配置

### 環境變數配置

```bash
# 應用環境
NODE_ENV=development # development, staging, production

# 自定義日誌級別（可選）
LOG_LEVEL=debug # error, warn, log, debug, verbose
```

### 日誌級別對應

| 環境 | 默認日誌級別 |
|------|-------------|
| development | error, warn, log, debug, verbose |
| staging | error, warn, log, debug |
| production | error, warn, log |

## 環境別日誌行為

### 開發環境 (development)
- 顯示所有級別的日誌
- 啟用 Swagger 文檔
- 詳細的錯誤信息
- 完整的啟動信息

### 測試環境 (staging)
- 顯示 error, warn, log, debug
- 啟用 Swagger 文檔
- 適中的日誌詳細度

### 生產環境 (production)
- 僅顯示 error, warn, log
- 禁用 Swagger 文檔
- 最小化的日誌輸出
- 簡潔的啟動信息

## 日誌格式

### 前端日誌格式
```
[時間] [分類] 級別: 訊息
[14:30:25] [Auth] DEBUG: 用戶登入成功
```

### 後端日誌格式
使用 NestJS 內建日誌格式：
```
[Nest] 12345  - 2024/01/01 下午2:30:25   LOG [Bootstrap] 應用程式已啟動
```

## 常見問題

### Q: 如何臨時啟用詳細日誌？
A: 設置環境變數 `VITE_LOG_LEVEL=debug` (前端) 或 `LOG_LEVEL=debug` (後端)

### Q: 如何完全禁用某個模組的日誌？
A: 設置對應的環境變數為 `false`，例如 `VITE_ENABLE_PERMISSION_LOGS=false`

### Q: 生產環境如何查看錯誤日誌？
A: 生產環境仍會顯示 error 和 warn 級別的日誌，可以通過日誌收集系統查看

### Q: 如何添加新的日誌分類？
A: 在 `debug.config.ts` 中的 `loggers` 對象添加新的日誌器：
```typescript
export const loggers = {
  // ... existing loggers
  newModule: createLogger('NewModule'),
} as const;
```

## 效果比較

### 優化前
```
🔒 認證守衛開始檢查: /admin/dashboard
🔍 認證狀態檢查: {processedIsAuthenticated: true, hasUser: true, ...}
👤 認證狀態: {isAuthenticated: true, hasValidToken: true, ...}
📋 路由要求: {requiresAuth: true, requiresGuest: false, ...}
🚪 訪問檢查: {accessible: true}
🔧 檢查用戶設置狀態...
⚙️ 設置狀態: {needsSetup: false}
✅ 認證守衛檢查通過
🔐 權限守衛開始檢查: /admin/dashboard
👤 當前用戶: {email: '<EMAIL>', role: 'SUPER_ADMIN', ...}
🔥 Super Admin 檢測到，跳過所有權限檢查
```

### 優化後（開發環境）
```
[14:30:25] [Router] DEBUG: 認證守衛檢查路由: /admin/dashboard
[14:30:25] [Auth] DEBUG: 認證狀態: {isAuthenticated: true, userId: "user123"}
[14:30:25] [Permission] DEBUG: Super Admin 檢測到，跳過所有權限檢查
```

### 優化後（生產環境）
```
僅在發生錯誤時才輸出日誌
```

## 遷移指南

如果您的代碼中還在使用 `console.log`，建議遷移到統一的日誌系統：

```typescript
// 舊方式
console.log('用戶登入成功');

// 新方式
import { loggers } from '@/config/debug.config';
loggers.auth.info('用戶登入成功');
``` 