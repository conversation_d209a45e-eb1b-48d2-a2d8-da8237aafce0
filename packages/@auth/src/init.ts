import { useAuthStore } from './store/auth.store'
import { createHttpService } from './services/http.service'
import { createAuthService } from './services/auth.service'
import type { AuthConfig } from './types/auth.types'

interface InitAuthReturnType {
  authStore: ReturnType<typeof useAuthStore>
  authService: ReturnType<typeof createAuthService>
}

/**
 * Initializes the authentication module.
 * This function sets up the required services and initializes the auth store.
 *
 * @param {AuthConfig} config - The configuration for the authentication module.
 * @returns An object containing the initialized authStore and authService.
 */
export async function initAuth(config: AuthConfig): Promise<InitAuthReturnType> {
  // 1. Create the low-level HTTP service
  const httpService = createHttpService(config)

  // 2. Create the authentication service, which uses the HTTP service
  const authService = createAuthService(httpService, config)

  // 3. Get the Pinia store instance
  const authStore = useAuthStore()

  // 4. Inject the auth service into the store
  authStore.setAuthService(authService)

  // 5. Initialize the store (which will try to fetch the current user) and wait for it to complete
  await authStore.init()

  return {
    authStore,
    authService,
  }
} 